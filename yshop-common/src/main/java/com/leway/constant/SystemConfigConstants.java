package com.leway.constant;

import java.math.BigDecimal;

public class SystemConfigConstants {
    //地址配置
    public final static String API="api";
    public final static String API_URL="api_url";
    public final static String ADMIN_API_URL="admin_api_url";
    public final static String SITE_URL="site_url";
    public final static String UNI_SITE_URL="uni_site_url";
    public final static String TENGXUN_MAP_KEY="tengxun_map_key";
    public final static String FILE_STORE_MODE="file_store_mode";

    //业务相关配置
    // 1. 积分
    // public final static String IMAGEARR="imageArr";
    // public final static String INTERGRAL_FULL="integral_full";
    // public final static String INTERGRAL_MAX="integral_max";
    // public final static String INTERGRAL_RATIO="integral_ratio";

    public final static String ORDER_CANCEL_JOB_TIME="order_cancel_job_time";
    public final static String STORE_BROKERAGE_OPEN="store_brokerage_open";
    public final static String STORE_BROKERAGE_RATIO="store_brokerage_ratio";
    public final static String STORE_BROKERAGE_STATU="store_brokerage_statu";
    public final static String STORE_BROKERAGE_TWO="store_brokerage_two";

    // public final static String STORE_FREE_POSTAGE="store_free_postage";

    public final static String STORE_POSTAGE="store_postage";
    public final static String STORE_SEFL_MENTION="store_self_mention";
    public final static String STORE_USER_MIN_RECHARGE="store_user_min_recharge";
    public final static String USER_EXTRACT_MIN_PRICE="user_extract_min_price";
    public final static String YSHOP_SHOW_RECHARGE = "yshop_show_recharge";

    //微信相关配置
    public final static String WECHAT_APPID="wechat_appid";
    public final static String WECHAT_APPSECRET="wechat_appsecret";
    public final static String WECHAT_AVATAR="wechat_avatar";
    public final static String WECHAT_ENCODE="wechat_encode";
    public final static String WECHAT_ENCODINGAESKEY="wechat_encodingaeskey";
    public final static String WECHAT_ID="wechat_id";
    public final static String WECHAT_NAME="wechat_name";
    public final static String WECHAT_QRCODE="wechat_qrcode";
    public final static String WECHAT_SHARE_IMG="wechat_share_img";
    public final static String WECHAT_SHARE_SYNOPSIS="wechat_share_synopsis";
    public final static String WECHAT_SHARE_TITLE="wechat_share_title";
    public final static String WECHAT_SOURCEID="wechat_sourceid";
    public final static String WECHAT_TOKEN="wechat_token";
    public final static String WECHAT_MA_TOKEN="wechat_ma_token";
    public final static String WECHAT_MA_ENCODINGAESKEY="wechat_ma_encodingaeskey";
    public final static String WECHAT_TYPE="wechat_type";
    public final static String WXAPP_APPID="wxapp_appId";
    public final static String WXAPP_SECRET="wxapp_secret";
    public final static String WXPAY_APPID="wxpay_appId";
    public final static String WXPAY_KEYPATH="wxpay_keyPath";
    public final static String WXPAY_MCHID="wxpay_mchId";
    public final static String WXPAY_MCHKEY="wxpay_mchKey";
    public final static String WX_NATIVE_APP_APPID="wx_native_app_appId";
    public final static String EXP_APPID = "exp_appId";


    //播放状态变化事件，detail = {code}
    public static final String BINDSTATECHANGE = "bindstatechange";

    /**
     * 满额包邮金额(元)
     */
    public static BigDecimal STORE_FREE_POSTAGE_AMOUNT = new BigDecimal("120");

}
