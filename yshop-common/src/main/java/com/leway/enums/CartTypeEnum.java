package com.leway.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 购物车类型
 */
@Getter
@AllArgsConstructor
public enum CartTypeEnum {
    /** 加入购物车 */
    CART_TYPE_ADD_CART_ONLY(0, "加入购物车"),

    /** 直接购买 */
    CART_TYPE_DIRECT_BUY(1, "加入购物车直接购买");

    private Integer value;
    private String desc;

    /**
     * 是否为此状态
     * 
     * @param value
     * @return
     */
    public boolean equalsTo(int value) {
        if (this.getValue() == value) {
            return true;
        }
        return false;
    }
}
