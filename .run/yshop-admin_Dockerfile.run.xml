<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="yshop-admin/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="yshop-admin" />
        <option name="buildCliOptions" value="" />
        <option name="command" value="" />
        <option name="containerName" value="" />
        <option name="entrypoint" value="" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <!--映射端口-->
              <option name="containerPort" value="8001" />
              <option name="hostIp" value="0.0.0.0" />
              <!--容器内部端口-->
              <option name="hostPort" value="8001" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <!--目录挂载 -v 宿主机:容器 -->
        <option name="commandLineOptions" value="-v /home/<USER>/home/<USER>/home/<USER>/logs:/yshop-admin/logs" />
        <option name="sourceFilePath" value="yshop-admin/Dockerfile" />
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
</component>
