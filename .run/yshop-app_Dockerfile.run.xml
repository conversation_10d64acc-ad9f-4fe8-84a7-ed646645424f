<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="yshop-app/Dockerfile" type="docker-deploy" factoryName="dockerfile" server-name="Docker">
    <deployment type="dockerfile">
      <settings>
        <option name="imageTag" value="yshop-app" />
        <option name="buildCliOptions" value="" />
        <option name="command" value="" />
        <option name="containerName" value="" />
        <option name="entrypoint" value="" />
        <option name="portBindings">
          <list>
            <DockerPortBindingImpl>
              <option name="containerPort" value="8008" />
              <option name="hostIp" value="0.0.0.0" />
              <option name="hostPort" value="8008" />
            </DockerPortBindingImpl>
          </list>
        </option>
        <option name="commandLineOptions" value="-v /home/<USER>/home/<USER>/home/<USER>/logs:/yshop-app/logs" />
        <option name="sourceFilePath" value="yshop-app/Dockerfile" />
      </settings>
    </deployment>
    <method v="2" />
  </configuration>
</component>