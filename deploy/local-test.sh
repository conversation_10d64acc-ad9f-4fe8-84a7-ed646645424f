# 现在，你可以通过访问
# http://localhost:9180 来访问Traefik代理的应用，
# 以及通过 http://localhost:9280 来访问Traefik的管理界面。

mvn clean
mvn package -Pdev -Dmaven.test.skip=true

java -jar yshop-admin/target/yshop-admin-3.3.jar --server.port=8001

java -jar yshop-admin/target/yshop-admin-3.3.jar --server.port=8002

docker run -d --name my-traefik \
  -v ./traefik.toml:/etc/traefik/traefik.toml \
  -v ./routes-admin.toml:/etc/traefik/routes.toml \
  -p 9180:80 \
  -p 9280:8080 \
  traefik:v2.10.5

docker run -d --name my-traefik \
  -v ./traefik.toml:/etc/traefik/traefik.toml \
  -p 9180:80 \
  -p 9280:8080 \
  traefik:v2.10.5

docker stop my-traefik
docker rm my-traefik


docker run -d --name my-traefik \
  -v ./traefik.toml:/etc/traefik/traefik.toml \
  -p 9180:80 \
  -p 9280:8080 \
  traefik:v2.10.5

docker logs -f my-traefik