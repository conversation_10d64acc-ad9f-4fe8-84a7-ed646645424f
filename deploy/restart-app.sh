#!/bin/bash

JAR_PATH="/var/www/cable"
JAR_NAME="yshop-app.jar"
FULL_JAR_PATH="${JAR_PATH}/${JAR_NAME}"

MAX_ATTEMPTS=30
INSTANCES_ENDPOINTS=("localhost:8008" "localhost:8009")  # 按照 "服务器:端口" 的格式添加更多实例

# curl -s "http://localhost:8001/health" | grep "UP"
function is_app_healthy() {
    local instance_endpoint=$1
    curl -s "http://$instance_endpoint/api/health" | grep "UP" &>/dev/null
}

function stop_app_on_instance() {
    local instance_endpoint=$1
    local port=${instance_endpoint##*:}
    # local pid=$(lsof -t -i:$port)
    local pid=$(lsof -i :$port | grep LISTEN | awk '{print $2}')
    if [ -z "$pid" ]; then
        echo "No app to stop on $instance_endpoint."
        return 0
    fi

    echo "Stopping app ($pid) on $instance_endpoint..."
    # 为了确保真正的优雅停机，Spring Boot 应用程序需要有足够的时间来关闭所有的活动连接、线程和其他资源。你可以考虑发送 SIGINT 信号或使用 Spring Boot 的 shutdown 端点
    # INT 不行，只能原进程重启，不能实现jar的逻辑更新
    # kill -INT $pid
    kill -TERM $pid
}

function start_app_on_instance() {
    local instance_endpoint=$1
    local port=${instance_endpoint##*:}  # 解析端口
    echo "Starting app on $instance_endpoint using port $port..."

    cd $JAR_PATH
    nohup java -jar "${JAR_NAME}" --spring.profiles.active=prod --server.port="$port" --yshop.workerId="$port"  &>/dev/null &
    sleep 2
}

function log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

function restart_instance() {
    local instance_endpoint=$1

    stop_app_on_instance "$instance_endpoint"
    start_app_on_instance "$instance_endpoint"

    local attempts=0
    while [ $attempts -lt $MAX_ATTEMPTS ]; do
        if is_app_healthy "$instance_endpoint"; then
            echo "App on $instance_endpoint is up and running."
            return 0
        fi
        echo "Waiting for app on $instance_endpoint to start..."

        sleep 2
        ((attempts++))
    done

    echo "Failed to start the app on $instance_endpoint."
    exit 1
}


# Start the restart sequence for each service instance endpoint
for instance_endpoint in "${INSTANCES_ENDPOINTS[@]}"; do
    if is_app_healthy "$instance_endpoint"; then
        restart_instance "$instance_endpoint"
    else
        echo "App on $instance_endpoint is already down. Trying to start..."
        start_app_on_instance "$instance_endpoint"
        attempts=0
        while [ $attempts -lt $MAX_ATTEMPTS ]; do
            if is_app_healthy "$instance_endpoint"; then
                echo "App on $instance_endpoint is now up and running."
                break
            fi
            log_with_timestamp "Waiting for app on $instance_endpoint to start..."
            sleep 2
            ((attempts++))
        done
        if [ $attempts -eq $MAX_ATTEMPTS ]; then
            echo "Failed to start the app on $instance_endpoint."
            exit 1
        fi
    fi
done
