# -*- coding: utf-8 -*-

import os
from fabric.api import task, hosts, cd, run, env, local, sudo
from fabric.contrib.files import exists
from os.path import isfile, expanduser

# Use ssh config if exist
if env.ssh_config_path and isfile(expanduser(env.ssh_config_path)):
    env.use_ssh_config = True

env.adminUser = 'root'
# env.password = '0'


HOST_ALPHA = '************'
REMOTE_WORKSPACE = '/var/www/cable'

HOST = '%s@%s' % (env.adminUser, HOST_ALPHA)
PROD_SERVERS = [HOST_ALPHA]
PROJECT_ROOT = os.path.abspath(
    os.path.join(os.path.join(__file__, os.pardir), os.pardir))


@task
@hosts([HOST_ALPHA])
def online():
    with cd(REMOTE_WORKSPACE):
        # 1. 上传binary
        run_cp('yshop-admin/target/yshop-admin-3.3.jar', 'temp-admin.jar')
        # 2. 停机
        run("ps -aux | grep yshop-admin.jar | grep -v grep | awk '{print $2}' | xargs kill -15 || true",
            warn_only=True)
        # 3. 替换
        run("mv temp-admin.jar yshop-admin.jar")

        # 4. run
        run_bg("java -jar yshop-admin.jar --spring.profiles.active=prod")

        # 1. 上传binary
        run_cp('yshop-app/target/yshop-app-3.3.jar', 'temp-app.jar')
        # 2. 停机
        run("ps -aux | grep yshop-app.jar | grep -v grep | awk '{print $2}' | xargs kill -15 || true",
            warn_only=True)
        # 3. 替换
        run("mv temp-app.jar yshop-app.jar")

        # 4. run
        run_bg("java -jar yshop-app.jar --spring.profiles.active=prod")


@task
@hosts([HOST_ALPHA])
def api_online():
    with cd(REMOTE_WORKSPACE):
        # 1. 上传binary
        run_cp('yshop-app/target/yshop-app-3.3.jar', 'temp-app.jar')
        # 2. 停机
        run("ps -aux | grep yshop-app.jar | grep -v grep | awk '{print $2}' | xargs kill -15 || true",
            warn_only=True)
        # 3. 替换
        run("mv temp-app.jar yshop-app.jar")
        # 4. run
        run_bg("java -jar yshop-app.jar --spring.profiles.active=prod")


@task
@hosts([HOST_ALPHA])
def admin_online():
    with cd(REMOTE_WORKSPACE):
        # 1. 上传binary
        run_cp('yshop-admin/target/yshop-admin-3.3.jar', 'temp-admin.jar')
        # 2. 停机
        run("ps -aux | grep yshop-admin.jar | grep -v grep | awk '{print $2}' | xargs kill -15 || true",
            warn_only=True)
        # 3. 替换
        run("mv temp-admin.jar yshop-admin.jar")

        # 4. run
        run_bg("java -jar yshop-admin.jar --spring.profiles.active=prod")


@task
@hosts([HOST_ALPHA])
def restart():
    with cd(REMOTE_WORKSPACE):
        # 1. 停机
        run("ps -aux | grep yshop-admin | grep -v grep | awk '{print $2}' | xargs kill -15 || true",
            warn_only=True)

        # 2. run
        run_bg("java -jar yshop-admin.jar --spring.profiles.active=prod")

        # 1. 停机
        run("ps -aux | grep yshop-app | grep -v grep | awk '{print $2}' | xargs kill -15 || true",
            warn_only=True)
        # 2. run
        # run("(nohup java -jar yshop-app.jar --spring.profiles.active=prod &) && sleep 1")
        run_bg("java -jar yshop-app.jar --spring.profiles.active=prod")


def run_cp(l_path, r_path):
    local('scp %s %s:%s/%s' % (l_path, HOST, REMOTE_WORKSPACE, r_path))


def run_bg(cmd, before=None, sockname="dtach", use_sudo=False):
    """Run a command in the background using dtach

    :param cmd: The command to run
    :param output_file: The file to send all of the output to.
    :param before: The command to run before the dtach. E.g. exporting
                   environment variable
    :param sockname: The socket name to use for the temp file
    :param use_sudo: Whether or not to use sudo
    """
    if not exists("/usr/bin/dtach"):
        sudo("apt-get install dtach")
    if before:
        cmd = "{}; dtach -n `mktemp -u /tmp/{}.XXXX` {}".format(
            before, sockname, cmd)
    else:
        cmd = "dtach -n `mktemp -u /tmp/{}.XXXX` {}".format(sockname, cmd)
    if use_sudo:
        return sudo(cmd)
    else:
        return run(cmd)


# @task
# @hosts([HOST_ALPHA])
# def kill_hup():
#     with cd(REMOTE_WORKSPACE):
#         # graceful 脱离shell
#         run("ps -aux | grep yshop | grep -v grep | awk '{print $2}' | xargs kill -SIGHUP || true", warn_only=True)

# @task
# @hosts([HOST_ALPHA])
# def tail_remote():
#     with cd(REMOTE_WORKSPACE):
#         run("./tail.sh")
