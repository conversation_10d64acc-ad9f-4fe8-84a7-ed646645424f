tar zxvf /root/deploy/package.tgz -C /root/deploy

mv /root/deploy/yshop-admin/target/yshop-admin-3.3.jar /var/www/cable/yshop-admin.jar

mv /root/deploy/yshop-app/target/yshop-app-3.3.jar /var/www/cable/yshop-app.jar

ps -aux | grep yshop-admin.jar | grep -v grep | awk '{print $2}' | xargs kill -15 || true

ps -aux | grep yshop-app.jar | grep -v grep | awk '{print $2}' | xargs kill -15 || true

cd /var/www/cable

dtach -n `mktemp -u /tmp/dtach.XXXX` java -jar yshop-app.jar --spring.profiles.active=prod

dtach -n `mktemp -u /tmp/dtach.XXXX` java -jar yshop-admin.jar --spring.profiles.active=prod