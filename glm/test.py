import zhipuai

zhipuai.api_key = "9e675bdfc23e3c854664e8e2d87c3515.zbHHeURLFrAnFFYP"

CHATGLM_LITE = "chatglm_lite"
CHATGLM_STD = "chatglm_std"
CHATGLM_PRO = "chatglm_pro"


def sync_call(prompt, model=CHATGLM_LITE):
    return zhipuai.model_api.invoke(
        model=model,
        prompt=prompt,
        top_p=0.7,
        temperature=0.9,
    )


def async_call(prompt, model=CHATGLM_LITE):
    zhipuai.model_api.async_invoke(
        model=model,
        prompt=prompt,
        top_p=0.7,
        temperature=0.9,
    )


def sse_call(prompt, model=CHATGLM_LITE):
    zhipuai.model_api.sse_invoke(
        model=model,
        prompt=prompt,
        top_p=0.7,
        temperature=0.9,
    )


def gen_prompt(simple_prompt):
    prompt = [
        {
            "role": "adminUser",
            "content": """作为一名'幻觉贸易'的智能客服，请你记住【】中的知识库内容，每一行一个知识，并根据知识库内容用简洁和专业的来回答用户问题。如果无法从中得到答案，请说 “根据已知信息无法回答该问题” 或 “没有提供足够的相关信息”，不允许在答案中添加编造成分，答案请使用中文。
            用户问题：我要退货
            【修改订单收货地址，请按照xxxxx操作，下单后一小时之内可以操作，快递发出后再修改不会响应。
            退货在货品发出后，需要承担15元退货物流费用。】""",
            # "content": "你好",
        },
        {"role": "assistant", "content": "我是幻觉贸易的智能客服，我知道怎么做互联网电商线上咨询。"},
        {"role": "adminUser", "content": simple_prompt},
    ]
    return prompt


def parse_output(resp):
    code = resp["code"]
    msg = resp["msg"]
    if code != 200:
        print(code, msg)
        print(resp)
        return

    data = resp["data"]
    request_id = data["request_id"]
    task_id = data["task_id"]
    task_satatus = data["task_status"]
    usage = data["usage"]
    total_tokens = usage["total_tokens"]
    choice = data["choices"][0]
    content = choice["content"]
    print(content)
    print("花费:", total_tokens, "tokens")


if __name__ == "__main__":
    # prompt = [{"role": "adminUser", "content": "你好"}]
    prompt = gen_prompt("我要退货")
    # print(prompt)
    # prompt = [
    #     {"role": "adminUser", "content": "你好"},
    #     {"role": "assistant", "content": "我是人工智能助手"},
    #     {"role": "adminUser", "content": "你叫什么名字"},
    #     {"role": "assistant", "content": "我叫chatGLM"},
    #     {"role": "adminUser", "content": "你都可以做些什么事"},
    # ]

    response = sync_call(prompt, model=CHATGLM_STD)
    parse_output(response)
    # async_call(prompt)
    # sse_call(prompt)
