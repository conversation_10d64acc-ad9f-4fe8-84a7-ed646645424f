# Server-Sent Events
from zhipuai import ZhipuAI

client = ZhipuAI(
    api_key="9e675bdfc23e3c854664e8e2d87c3515.zbHHeURLFrAnFFYP"
)  # 请填写您自己的APIKey
response = client.chat.completions.create(
    model="chatglm_std",  # 填写需要调用的模型名称
    messages=[
        # {"role": "system", "content": "你是一个人工智能助手，你叫叫chatGLM"},
        {"role": "user", "content": "你好！你叫什么名字"},
        {"role": "assistant", "content": "我叫chatGLM"},
        {"role": "user", "content": "你都可以做些什么事"},
    ],
    stream=True,
)
for chunk in response:
    print(chunk.choices[0].delta.content, end='')
