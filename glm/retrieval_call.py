from zhipuai import ZhipuAI

KNOWLEDGE_ID = "1717153939669467136"

client = ZhipuAI(api_key="9e675bdfc23e3c854664e8e2d87c3515.zbHHeURLFrAnFFYP")
response = client.chat.completions.create(
    model="glm-4",  # 填写需要调用的模型名称
    messages=[
        {"role": "user", "content": "你好！我要退货！"},
        {"role": "assistant", "content": "根据用户协议，在所购商品保持购买时的原始状况、并符合二次销售条件的情况下，您可以在收到商品之日至第7天内进行退货退款。退货时，请确保商品完好，无划痕、磨损、磕碰、使用或拆卸痕迹，商品外包装完整，配件、附件齐全。退货申请仅能发起2次，超出时间和次数限制将无法受理。退货流程请通过“我的”页面点击“全部订单”进行操作。若不符合退货要求或遇其他特殊情况，请联系人工客服。"},
        {"role": "user", "content": "退货需要多久时间？"},
        {"role": "assistant", "content": "退款将在15个工作日内通过原支付方式无息退回。"},
        {"role": "user", "content": "周杰伦的成名作是什么？"},
    ],
    tools=[
        {
            "type": "retrieval",
            "retrieval": {
                "knowledge_id": KNOWLEDGE_ID,
                "prompt_template": """从文档

                    {{knowledge}}

                    中找问题

                    {{question}}

                    的答案，找到答案就仅使用文档语句回答，不要用‘根据文档内容这样的话，应该使用根据用户协议，……’，找不到答案就回答‘抱歉，无法回答您的问题，请联系人工客服‘。
                    不要复述问题，直接开始回答；
                    """,
            },
        }
    ],
    stream=True,
)
for chunk in response:
    print(chunk.choices[0].delta.content, end='')
