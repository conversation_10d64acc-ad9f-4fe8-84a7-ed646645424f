<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>yshop</artifactId>
        <groupId>com.leway</groupId>
        <version>3.3</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>le-scripts</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.leway</groupId>
            <artifactId>yshop-common</artifactId>
            <version>3.3</version>
        </dependency>
        <dependency>
            <groupId>com.leway</groupId>
            <artifactId>yshop-mall</artifactId>
            <version>3.3</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>1.6.0</version> <!-- 使用最新的版本号 -->
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.getsentry.raven/raven-logback -->
        <dependency>
            <groupId>com.getsentry.raven</groupId>
            <artifactId>raven-logback</artifactId>
            <version>8.0.3</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.getsentry.raven/raven -->
        <dependency>
            <groupId>com.getsentry.raven</groupId>
            <artifactId>raven</artifactId>
            <version>8.0.3</version>
        </dependency>

    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profile.active>dev</profile.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <profile.active>prod</profile.active>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profile.active>test</profile.active>
            </properties>
        </profile>
    </profiles>

    <build>
        <!--先排除所有的配置文件-->
        <!--引入所需环境的配置文件-->
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>config/application-*.yml</exclude>
                    <exclude>logback-spring-*.xml</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>config/application.yml</include>
                    <include>config/application-${profile.active}.yml</include>
                    <include>logback-spring.xml</include>
                    <include>logback-spring-${profile.active}.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.leway.MyCliApp</mainClass>
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>