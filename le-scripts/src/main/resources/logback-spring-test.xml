<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <!-- 彩色日志 -->
    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />
    <conversionRule conversionWord="wex" converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter" />
    <conversionRule conversionWord="wEx" converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter" />
    <contextName>yshop</contextName>
    <property name="log.charset" value="utf-8" />
    <property name="log.pattern" value="${log.pattern:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%thread]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"  />
    <property name="log.path" value="logs/yshop-admin"/>

    <!--输出到控制台-->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
            <charset>${log.charset}</charset>
        </encoder>
    </appender>

    <!-- Log file error output -->
    <appender name="error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM}/error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>ERROR</level>
        </filter>
    </appender>

    <!-- Log file info output -->
    <appender name="info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/%d{yyyy-MM}/info.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date [%thread] %-5level [%logger{50}] %file:%line - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!-- 屏蔽日志 -->
    <logger name="org.springframework.data.repository.config.RepositoryConfigurationDelegate" level="OFF" additivity="false"></logger>
    <logger name="org.springframework.context.support.PostProcessorRegistrationDelegate" level="OFF" additivity="false"></logger>
    <logger name="springfox.documentation.spring.web.readers.parameter.ParameterDataTypeReader" level="OFF" additivity="false"></logger>
    <logger name="springfox.documentation.spring.web.PropertySourcedRequestMappingHandlerMapping" level="OFF" additivity="false"></logger>
    <logger name="org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor" level="OFF" additivity="false"></logger>
    <logger name="springfox.documentation.spring.web.plugins.DocumentationPluginsBootstrapper" level="OFF" additivity="false"></logger>
    <logger name="springfox.documentation.spring.web.readers.operation.CachingOperationNameGenerator" level="OFF" additivity="false"></logger>

    <logger name="org.springframework.test.context.support.AbstractContextLoader" level="OFF" additivity="false"></logger>
    <logger name="org.springframework.test.context.BootstrapUtils" level="OFF" additivity="false"></logger>
    <logger name="org.springframework.test.annotation.ProfileValueUtils" level="OFF" additivity="false"></logger>
    <logger name="org.springframework.test.context.support.AbstractDirtiesContextTestExecutionListener" level="OFF" additivity="false"></logger>
    <logger name="org.springframework.test.context.support.DependencyInjectionTestExecutionListener" level="OFF" additivity="false"></logger>
    <!--  屏蔽定时任务 -->
    <logger name="org.quartz" level="OFF" additivity="false"></logger>

    <!--普通日志输出到控制台-->
    <root level="info">
        <appender-ref ref="console" />
        <appender-ref ref="error"/>
        <appender-ref ref="info"/>
    </root>

</configuration>
