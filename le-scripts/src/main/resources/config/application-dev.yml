#配置数据源
spring:
  debug: true
  devtools:
    restart:
      enabled: false
      use-default-classloader: true
      exclude: leway/scripts/**, another/package/**
  application:
    name: cable-cli
  datasource:
    druid:
      type: com.alibaba.druid.pool.DruidDataSource
      driverClassName: com.mysql.cj.jdbc.Driver
      url: ***************************************************************************************************************************************************************************************************************************************************
      username: eye
      password: sauron

      # 初始连接数
      initialSize: 5
      # 最小连接池数量
      minIdle: 10
      # 最大连接池数量
      maxActive: 200
      # 配置获取连接等待超时的时间
      maxWait: 8000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      maxEvictableIdleTimeMillis: 900000
      # 配置检测连接是否有效
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        # 设置白名单，不填则允许所有访问
        allow:
        url-pattern: /druid/*
        # 控制台管理用户名和密码
        login-username: admin
        login-password: 123456
      filter:
        stat:
          enabled: true
          # 慢SQL记录
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  redis:
    #数据库索引
    database: 2
    host: 127.0.0.1
    port: 6379
    password:
    #连接超时时间
    timeout: 5000


# 是否限制单用户登录
single:
  login: false


#jwt
jwt:
  header: Authorization
  # 令牌前缀
  token-start-with: Bearer
  secret: k09BQnaF
  # 必须使用最少88位的Base64对该令牌进行编码
  base64-secret: ZmQ0ZGI5NjQ0MDQwY2I4MjMxY2Y3ZmI3MjdhN2ZmMjNhODViOTg1ZGE0NTBjMGM4NDA5NzYxMjdjOWMwYWRmZTBlZjlhNGY3ZTg4Y2U3YTE1ODVkZDU5Y2Y3OGYwZWE1NzUzNWQ2YjFjZDc0NGMxZWU2MmQ3MjY1NzJmNTE0MzI=
  # 令牌过期时间 此处单位/毫秒 ，默认4小时，可在此网站生成 https://www.convertworld.com/zh-hans/time/milliseconds.html
  # token-validity-in-seconds: 14400000
  # 7天
  token-validity-in-seconds: 604800000
  # 在线用户key
  online-key: online-token
  # 验证码
  code-key: code-key


#是否允许生成代码，生产环境设置为false
generator:
  enabled: false

#是否开启 swagger-ui
swagger:
  enabled: false

# 文件存储路径
file:
  path: /Users/<USER>/workspace/shop/temple/backend/upload/file/
  avatar: /Users/<USER>/workspace/shop/temple/backend/upload/avatar/
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5


#ERP配置
mybatis-plus:
  mapper-locations: classpath:./mapper_xml/*.xml

#租户对应的角色id 租户role
manage:
  roleId: 10
#租户允许创建的用户数
tenant:
  userNumLimit: 1000000

#租户允许试用的天数
  tryDayLimit: 3000

# 日志配置
logging:
  config: classpath:logback-spring-dev.xml
  level:
    web: info
    com.leway: debug
    org.springframework: info

sentry:
  # dsn: https://6cd7cd9c0903413c8dcfc2e5c3df0dbd:<EMAIL>/7
  dsn: ""
