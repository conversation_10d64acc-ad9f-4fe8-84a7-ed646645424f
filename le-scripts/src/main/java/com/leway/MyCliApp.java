package com.leway;

import com.binarywang.spring.starter.wxjava.miniapp.config.WxMaAutoConfiguration;
import com.leway.utils.SpringContextHolder;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.web.servlet.error.DefaultErrorAttributes;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

@Configuration
@EnableAsync
@EnableCaching
@SpringBootApplication(exclude = { WxMaAutoConfiguration.class })
@EnableTransactionManagement // 支持事务
// @MapperScan(basePackages = { "com.leway.modules.*.service.mapper",
// "com.leway.config",
// "com.leway.modules.erp.datasource.mappers" })
@MapperScan(basePackages = { "com.leway.modules.*.service.mapper", "com.leway.config",
})
public class MyCliApp {
    public static void main(String[] args) {
        SpringApplication app = new SpringApplication(MyCliApp.class);
        app.setWebApplicationType(WebApplicationType.NONE);
        ConfigurableApplicationContext ctx = app.run(args);
        ctx.close();
    }

    @Bean
    @ConditionalOnMissingBean(name = "errorAttributes")
    public DefaultErrorAttributes errorAttributes() {
        return new DefaultErrorAttributes();
    }

    /**
     * 默认不创建，没有的时候才创建
     * 
     * @ConditionalOnMissingBean 是Spring
     *                           Boot的一个条件注解，当Spring上下文中不存在该类的Bean时，就会创建这个Bean。它通常与 @Bean
     *                           注解一起用在配置类中，用来定义默认的Bean，只有在没有其他符合条件的Bean时，才会实例化这个Bean。
     * @return
     */
    @Bean
    @ConditionalOnMissingBean(name = "requestMappingHandlerMapping")
    public RequestMappingHandlerMapping requestMappingHandlerMapping() {
        return new RequestMappingHandlerMapping();
    }

    @Bean
    public SpringContextHolder springContextHolder() {
        return new SpringContextHolder();
    }

}
