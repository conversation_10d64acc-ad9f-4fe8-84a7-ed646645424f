/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>
 * 注意：
 * 本软件为************开发研制，未经购买不得使用
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.leway.common.config;

import com.baomidou.mybatisplus.LeSQLInjector;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.core.injector.ISqlInjector;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * MybatisPlus配置
 *
 */
@Configuration(proxyBeanMethods = false)
public class MybatisPlusConfig {

    /**
     * mybatis-plus分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    @Bean
    public ISqlInjector iSqlInjector() {
        return new LeSQLInjector();
    }

}
