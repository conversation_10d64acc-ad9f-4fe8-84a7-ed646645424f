package com.leway.scripts;

import cn.binarywang.wx.miniapp.bean.openapi.WxMiniGetApiQuotaResult;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.getsentry.raven.DefaultRavenFactory;
import com.getsentry.raven.Raven;
import com.getsentry.raven.dsn.Dsn;
import com.github.binarywang.wxpay.bean.result.WxPayFundFlowResult;
import com.leway.constant.ShopConstants;
import com.leway.domain.PageResult;
import com.leway.enums.OrderInfoEnum;
import com.leway.modules.mp.service.MaOpenApi;
import com.leway.modules.mp.service.WeAppCommonService;
import com.leway.modules.mp.service.WeiXinOrderShippingService;
import com.leway.modules.mp.service.WeiXinSubscribeService;
import com.leway.modules.mp.service.WeixinPayService;
import com.leway.modules.order.domain.Express;
import com.leway.modules.order.domain.Order;
import com.leway.modules.order.service.ExpressService;
import com.leway.modules.order.service.StoreOrderService;
import com.leway.modules.order.service.dto.YxExpressQueryCriteria;
import com.leway.modules.order.service.dto.YxStoreOrderDto;
import com.leway.modules.order.service.dto.YxStoreOrderQueryCriteria;
import com.leway.modules.product.domain.Product;
import com.leway.modules.product.service.ProductRelationService;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.product.service.dto.ProductRelationDto;
import com.leway.modules.product.service.dto.YxStoreProductRelationQueryCriteria;
import com.leway.modules.tools.express.KDBirdExpressService;
import com.leway.modules.tools.express.config.ExpressAutoConfiguration;
import com.leway.modules.tools.express.vo.Address;
import com.leway.modules.tools.express.vo.AddressParser;
import com.leway.modules.tools.express.vo.ExpressInfo;
import com.leway.modules.tools.express.vo.KDBirdAddressInfo;
import com.leway.utils.DateUtils;
import com.leway.utils.RedisUtil;
import com.leway.utils.RedisUtils;
import com.leway.utils.StringUtils;
import com.leway.utils.spring.SortPage;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.cli.*;
import org.apache.poi.hpsf.Array;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.context.annotation.Conditional;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.CountDownLatch;

import com.leway.modules.user.param.AddressParam;
import com.leway.modules.user.service.YxUserAddressService;

@Slf4j
// 需要在启动参数中加入 --task=dataMigration
// java -jar le-scripts/target/le-scripts-3.3.jar --task=sync2wechat
@Conditional(MyScriptAppRunner.MyConditimaton.class)
@Component
public class MyScriptAppRunner implements ApplicationRunner, ApplicationContextAware {
    private static final String TASK_DATA_MIGRATION = "dataMigration";
    private static final String TASK_SYNC2WECHAT = "sync2wechat";
    private static final String TASK_TEST = "test";
    private static final String TASK_CHECK_LIMIT = "check_limit";
    private static final String TASK_CHECK_DELIVERY = "check_delivery";
    private static final String TASK_REFUND = "refund";
    private static final String TASK_SYNC_INVENTORY = "sync_inventory";
    private static final String TASK_TEST_SEND_SUB_MSG = "sub_msg_send";
    private static final String TASK_DOWNLOAD_BILL = "download_bill";
    private static final String TASK_PARALLEL_ADDRESS = "parallel_address";

    public static class MyConditimaton implements Condition {
        @Override
        public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
            String[] args = context.getBeanFactory().getBean(ApplicationArguments.class).getSourceArgs();

            String c1 = String.format("--task=%s", TASK_DATA_MIGRATION);
            String c2 = String.format("--task=%s", TASK_SYNC2WECHAT);
            String c3 = String.format("--task=%s", TASK_TEST);
            String c4 = String.format("--task=%s", TASK_CHECK_LIMIT);
            String c5 = String.format("--task=%s", TASK_CHECK_DELIVERY);
            // 强制微信支付退款
            // java -jar le-scripts.jar --spring.profiles.active=prod --task=refund -o
            // 1769648881215254528 -a 160
            String c6 = String.format("--task=%s", TASK_REFUND);
            String c7 = String.format("--task=%s", TASK_SYNC_INVENTORY);
            String c8 = String.format("--task=%s", TASK_TEST_SEND_SUB_MSG);
            String c9 = String.format("--task=%s", TASK_DOWNLOAD_BILL);
            String c10 = String.format("--task=%s", TASK_PARALLEL_ADDRESS);

            // log.info("args: {}", String.join(", ", Arrays.asList(args)));

            return Arrays.asList(args).contains(c1) || Arrays.asList(args).contains(c2)
                    || Arrays.asList(args).contains(c3)
                    || Arrays.asList(args).contains(c4)
                    || Arrays.asList(args).contains(c5)
                    || Arrays.asList(args).contains(c6)
                    || Arrays.asList(args).contains(c7)
                    || Arrays.asList(args).contains(c8)
                    || Arrays.asList(args).contains(c9)
                    || Arrays.asList(args).contains(c10);
        }
    }

    private ApplicationContext context;

    @Override
    public void setApplicationContext(ApplicationContext context) throws BeansException {
        this.context = context;
    }

    @Value("${sentry.dsn}")
    private String dsnUrl;

    /**
     * 测试服务
     */
    @Autowired
    private ScriptTestService scriptTestService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private WeixinPayService weixinPayService;

    @Autowired
    private WeiXinSubscribeService weiXinSubscribeService;

    @Autowired
    private ProductService storeProductService;

    private void sendSentry(Throwable e) {
        log.info("sendSentry dsn: {}", dsnUrl);

        Dsn dsn = new Dsn(dsnUrl);
        Raven raven = (new DefaultRavenFactory()).createRavenInstance(dsn);
        Throwable throwable = new Throwable(e.getMessage(), e.getCause());
        throwable.setStackTrace(e.getStackTrace());
        raven.sendException(throwable);
    }

    public void testSentry() {
        try {
            int a = 1 / 0;
        } catch (Exception e) {
            e.printStackTrace();
            sendSentry(e);
        }
    }

    /**
     * 同步订单到微信
     */
    private void syncOrder2Wechat() {
        storeOrderService.syncFinishOrder2Wechat();
    }

    private void testInterrupt() {
        kdBirdExpressService.interruptJDEOrder("JDVA24196038772");
    }

    private void parseKdBirdRoutePost() {
        String body = "RequestData=%7B%22PushTime%22%3A%222024-04-12+12%3A55%3A37%22%2C%22EBusinessID%22%3A%221779053%22%2C%22Data%22%3A%5B%7B%22StateEx%22%3A%222%22%2C%22LogisticCode%22%3A%22JDVA26292009453%22%2C%22ShipperCode%22%3A%22JD%22%2C%22Traces%22%3A%5B%7B%22Action%22%3A%2210%22%2C%22AcceptStation%22%3A%22%E6%8F%BD%E6%94%B6%E4%BB%BB%E5%8A%A1%E5%B7%B2%E5%88%86%E9%85%8D%E7%BB%99%E5%AE%8B%E8%B1%AA%E6%9D%B0%E3%80%82%22%2C%22AcceptTime%22%3A%222024-04-10+14%3A31%3A42%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%221%22%2C%22AcceptStation%22%3A%22%E4%BA%AC%E4%B8%9C%E5%BF%AB%E9%80%92+%E5%B7%B2%E6%94%B6%E5%8F%96%E5%BF%AB%E4%BB%B6%22%2C%22AcceptTime%22%3A%222024-04-10+16%3A16%3A11%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%221%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E5%B7%B2%E7%94%B1%E3%80%90%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%A4%A7%E7%9F%B3%E9%97%A8%E8%90%A5%E4%B8%9A%E9%83%A8%E3%80%91%E6%8F%BD%E6%94%B6%E5%AE%8C%E6%88%90%22%2C%22AcceptTime%22%3A%222024-04-10+16%3A16%3A11%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E5%B7%B2%E5%8F%91%E8%BD%A6%22%2C%22AcceptTime%22%3A%222024-04-10+20%3A39%3A02%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E5%B7%B2%E5%88%B0%E8%BE%BE%E3%80%90%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E3%80%91%22%2C%22AcceptTime%22%3A%222024-04-10+21%3A56%3A51%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E5%9C%A8%E3%80%90%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E3%80%91%E5%88%86%E6%8B%A3%E5%AE%8C%E6%88%90%22%2C%22AcceptTime%22%3A%222024-04-10+21%3A57%3A00%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E5%B7%B2%E6%94%B6%E7%AE%B1%EF%BC%8C%E7%AE%B1%E5%8F%B7BC1002240410260718875409%22%2C%22AcceptTime%22%3A%222024-04-10+23%3A17%3A15%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E7%94%B1%E3%80%90%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E3%80%91%E5%87%86%E5%A4%87%E5%8F%91%E5%BE%80%E3%80%90%E5%B9%BF%E5%B7%9E%E5%8D%9A%E5%B1%95%E6%95%A3%E8%B4%A7%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E3%80%91%22%2C%22AcceptTime%22%3A%222024-04-10+23%3A19%3A55%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E5%B7%B2%E5%8F%91%E8%BD%A6%22%2C%22AcceptTime%22%3A%222024-04-11+02%3A55%3A27%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E7%9F%B3%E5%AE%B6%E5%BA%84%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E5%B9%BF%E5%B7%9E%E5%8D%9A%E5%B1%95%E6%95%A3%E8%B4%A7%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E5%B7%B2%E6%94%B6%E7%AE%B1%EF%BC%8C%E7%AE%B1%E5%8F%B7BC1002240410260718875409%22%2C%22AcceptTime%22%3A%222024-04-12+08%3A27%3A42%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E5%B7%B2%E5%88%B0%E8%BE%BE%E3%80%90%E5%B9%BF%E5%B7%9E%E5%8D%9A%E5%B1%95%E6%95%A3%E8%B4%A7%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E3%80%91%22%2C%22AcceptTime%22%3A%222024-04-12+09%3A00%3A45%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E5%9C%A8%E3%80%90%E5%B9%BF%E5%B7%9E%E5%8D%9A%E5%B1%95%E6%95%A3%E8%B4%A7%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E3%80%91%E5%88%86%E6%8B%A3%E5%AE%8C%E6%88%90%22%2C%22AcceptTime%22%3A%222024-04-12+09%3A01%3A59%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E7%94%B1%E3%80%90%E5%B9%BF%E5%B7%9E%E5%8D%9A%E5%B1%95%E6%95%A3%E8%B4%A7%E5%88%86%E6%8B%A3%E4%B8%AD%E5%BF%83%E3%80%91%E5%87%86%E5%A4%87%E5%8F%91%E5%BE%80%E3%80%90%E5%B9%BF%E5%B7%9E%E4%BA%AC%E6%BA%AA%E8%90%A5%E4%B8%9A%E9%83%A8%E3%80%91%22%2C%22AcceptTime%22%3A%222024-04-12+09%3A24%3A45%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%7D%2C%7B%22Action%22%3A%222%22%2C%22AcceptStation%22%3A%22%E6%82%A8%E7%9A%84%E5%BF%AB%E4%BB%B6%E5%B7%B2%E5%8F%91%E8%BD%A6%22%2C%22AcceptTime%22%3A%222024-04-12+12%3A51%3A11%22%2C%22Remark%22%3A%22%22%2C%22Location%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%7D%5D%2C%22State%22%3A%222%22%2C%22OrderCode%22%3A%221777925316048625664%22%2C%22EBusinessID%22%3A%221779053%22%2C%22Success%22%3Atrue%2C%22Location%22%3A%22%E5%B9%BF%E5%B7%9E%E5%B8%82%22%7D%5D%2C%22Count%22%3A%221%22%7D&DataSign=MzkwODJiZWU2OWI2MDkzN2FmYzk0YzU2NGMwYTYxZWM%3D&RequestType=102";
        try {
            // URL解码
            String encodedUrl = URLDecoder.decode(body, StandardCharsets.UTF_8.name());
            Map<String, String> queryParams = new HashMap<>();

            // Decoding the encoded URL string
            String decodedUrl = URLDecoder.decode(encodedUrl, StandardCharsets.UTF_8.name());

            // Split the parameters and fill the map
            String[] pairs = decodedUrl.split("&");
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                String key = keyValue[0];
                String value = keyValue.length > 1 ? keyValue[1] : ""; // Handle the case of empty value
                queryParams.put(key, value);
                log.info("key: {}, value: {}", key, value);

            }

            String requestType = queryParams.get("RequestType");
            String dataSign = queryParams.get("DataSign");
            String requestData = queryParams.get("RequestData");
            log.info("requestType: {}, dataSign: {}, requestData: {}", requestType, dataSign, requestData);

            if (StringUtils.equals("102", requestType)) {
                // 轨迹推送
                // https://www.yuque.com/kdnjishuzhichi/dfcrg1/meiubz#DN913

                // 从解码的字符串中提取JSON部分

                // 解析JSON字符串
                JSONObject jsonObject = JSONObject.parseObject(requestData);
                // 普通物流状态：
                // 0-暂无轨迹信息
                // 1-已揽收
                // 2-在途中
                // 3-签收
                // 4-问题件
                // 5-转寄
                // 6-清关
                JSONArray datas = jsonObject.getJSONArray("Data");
                // String count = jsonObject.getString("Count");
                for (int i = 0; i < datas.size(); i++) {
                    JSONObject data = datas.getJSONObject(i);
                    String state = data.getString("State");
                    String OrderCode = data.getString("OrderCode");
                    log.info("state: {}, OrderCode: {} {} {}", state, OrderCode, data.getString("ShipperCode"),

                            data.getString("LogisticCode"));
                }

                // 从此处开始操作JSON对象...
                // System.out.println(jsonObject.toJSONString()); // 打印格式化后的JSON字符串
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Autowired
    private WeiXinOrderShippingService weiXinOrderShippingService;

    private void testGetWechatOrderState() {
        String outTradeNo = "1777925316048625664";
        JSONObject wechatOrder = weiXinOrderShippingService.queryOrderShippingStatus(null, outTradeNo);
        if (wechatOrder == null) {
            log.error("微信订单状态查询异常，订单号: {}", outTradeNo);

            return;
        }

        int orderState = wechatOrder.getIntValue("order_state");

        log.info("微信订单状态: {}", orderState);

        log.info("wechat order", wechatOrder);

    }

    private void testQuota() {
        MaOpenApi api = new MaOpenApi();

        String cgiPath = "/cgi-bin/message/subscribe/send";
        try {
            WxMiniGetApiQuotaResult result = api.getApiQuota(cgiPath);
            log.info("result: {}", result);
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
    }

    private void testTaiwan() {
        // 张力夫 932626175
        // 台湾省 苗栗县 头份市 中华路1101巷9号
        // 护照/身份证： K122630048
        KDBirdExpressService kuaidiBirdExpressService = ExpressAutoConfiguration.expressService();

        ExpressInfo expressInfo = kuaidiBirdExpressService.getExpressInfo("1782291042440097792",
                "SF", "SF1445852426791", "4704");
        log.info("expressInfo: {}", expressInfo);

        expressInfo = kuaidiBirdExpressService.getExpressInfo("1782291042440097792",
                "SF", "SF1445852426791", "6175");
        log.info("expressInfo: {}", expressInfo);

    }

    private void testAddress() {
        KDBirdAddressInfo sender = new KDBirdAddressInfo(
                "杜米", "18601296102", "北京市", "北京市", "朝阳区", "安苑北里20号楼17门501");
        KDBirdAddressInfo receiver = new KDBirdAddressInfo(
                "ssss", "18913105810", "江苏省", "苏州市", "吴中区", "苏州工业园区唯康路9号颐和花园11-602");
        boolean reachable = kdBirdExpressService.queryAddress(
                receiver,
                sender,
                KDBirdExpressService.JD_CODE);
        log.info("reachable: {}", reachable);

        try {
            reachable = kdBirdExpressService.queryAddress(receiver, sender, KDBirdExpressService.SF_CODE);
            log.info("reachable:{}", reachable);
        } catch (Exception e) {
            e.printStackTrace();
        }

        receiver = new KDBirdAddressInfo(
                "乐",
                "18911067180",
                "山东省", "威海市", "环翠区", "张村镇昆仑路126号东北一门");
        try {
            reachable = kdBirdExpressService.queryAddress(receiver, sender, KDBirdExpressService.SF_CODE);
            log.info("reachable:{}", reachable);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // // 计算处理时间
        // Long start = System.currentTimeMillis();
        //
        // Address parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
        // log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
        // Long end = System.currentTimeMillis();
        // log.info("parseAddress处理时间: {}ms", end - start);
        //
        // // 河北省 秦皇岛市 海港区 海港镇 建国路与建业街交叉口燕鑫花苑南区7栋2单元102室
        // start = System.currentTimeMillis();
        // receiver = new KDBirdAddressInfo(
        // "ssss", "18913105810", "河北省", "秦皇岛市", "海港区", "海港镇
        // 建国路与建业街交叉口燕鑫花苑南区7栋2单元102室");
        // reachable = kdBirdExpressService.queryAddress(receiver, sender,
        // KDBirdExpressService.JD_CODE);
        // log.info("reachable: {}", reachable);
        // end = System.currentTimeMillis();
        // log.info("queryAddress处理时间: {}ms", end - start);
        //
        // List<String> testList = new ArrayList<>();
        //// for (int i = 0; i < 100; i++) {
        //// testList.add("河北省 秦皇岛市 海港区 海港镇 建国路与建业街交叉口燕鑫花苑南区7栋2单元102室");
        //// }
        //
        //// start = System.currentTimeMillis();
        //// for (int i = 0; i < 100; i++) {
        //// reachable = kdBirdExpressService.queryAddress(receiver, sender,
        // KDBirdExpressService.JD_CODE);
        //// }
        //// end = System.currentTimeMillis();
        //// log.info("for 处理时间: {}ms", end - start);
        //
        // start = System.currentTimeMillis();
        // final KDBirdAddressInfo finalReceiver = receiver;
        // testList.parallelStream().forEach(x -> {
        // kdBirdExpressService.queryAddress(finalReceiver, sender,
        // KDBirdExpressService.JD_CODE);
        // });
        // end = System.currentTimeMillis();
        // log.info("parallelStream 处理时间: {}ms", end - start);
        //
        // // 2024-05-08 14:21:22.479 INFO 15009 --- [main]
        // // com.leway.scripts.MyScriptAppRunner : parseAddress处理时间: 102ms
        // // 2024-05-08 14:21:22.557 INFO 15009 --- [main]
        // // com.leway.scripts.MyScriptAppRunner : reachable: true
        // // 2024-05-08 14:21:22.558 INFO 15009 --- [main]
        // // com.leway.scripts.MyScriptAppRunner : queryAddress处理时间: 79ms
        // // 2024-05-08 14:21:31.892 INFO 15009 --- [main]
        // // com.leway.scripts.MyScriptAppRunner : for 处理时间: 9334ms
        // // 2024-05-08 14:21:32.876 INFO 15009 --- [main]
        // // com.leway.scripts.MyScriptAppRunner : parallelStream 处理时间: 983ms
        //
        // // 河北省 石家庄市 灵寿县 灵寿镇 人民西路43号河北省灵寿县教育局
        // receiver = new KDBirdAddressInfo(
        // "ssss", "18913105810", "河北省", "石家庄市", "灵寿县", "灵寿镇 人民西路43号河北省灵寿县教育局");
        // reachable = kdBirdExpressService.queryAddress(receiver, sender,
        // KDBirdExpressService.JD_CODE);
        // log.info("reachable: {}", reachable);
        //
        // parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
        // log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
        //
        // // 新疆维吾尔自治区 直辖县 铁门关市 兵团兴新职业技术学院
        // receiver = new KDBirdAddressInfo(
        // "ssss", "18913105810", "新疆维吾尔自治区", "直辖县", "铁门关市", "兵团兴新职业技术学院");
        // reachable = kdBirdExpressService.queryAddress(receiver, sender,
        // KDBirdExpressService.JD_CODE);
        // log.info("reachable: {}", reachable);
        //
        // parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
        // log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
        //
        // Address addressVo = AddressParser.simpleParse(receiver.desc());
        // log.info("addressVo: {}", addressVo);
        // if (StringUtils.isNotEqual(addressVo.getCity(), parsedAddress.getCity())) {
        // log.info("智能解析异常: {} 不等于 {}", parsedAddress.getCity(), addressVo.getCity());
        // }
        //
        // // 海南省 省直辖县级行政区域 临高县 解放里商业宿舍30号
        // receiver = new KDBirdAddressInfo(
        // "ssss", "18913105810", "海南省", "省直辖县级行政区域", "临高县", "解放里商业宿舍30号");
        // reachable = kdBirdExpressService.queryAddress(receiver, sender,
        // KDBirdExpressService.SF_CODE);
        // log.info("reachable: {}", reachable);
        //
        // parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
        // log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
        //
        // addressVo = AddressParser.simpleParse(receiver.desc());
        // log.info("addressVo: {}", addressVo);
        // if (StringUtils.isNotEqual(addressVo.getCity(), parsedAddress.getCity())) {
        // log.info("智能解析异常: {} 不等于 {}", parsedAddress.getCity(), addressVo.getCity());
        // }
        //
        // // 福建省三明市明溪县明珠御苑A6幢603室
        // receiver = new KDBirdAddressInfo(
        // "ssss", "18913105810", "福建省", "三明市", "明溪县", "明珠御苑A6幢603室");
        // reachable = kdBirdExpressService.queryAddress(receiver, sender,
        // KDBirdExpressService.SF_CODE);
        // log.info("reachable: {}", reachable);
        //
        // parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
        // log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
        //
        // addressVo = AddressParser.simpleParse(receiver.desc());
        // log.info("addressVo: {}", addressVo);
        // if (StringUtils.isNotEqual(addressVo.getCity(), parsedAddress.getCity())) {
        // log.info("智能解析异常: {} 不等于 {}", parsedAddress.getCity(), addressVo.getCity());
        // }
        //
        // // 湖北省 直辖县 仙桃市 环球中心广场b2栋二单元3301
        // receiver = new KDBirdAddressInfo(
        // "ssss", "18913105810", "湖北省", "直辖县", "仙桃市", "环球中心广场b2栋二单元3301");
        // reachable = kdBirdExpressService.queryAddress(receiver, sender,
        // KDBirdExpressService.SF_CODE);
        // log.info("reachable: {}", reachable);
        //
        // parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
        // log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
        //
        // addressVo = AddressParser.simpleParse(receiver.desc());
        // log.info("addressVo: {}", addressVo);
        // if (StringUtils.isNotEqual(addressVo.getCity(), parsedAddress.getCity())) {
        // log.info("智能解析异常: {} 不等于 {}", parsedAddress.getCity(), addressVo.getCity());
        // }
        //
        // // 浙江省 杭州市 临安区 长桥路滨湖天地3幢2907
        // receiver = new KDBirdAddressInfo(
        // "ssss", "18913105810", "浙江省", "杭州市", "临安区", "长桥路滨湖天地3幢2907");
        // reachable = kdBirdExpressService.queryAddress(receiver, sender,
        // KDBirdExpressService.SF_CODE);
        // log.info("reachable: {}", reachable);
        //
        // parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
        // log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
        //
        // addressVo = AddressParser.simpleParse(receiver.desc());
        // log.info("addressVo: {}", addressVo);
        // if (StringUtils.isNotEqual(addressVo.getCity(), parsedAddress.getCity())) {
        // log.info("智能解析异常: {} 不等于 {}", parsedAddress.getCity(), addressVo.getCity());
        // }

        /*
         * // 重庆市 重庆市 江北区 大石坝街道华润中央公园10栋
         * receiver = new KDBirdAddressInfo(
         * "ssss", "18913105810", "重庆市", "重庆市", "江北区", "大石坝街道华润中央公园10栋");
         * reachable = kdBirdExpressService.queryAddress(receiver, sender,
         * KDBirdExpressService.JD_CODE);
         * log.info("reachable: {}", reachable);
         *
         * parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
         * log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
         *
         * // 河北省 沧州市 肃宁县 国宅华园
         * receiver = new KDBirdAddressInfo(
         * "ssss", "18913105810", "河北省", "沧州市", "肃宁县", "国宅华园");
         * reachable = kdBirdExpressService.queryAddress(receiver, sender,
         * KDBirdExpressService.JD_CODE);
         * log.info("reachable: {}", reachable);
         *
         * parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
         * log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
         *
         * // 北京市 北京市 海淀区 北下关街道 交大东路31号院办公区东门丰巢柜
         * receiver = new KDBirdAddressInfo(
         * "ssss", "18913105810", "北京市", "北京市", "海淀区", "北下关街道 交大东路31号院办公区东门丰巢柜");
         * reachable = kdBirdExpressService.queryAddress(receiver, sender,
         * KDBirdExpressService.JD_CODE);
         * log.info("reachable: {}", reachable);
         *
         * parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
         * log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
         *
         * // 湖南省 长沙市 天心区 长沙理工大学云塘校区弘毅轩四栋
         * receiver = new KDBirdAddressInfo(
         * "ssss", "18913105810", "湖南省", "长沙市", "天心区", "长沙理工大学云塘校区弘毅轩四栋");
         * reachable = kdBirdExpressService.queryAddress(receiver, sender,
         * KDBirdExpressService.JD_CODE);
         * log.info("reachable: {}", reachable);
         *
         * parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
         * log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
         *
         * // 浙江省 杭州市 临安区 长桥路滨湖天地3幢2907
         * receiver = new KDBirdAddressInfo(
         * "ssss", "18913105810", "浙江省", "杭州市", "临安区", "长桥路滨湖天地3幢2907");
         * reachable = kdBirdExpressService.queryAddress(receiver, sender,
         * KDBirdExpressService.JD_CODE);
         * log.info("reachable: {}", reachable);
         *
         * parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
         * log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
         *
         * // 江苏省 宿迁市 沭阳县 20号楼1单元601
         * receiver = new KDBirdAddressInfo(
         * "ssss", "18913105810", "江苏省", "宿迁市", "沭阳县", "20号楼1单元601");
         * reachable = kdBirdExpressService.queryAddress(receiver, sender,
         * KDBirdExpressService.JD_CODE);
         * log.info("reachable: {}", reachable);
         *
         * parsedAddress = kdBirdExpressService.parseAddress(receiver.desc());
         * log.info("address: {}, parsedAddress: {}", receiver.desc(), parsedAddress);
         */

    }

    /**
     * 测试合单
     */
    private void testMergeOrderExpress() {
        Map<String, String> expressOrderMap = new ConcurrentHashMap<>();

        YxStoreOrderQueryCriteria query = new YxStoreOrderQueryCriteria();
        /**
         * 分仓库进行
         */
        // query.setDepotId(ShopConstants.DEPOT_SHIJIAZHUANG_JD);
        /**
         * 出库中的状态 = 已发货（出库中）
         */
        query.setStatus(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue());
        query.setDeliveryStatus(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue());

        List<Order> orderList = storeOrderService.queryAllOrder(query);
        log.info("orderList size: {}", orderList.size());

        for (Order o : orderList) {
            String key = o.delivieryKey();
            if (expressOrderMap.containsKey(key)) {
                log.info("待合单: key: {}, order_id: {}", key, o.getOrderId());

            } else {
                expressOrderMap.put(key, o.getOrderId());
                if (o.getIndependently() != null && o.getIndependently()) {
                    log.info("新单 独立发货: key: {}, order_id: {}, 地址信息：{}, {}, {}", key, o.getOrderId(), o.getRealName(),

                            o.getUserPhone(), o.getUserAddress());
                } else {
                    log.info("新单: key: {}, order_id: {}", key, o.getOrderId());

                }
            }
        }

        log.info("合单后快递单数量：{}", expressOrderMap.size());

    }

    /**
     * 微信支付账单导出
     *
     * @param allArgs
     */
    private void downloadWechatPayBill(String[] allArgs) {
        Options options = new Options() {
            {
            }
        };

        Option startOp = new Option("s", "start", true, "开始") {
            {
                setRequired(true);
            }
        };
        options.addOption(startOp);

        Option endOp = new Option("e", "end", true, "结束") {
            {
                setRequired(true);
            }
        };
        options.addOption(endOp);

        Option outDirOp = new Option("d", "dir", true, "导出路径") {
            {
                setRequired(true);
            }
        };
        options.addOption(outDirOp);

        RelaxedParser parser = new RelaxedParser();
        HelpFormatter formatter = new HelpFormatter();
        CommandLine cmd;

        try {
            cmd = parser.parse(options, allArgs, false);
            // 2024-05-06
            String start = cmd.getOptionValue("start");
            // 2024-05-07
            String end = cmd.getOptionValue("end");
            String dir = cmd.getOptionValue("dir");

            // String filePath = dir + File.separator + "微信支付账单" + start + "-" + end +
            // ".json";

            // 根据日期差值，下载账单
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            Date startDate = sdf.parse(start);
            Date endDate = sdf.parse(end);

            Calendar startCal = Calendar.getInstance();
            startCal.setTime(startDate);
            Calendar endCal = Calendar.getInstance();
            endCal.setTime(endDate);

            while (startCal.compareTo(endCal) <= 0) {
                String date = sdf.format(startCal.getTime());
                String filePath = dir + File.separator + "微信支付账单" + date + ".csv";

                // 格式：20140603
                String queryDate = date.replace("-", "");
                String content = "";
                try {
                    content = weixinPayService.downloadRawBill(queryDate);
                    log.info("download {} 交易账单 success", date);
                    FileUtil.writeUtf8String(content, filePath);
                } catch (Exception e) {
                    log.error("download {} 交易账单 failed, {}", date, e.getMessage());
                }

                content = "";
                try {
                    WxPayFundFlowResult fundFlowRet = weixinPayService.downloadFundFlow(queryDate);
                    // 转为JSON 写入文件
                    filePath = dir + File.separator + "资金账单" + date + ".json";
                    content = JSON.toJSONString(fundFlowRet);
                    log.info("download {} 资金账单 success", date);
                    FileUtil.writeUtf8String(content, filePath);
                } catch (Exception e) {
                    log.error("download {} 资金账单 failed, {}", date, e.getMessage());
                }

                startCal.add(Calendar.DAY_OF_MONTH, 1);
            }

            log.info("download finished");

        } catch (ParseException e) {
            System.out.println(e.getMessage());
            formatter.printHelp("utility-name", options);
            log.info("参数错误，应该为：--task=download_bill, <start>, <end> <dir>");

            System.exit(0);
        } catch (java.text.ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 微信支付退款，和订单无关
     *
     * @param allArgs
     */
    private void wechatRefund(String[] allArgs) {
        Options options = new Options() {
            {
            }
        };

        Option uidOp = new Option("a", "amount", true, "金额") {
            {
                setRequired(true);
            }
        };
        options.addOption(uidOp);

        Option pidOp = new Option("o", "oid", true, "订单ID") {
            {
                setRequired(true);
            }
        };
        options.addOption(pidOp);

        RelaxedParser parser = new RelaxedParser();
        HelpFormatter formatter = new HelpFormatter();
        CommandLine cmd;

        try {
            cmd = parser.parse(options, allArgs, false);
            String oidStr = cmd.getOptionValue("oid");
            String amountStr = cmd.getOptionValue("amount");

            // Long oID = Long.valueOf(oidStr);
            int amount = Integer.valueOf(amountStr) * 100;

            weixinPayService.refundOrder(oidStr, amount);
            log.info("refund success");

        } catch (ParseException e) {
            System.out.println(e.getMessage());
            formatter.printHelp("utility-name", options);
            log.info("参数错误，应该为：--task=refund, <oid>, <amount>");

            System.exit(0);
        }
    }

    private void refund(String[] allArgs) {
        Options options = new Options() {
            {
            }
        };

        Option uidOp = new Option("a", "amount", true, "金额") {
            {
                setRequired(true);
            }
        };
        options.addOption(uidOp);

        Option pidOp = new Option("o", "oid", true, "订单ID") {
            {
                setRequired(true);
            }
        };
        options.addOption(pidOp);

        RelaxedParser parser = new RelaxedParser();
        HelpFormatter formatter = new HelpFormatter();
        CommandLine cmd;

        try {
            cmd = parser.parse(options, allArgs, false);
            String oidStr = cmd.getOptionValue("oid");
            String amountStr = cmd.getOptionValue("amount");

            // Long oID = Long.valueOf(oidStr);
            Long amount = Long.valueOf(amountStr);

            storeOrderService.orderRefund(oidStr, BigDecimal.valueOf(amount));
        } catch (ParseException e) {
            System.out.println(e.getMessage());
            formatter.printHelp("utility-name", options);
            log.info("参数错误，应该为：--task=refund, <oid>, <amount>");

            System.exit(0);
        }
    }

    /**
     * --task=sub_msg_send -u 80 -p 642
     *
     * @param allArgs
     */
    private void sendSubMsg(String[] allArgs) {
        Options options = new Options() {
            {
            }
        };

        Option uidOp = new Option("u", "user", true, "用户") {

            {
                setRequired(true);
            }
        };
        options.addOption(uidOp);

        Option pidOp = new Option("p", "product", true, "产品") {
            {
                setRequired(true);
            }
        };
        options.addOption(pidOp);

        // Option skuUniqueOp = new Option("s", "sku", true, "SKU") {
        // {
        // setRequired(true);
        // }
        // };
        // options.addOption(skuUniqueOp);

        RelaxedParser parser = new RelaxedParser();
        HelpFormatter formatter = new HelpFormatter();
        CommandLine cmd;

        try {
            cmd = parser.parse(options, allArgs, false);
            String uidStr = cmd.getOptionValue("user");
            Long uid = Long.valueOf(uidStr);

            String pidStr = cmd.getOptionValue("product");
            Long pid = Long.valueOf(pidStr);

            Product product = storeProductService.getById(pidStr);

            String spec = "默认";

            weiXinSubscribeService.newProductSupplyNotice(pid, product.getStoreName(), spec, uid);
            log.info("send sub msg success: uid: {}, pid: {}", uid, pid);
        } catch (ParseException e) {
            log.error(e.getMessage());
            formatter.printHelp("utility-name", options);
            log.info("参数错误，应该为：--task=sub_msg_send -u -p -s");
            System.exit(0);
        }
    }

    /**
     * --task=sync_inventory -d 20240201
     *
     * @param allArgs
     */
    private void syncInventory(String[] allArgs) {
        Options options = new Options() {
            {
            }
        };

        Option pidOp = new Option("d", "day", true, "日期") {
            {
                setRequired(true);
            }
        };
        options.addOption(pidOp);

        RelaxedParser parser = new RelaxedParser();
        HelpFormatter formatter = new HelpFormatter();
        CommandLine cmd;

        try {
            cmd = parser.parse(options, allArgs, false);
            String dateStr = cmd.getOptionValue("day");
            DateTime d = DateUtil.parse(dateStr, DatePattern.PURE_DATE_PATTERN);
            if (d == null) {
                log.info("日期格式错误");
                System.exit(0);
            }
            // 不会生成summary，只会去同步summary，产生库存操作记录
            storeOrderService.reSyncTmpInventory(dateStr);
        } catch (ParseException e) {
            System.out.println(e.getMessage());
            formatter.printHelp("utility-name", options);
            log.info("参数错误，应该为：--task=sync_inventory, <day>");

            System.exit(0);
        }
    }

    /**
     * --task=check_limit -u 1 -p 1 -s skuUnique
     *
     * @param allArgs
     */
    private void checkLimit(String[] allArgs) {
        Options options = new Options() {
            {
            }
        };

        Option uidOp = new Option("u", "uid", true, "用户ID") {
            {
                setRequired(true);
            }
        };
        options.addOption(uidOp);

        Option pidOp = new Option("p", "pid", true, "产品ID") {
            {
                setRequired(true);
            }
        };
        options.addOption(pidOp);

        Option skuUniqueOp = new Option("s", "sku", true, "SKU唯一值") {
            {
                setRequired(true);
            }
        };
        options.addOption(skuUniqueOp);

        // --clear=true
        // -c=true
        Option clearOp = new Option("c", "clear", true, "清除缓存") {
            {
                setRequired(false);
                setType(Boolean.class);
            }
        };
        options.addOption(clearOp);

        RelaxedParser parser = new RelaxedParser();
        HelpFormatter formatter = new HelpFormatter();
        CommandLine cmd;

        try {
            cmd = parser.parse(options, allArgs, false);
            String uidStr = cmd.getOptionValue("uid");
            String pidStr = cmd.getOptionValue("pid");
            String skuUnique = cmd.getOptionValue("sku");

            Long uID = Long.valueOf(uidStr);
            Long pID = Long.valueOf(pidStr);
            Boolean clear = Boolean.valueOf(cmd.getOptionValue("clear"));

            String key = StoreOrderService.getSKUBuyLimitCacheKey(uID, pID, skuUnique);
            log.info("key: {}", key);

            Integer skuAlreadyBuyNum = RedisUtil.get(key);
            if (skuAlreadyBuyNum == null) {
                skuAlreadyBuyNum = 0;
                log.info("alreadyBuyNum is empty");

            }

            log.info("alreadyBuyNum: {}", skuAlreadyBuyNum);

            if (clear != null && clear) {
                log.info("clear cache");

                RedisUtil.del(key);
            }
        } catch (ParseException e) {
            System.out.println(e.getMessage());
            formatter.printHelp("utility-name", options);
            log.info("参数错误，应该为：--task=check_limit, <uid>, <pid>, <skuUnique> <clear>");

            System.exit(0);
        }

    }

    /**
     * 同步发货信息给微信
     */
    private void uploadDeliveryInfo2Wechat() {
        String orderId = "1770339283836641280";
        storeOrderService.uploadDeliveryInfo2Wechat(orderId);
    }

    private void testSelectOrderByProduct() {
        int page = 0;
        int pageLimit = 10;

        Pageable pageQuery = Pageable.ofSize(pageLimit).withPage(page);
        YxStoreOrderQueryCriteria query = new YxStoreOrderQueryCriteria();
        query.setProductId(9L);
        query.setPaid(1);
        PageResult<YxStoreOrderDto> result = storeOrderService.queryPagedResult(query, pageQuery);
        log.info("storeOrderList total: {}, cur page size: {}", result.getTotalElements(), result.getContent().size());

    }

    @Autowired
    private WeAppCommonService weAppCommonService;

    void testWechatAccessToken() {
        String ac = weAppCommonService.getAccessToken();
        log.info("ac from wechat: {}", ac);

        ac = weAppCommonService.getAccessToken();
        log.info("ac from cache: {}", ac);
    }

    private void testScheduler() {
        // 创建一个定时执行器服务
        ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

        int i = 0;
        final int limit = 20; // 每秒20个订单

        while (i < limit) {
            int finalI = i;
            scheduler.schedule(() -> {
                log.info("{} start do something in one second: {}", finalI, DateUtils.getTime());

                // 睡眠0.5秒
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                log.info("{} end do something in one second: {}", finalI, DateUtils.getTime());

            }, 1, TimeUnit.SECONDS);

            i += 1;
        }

        // 关闭调度器
        scheduler.shutdown();
        try {
            // 等待所有已提交的任务执行完毕
            scheduler.awaitTermination(Long.MAX_VALUE, TimeUnit.SECONDS);
            log.info("scheduler shutdown success");

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    @Autowired
    private ResourceLoader resourceLoader;

    public String loadJson(String location) throws IOException {
        Resource resource = resourceLoader.getResource("classpath:" + location);
        try (InputStream is = resource.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {
            return reader.lines().collect(Collectors.joining(System.lineSeparator()));
        }
    }

    @Autowired
    private ExpressService expressService;

    @Autowired
    private KDBirdExpressService kdBirdExpressService;

    private void checkDelivery() {
        // 从resouces 读取 deliveryList.json
        try {
            String jsonData = loadJson("deliveryList.json");
            // jsonData里面是JSON字符串
            JSONArray deliveryList = (JSONArray) JSONObject.parse(jsonData);
            log.info("微信物流公司数量: {}", deliveryList.size());

            List<Express> expressList = expressService.queryAll(new YxExpressQueryCriteria());
            log.info("本系统物流公司数量: {}", expressList.size());

            int count = 0;
            for (Express express : expressList) {
                String expressName = express.getName();
                String expressCode = express.getCode();
                boolean found = false;
                for (Object obj : deliveryList) {
                    JSONObject jsonObj = (JSONObject) obj;
                    String name = jsonObj.getString("delivery_name");
                    String id = jsonObj.getString("delivery_id");
                    // if (name.equals(expressName)) {
                    if (expressCode.equals(id)) {
                        found = true;
                        expressCode = id;
                        expressName = name;
                        count += 1;
                        break;
                    }
                }
                if (!found) {
                    express.setIsShow(0);
                    boolean succ = expressService.update(express, Wrappers.<Express>lambdaQuery()
                            .in(Express::getId, express.getId()));
                    log.info("未找到物流公司: {} {} 暂时隐藏", expressCode, expressName);

                }
            }
            log.info("一致的物流公司数量: {}", count);

            scriptTestService.testQueryShipper();
        } catch (IOException e) {
            e.printStackTrace();
            log.error("读取deliveryList.json失败: {}", e.getMessage());

        }

    }

    @Autowired
    ProductRelationService productRelationService;

    void testQueryNotice() {
        int pageSize = 5000;
        Sort sort = Sort.by(Sort.Direction.ASC, "update_time");
        Pageable pageQuery = new SortPage(0, pageSize, sort);
        YxStoreProductRelationQueryCriteria criteria = new YxStoreProductRelationQueryCriteria() {
            {
                setUid(3636l);
                setSku("8f60540739a342028ef8f3cc5e8106f7");
            }
        };

        List<ProductRelationDto> relations = productRelationService.queryAll(criteria, pageQuery).getContent();
        for (ProductRelationDto relation : relations) {
            log.info("relation: {}, ctime: {}, utime: {}, isDel: {}", relation.getId(), relation.getCreateTime(),
                    relation.getUpdateTime(), relation.getIsDel());
            productRelationService.delProductRelation(relation.getProductId(), relation.getUid(), relation.getType(),
                    relation.getSku());
        }
    }

    void fix0506bug() {
        // 05-07
        // storeOrderService.fix0506BatchOrderIssue();

        // 05-09
        // storeOrderService.query0506UidDupOrder();
    }

    @Autowired
    private RedisUtils redisUtils;

    void testRedisLock() {
        String key = String.format(ShopConstants.LOCK_KEY_TMP_LOCK_ORDER_STATUS, "1");
        boolean has = redisUtils.hasKey(key);
        log.info("not has key: {} {}", key, has);
        // 锁10s
        redisUtils.set(key, 1, 10, TimeUnit.SECONDS);

        has = redisUtils.hasKey(key);
        log.info("should has key: {} {}", key, has);
        redisUtils.del(key);

        has = redisUtils.hasKey(key);
        log.info("not has key: {} {}", key, has);
    }

    /**
     * 测试用户地址默认设置的并发性能
     */
    private void testAddressDefaultConcurrent() {
        log.info("开始测试用户地址默认设置的并发性能");

        // 从Spring容器中获取YxUserAddressService
        YxUserAddressService userAddressService = context.getBean(YxUserAddressService.class);

        // 模拟的用户ID
        final Long uid = 1000L;

        // 先创建5个测试地址
        List<Long> addressIds = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            // 创建地址参数
            AddressParam param = new AddressParam();
            param.setReal_name("测试用户" + i);
            param.setPhone("1380000" + String.format("%04d", i));
            param.setDetail("测试地址详情" + i);
            param.setIs_default(i == 0 ? "true" : "false"); // 第一个设为默认

            // 创建地址对象
            com.leway.modules.user.domain.YxUserAddress address = new com.leway.modules.user.domain.YxUserAddress();
            address.setUid(uid);
            address.setRealName("测试用户" + i);
            address.setPhone("1380000" + String.format("%04d", i));
            address.setDetail("测试地址详情" + i);
            address.setProvince("测试省");
            address.setCity("测试市");
            address.setDistrict("测试区");
            address.setIsDefault(i == 0 ? 1 : 0); // 第一个设为默认

            try {
                // 保存地址
                userAddressService.save(address);
                addressIds.add(address.getId());
                log.info("创建测试地址成功: {}", address.getId());
            } catch (Exception e) {
                log.error("创建测试地址失败", e);
            }
        }

        if (addressIds.size() < 2) {
            log.error("创建测试地址不足，无法进行并发测试");
            return;
        }

        // 创建线程池
        int threadCount = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 提交并发任务
        for (int i = 0; i < threadCount; i++) {
            final int index = i % addressIds.size();
            executorService.submit(() -> {
                try {
                    // 随机选择一个地址设为默认
                    Long addressId = addressIds.get(index);
                    log.info("线程 {} 开始设置地址 {} 为默认地址", Thread.currentThread().getName(), addressId);

                    // 调用设置默认地址方法
                    userAddressService.setDefault(uid, addressId);

                    log.info("线程 {} 成功设置地址 {} 为默认地址", Thread.currentThread().getName(), addressId);
                } catch (Exception e) {
                    log.error("设置默认地址失败: {}", e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            // 等待所有线程完成
            latch.await();

            // 记录结束时间
            long endTime = System.currentTimeMillis();
            log.info("并发测试完成，耗时: {}ms", endTime - startTime);

            // 验证结果 - 检查是否只有一个默认地址
            int defaultCount = 0;
            Long defaultAddressId = null;

            for (Long addressId : addressIds) {
                com.leway.modules.user.domain.YxUserAddress address = userAddressService.getById(addressId);
                if (address != null && address.getIsDefault() == 1) {
                    defaultCount++;
                    defaultAddressId = addressId;
                }
            }

            log.info("测试结果: 默认地址数量={}, 默认地址ID={}", defaultCount, defaultAddressId);

            if (defaultCount == 1) {
                log.info("测试成功: 只有一个默认地址");
            } else {
                log.error("测试失败: 存在{}个默认地址", defaultCount);
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("测试被中断", e);
        } finally {
            // 清理测试数据
            for (Long addressId : addressIds) {
                try {
                    userAddressService.removeById(addressId);
                    log.info("删除测试地址: {}", addressId);
                } catch (Exception e) {
                    log.error("删除测试地址失败: {}", e.getMessage());
                }
            }

            // 关闭线程池
            executorService.shutdown();
        }
    }

    /**
     * 测试用户地址默认设置的并行性能
     * 使用更高的并发量和更复杂的测试场景
     */
    private void testParallelAddress() {
        log.info("开始测试用户地址默认设置的并行性能");

        // 从Spring容器中获取YxUserAddressService
        YxUserAddressService userAddressService = context.getBean(YxUserAddressService.class);

        // 模拟多个用户，每个用户有多个地址
        int userCount = 5;
        int addressPerUser = 3;
        int threadCount = 20; // 更高的并发量

        // 存储测试数据，格式: Map<用户ID, 地址ID列表>
        Map<Long, List<Long>> testData = new HashMap<>();

        // 创建测试数据
        for (int u = 0; u < userCount; u++) {
            Long uid = 2000L + u;
            List<Long> addressIds = new ArrayList<>();

            for (int i = 0; i < addressPerUser; i++) {
                // 创建地址对象
                com.leway.modules.user.domain.YxUserAddress address = new com.leway.modules.user.domain.YxUserAddress();
                address.setUid(uid);
                address.setRealName("并行测试用户" + u + "-" + i);
                address.setPhone("1390000" + String.format("%04d", u * 10 + i));
                address.setDetail("并行测试地址详情" + u + "-" + i);
                address.setProvince("测试省");
                address.setCity("测试市");
                address.setDistrict("测试区");
                address.setIsDefault(i == 0 ? 1 : 0); // 第一个设为默认

                try {
                    // 保存地址
                    userAddressService.save(address);
                    addressIds.add(address.getId());
                    log.info("创建测试地址成功: uid={}, addressId={}", uid, address.getId());
                } catch (Exception e) {
                    log.error("创建测试地址失败", e);
                }
            }

            if (!addressIds.isEmpty()) {
                testData.put(uid, addressIds);
            }
        }

        if (testData.isEmpty()) {
            log.error("创建测试数据失败，无法进行并行测试");
            return;
        }

        // 创建线程池
        ExecutorService executorService = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 提交并行任务
        for (int i = 0; i < threadCount; i++) {
            executorService.submit(() -> {
                try {
                    // 随机选择一个用户
                    List<Long> userIds = new ArrayList<>(testData.keySet());
                    Long uid = userIds.get((int) (Math.random() * userIds.size()));

                    // 随机选择该用户的一个地址
                    List<Long> addressIds = testData.get(uid);
                    Long addressId = addressIds.get((int) (Math.random() * addressIds.size()));

                    log.info("线程 {} 开始为用户 {} 设置地址 {} 为默认地址",
                            Thread.currentThread().getName(), uid, addressId);

                    // 调用设置默认地址方法
                    userAddressService.setDefault(uid, addressId);

                    log.info("线程 {} 成功为用户 {} 设置地址 {} 为默认地址",
                            Thread.currentThread().getName(), uid, addressId);
                } catch (Exception e) {
                    log.error("设置默认地址失败: {}", e.getMessage());
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            // 等待所有线程完成
            latch.await();

            // 记录结束时间
            long endTime = System.currentTimeMillis();
            log.info("并行测试完成，耗时: {}ms", endTime - startTime);

            // 验证结果 - 检查每个用户是否只有一个默认地址
            boolean testSuccess = true;

            for (Map.Entry<Long, List<Long>> entry : testData.entrySet()) {
                Long uid = entry.getKey();
                List<Long> addressIds = entry.getValue();

                int defaultCount = 0;
                Long defaultAddressId = null;

                for (Long addressId : addressIds) {
                    com.leway.modules.user.domain.YxUserAddress address = userAddressService.getById(addressId);
                    if (address != null && address.getIsDefault() == 1) {
                        defaultCount++;
                        defaultAddressId = addressId;
                    }
                }

                log.info("用户 {} 测试结果: 默认地址数量={}, 默认地址ID={}", uid, defaultCount, defaultAddressId);

                if (defaultCount != 1) {
                    testSuccess = false;
                    log.error("用户 {} 测试失败: 存在{}个默认地址", uid, defaultCount);
                }
            }

            if (testSuccess) {
                log.info("并行测试成功: 所有用户都只有一个默认地址");
            } else {
                log.error("并行测试失败: 存在用户有多个或没有默认地址");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("测试被中断", e);
        } finally {
            // 清理测试数据
            for (Map.Entry<Long, List<Long>> entry : testData.entrySet()) {
                for (Long addressId : entry.getValue()) {
                    try {
                        userAddressService.removeById(addressId);
                        log.info("删除测试地址: {}", addressId);
                    } catch (Exception e) {
                        log.error("删除测试地址失败: {}", e.getMessage());
                    }
                }
            }

            // 关闭线程池
            executorService.shutdown();
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        // 获取所有参数
        String[] allArgs = args.getSourceArgs();
        log.info("all args: {}", String.join(", ", allArgs));

        // 获取指定选项的参数
        List<String> taskValues = args.getOptionValues("task");

        // 检查某个选项是否存在
        boolean hasTask = args.containsOption("task");
        if (!hasTask) {
            log.info("task not found!");

            return;
        }

        // 只认第一个task
        String taskName = taskValues.get(0);

        log.info("====================================");

        log.info("Script ApplicationRunner runner start!");

        log.info("task: {}", taskName);

        log.info("====================================\n\n\n");

        switch (taskName) {
            case TASK_TEST:
                // scriptTestService.testAll();
                // testSelectOrderByProduct();
                // testScheduler();
                // uploadDeliveryInfo2Wechat();
                // testSentry();
                // testMergeOrderExpress();
                // testAddress();
                // testGetWechatOrderState();
                // testTaiwan();
                // testQuota();
                // parseKdBirdRoutePost();
                // testInterrupt();
                // fix0506bug();
                // testQueryNotice();
                // testWechatAccessToken();
                testParallelAddress();
                log.info("test all");

                break;
            case TASK_SYNC2WECHAT:
                syncOrder2Wechat();
                break;
            case TASK_REFUND:
                log.info("refund");

                wechatRefund(allArgs);
                break;
            case TASK_DOWNLOAD_BILL:
                log.info("下载支付账单");

                downloadWechatPayBill(allArgs);
                break;
            case TASK_DATA_MIGRATION:
                log.info("data migration");

                break;
            case TASK_CHECK_LIMIT:
                log.info("check limit");

                checkLimit(allArgs);
                break;
            case TASK_CHECK_DELIVERY:
                log.info("check delivery");

                checkDelivery();
                break;
            case TASK_SYNC_INVENTORY:
                log.info("sync inventory");

                syncInventory(allArgs);
                break;
            case TASK_TEST_SEND_SUB_MSG:
                log.info("send sub msg");

                sendSubMsg(allArgs);
                break;
            case TASK_PARALLEL_ADDRESS:
                log.info("parallel address");

                testParallelAddress();
                break;
            default:
                log.info("task not found!");

                break;
        }

        log.info("\n");

        log.info("====================================");

        log.info("Script ApplicationRunner runner end!");

        log.info("====================================\n\n\n");

    }

}
