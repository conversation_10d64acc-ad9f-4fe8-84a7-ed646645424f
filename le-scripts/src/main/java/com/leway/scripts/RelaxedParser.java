package com.leway.scripts;

import org.apache.commons.cli.CommandLine;
import org.apache.commons.cli.DefaultParser;
import org.apache.commons.cli.Options;
import org.apache.commons.cli.ParseException;

import java.util.ArrayList;
import java.util.List;

public class RelaxedParser extends DefaultParser {

    @Override
    public CommandLine parse(Options options, String[] arguments, boolean stopAtNonOption) throws ParseException {
        List<String> modifiedArguments = new ArrayList<>();

        List<String> needParseArguments = new ArrayList<>();
        for (String arg : arguments) {
            // Deal with long options with '='
            String optionName = arg;
            String optionValue = null;

            // Check if it's a long option with an '='
            if (arg.startsWith("--") && arg.contains("=")) {
                int equalIndex = arg.indexOf('=');
                optionName = arg.substring(0, equalIndex);
                optionValue = arg.substring(equalIndex + 1);

                // Strip off the "--" before we check the option
                optionName = optionName.replaceFirst("^--", "");

                // If the option is known, add it
                if (options.hasLongOption(optionName)) {
                    modifiedArguments.add("--" + optionName);
                    modifiedArguments.add(optionValue);
                    continue;
                }
            }

            // Check single dash long-style options without '=', or standard short options
            if ((arg.startsWith("-") && !arg.matches("^-[0-9]+$")) || (arg.startsWith("--") && !options.hasLongOption(optionName))) {
                // Check if it's a short option
                optionName = arg.replaceFirst("^-+", "");

                // If the option is known, add it, otherwise ignore it
                if (options.hasShortOption(optionName)) {
                    modifiedArguments.add("-" + optionName);
                }
                continue;
            }

            // If it's not an option at all, just a value, add it.
            modifiedArguments.add(arg);
        }

        // Use the super method to parse the known and separated options.
        return super.parse(options, modifiedArguments.toArray(new String[0]), stopAtNonOption);
    }
}