package com.leway.scripts;

import java.util.Date;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Profile;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.leway.domain.PageResult;
import com.leway.enums.AfterSalesStateEnum;
import com.leway.enums.ShopCommonEnum;
import com.leway.foo.BarService;
import com.leway.modules.mp.service.WeAppCodeService;
import com.leway.modules.mp.service.WeAppCommonService;
import com.leway.modules.mp.service.WeiXinOrderShippingService;
import com.leway.modules.order.service.StoreOrderService;
import com.leway.modules.order.service.dto.YxStoreOrderDto;
import com.leway.modules.order.service.dto.YxStoreOrderQueryCriteria;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.sales.domain.AfterSales;
import com.leway.modules.sales.param.AfterSalesQueryCriteria;
import com.leway.modules.sales.service.AfterSalesService;
import com.leway.modules.sales.service.vo.AfterSalesVo;
import com.leway.modules.tools.express.KDBirdExpressService;
import com.leway.modules.tools.express.config.ExpressAutoConfiguration;
import com.leway.modules.tools.express.vo.Shippers;

import lombok.extern.slf4j.Slf4j;

@Slf4j
//@Profile("dev")
@Service
public class ScriptTestService {
    // @Autowired
    // MenuService yxMenuService;

    @Autowired
    WeiXinOrderShippingService weiXinOrderShippingService;

    @Autowired
    WeAppCodeService weAppCodeService;

    public void getWxDeliveryList() {
        weiXinOrderShippingService.getDeliveryList();
    }

    public void testUploadDeliveryInfo() {
        String orderCode = "1725317026643181568";
        String openId = "ocWOO6zI0Xgv9DjzMbIkFKy4xhPo";
        String transaction_id = "4200002020202311171526733508";
        weiXinOrderShippingService.uploadShippingInfo(
                orderCode,
                openId,
                transaction_id,
                "手套",
                "78380030550344",
                "ZTO",
                null,
                "17600083957",
                1);
    }

    public void testUpdateDeliveryInfo() {
        String orderCode = "1725317026643181568";
        String openId = "ocWOO6zI0Xgv9DjzMbIkFKy4xhPo";
        String transaction_id = "4200002020202311171526733508";
        weiXinOrderShippingService.uploadCombinedShippingInfo(
                orderCode,
                openId,
                transaction_id,
                "手套",
                "78380030550344",
                "ZTO",
                null,
                "17600083957",
                1);
    }

    public void testQueryOrderDeliveryStatus() {
        // String orderCode = "1725317026643181568";
        // String transaction_id = "4200002020202311171526733508";
        String orderCode = "1726853780848246784";
        String transaction_id = null;
        JSONObject order = weiXinOrderShippingService.queryOrderShippingStatus(transaction_id, orderCode);
        log.info("order: {}", order);
    }

    public boolean testGetOrderList() {
        int order_state = WeiXinOrderShippingService.OrderState.WAITING_FOR_DELIVERY.getCode();
        JSONObject ret = weiXinOrderShippingService.getOrderList(null, null, order_state, null, null, null);
        log.info("ret: {}", ret);

        int errcode = ret.getInteger("errcode");
        if (errcode != 0) {
            log.error("ret: {} {}", errcode, ret.getString("errmsg"));
            return false;
        }

        String last_index = ret.getString("last_index");
        boolean has_more = ret.getBooleanValue("has_more");
        JSONArray order_list = ret.getJSONArray("order_list");
        log.info("last_index: {} has_more: {}", last_index, has_more);

        for (int i = 0; i < order_list.size(); i++) {
            JSONObject order = order_list.getJSONObject(i);
            log.info("order: {}", order);
        }
        while (has_more) {
            ret = weiXinOrderShippingService.getOrderList(null, null, order_state, null, last_index, null);
            errcode = ret.getInteger("errcode");
            if (errcode != 0) {
                log.error("ret: {} {}", errcode, ret.getString("errmsg"));
                return false;
            }
            has_more = ret.getBooleanValue("has_more");
            last_index = ret.getString("last_index");
            order_list = ret.getJSONArray("order_list");

            log.info("last_index: {} has_more: {}", last_index, has_more);
            for (int i = 0; i < order_list.size(); i++) {
                JSONObject order = order_list.getJSONObject(i);
                log.info("order: {}", order);
            }

        }
        return true;
    }

    /**
     * 测试物流号查询公司
     */
    public void testQueryShipper() {
        KDBirdExpressService kuaidiBirdExpressService = ExpressAutoConfiguration.expressService();
        try {
            String logisticCode = "SF1655289172858";
            logisticCode = "302550344";
            List<Shippers> ss = kuaidiBirdExpressService.queryOrderShipper(logisticCode);
            for (Shippers s : ss) {
                log.info("可能的物流公司: {} {}", s.getShipperCode(), s.getShipperName());
            }

            if (ss.size() == 0) {
                log.error("快递单号异常，没有对应的物流公司: {}", logisticCode);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("查询快递公司失败：{}", e.getMessage());
        }
    }

    /**
     * 测试发货提醒
     */
    public void testConfirmNotify() {
        String orderCode = "1725317026643181568";
        String transaction_id = "4200002020202311171526733508";
        Long t = new Date().getTime();
        JSONObject data = weiXinOrderShippingService.confirmOrderReceive(transaction_id, orderCode, t);
        log.info("ret: {}", data);
    }

    public void testSetMsgPath() {
        String path = "pages/order/OrderDetails/index";
        boolean succ = weiXinOrderShippingService.setMsgJumpPath(path);
        log.info("success: {}", succ);
    }

    public void testGetTradeManaged() {
        boolean managed = weiXinOrderShippingService.isTradeManaged();
        log.info("managed: {}", managed);
    }

    public void testWeAppCode() {
        try {
            weAppCodeService.getMiniProgramQRCode("pages/home/<USER>", 280, WeAppCodeService.EnvTrial);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Autowired
    private BarService barService;

    public void testCache() {
        // 1.
        log.info("get from db");
        barService.testSetExpire();

        // 2.
        log.info("get from cache");
        barService.testSetExpire();

        // 3.
        log.info("evict test cache");
        barService.testEvict();

        // 4.
        log.info("get from db2");
        barService.testSetExpire();

        // clear
        // barService.testEvict();

        log.info("done!");
    }

    @Autowired
    private AfterSalesService afterSalesService;

    /**
     * 测试售后
     */
    public void testAfterSales() {
        AfterSales obj = new AfterSales();
        obj.setOrderCode("2021063010000001");

        obj.setSalesState(AfterSales.SALES_CANCEL_STATUS_NORMAL);
        obj.setState(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue());
        obj.setIsDel(ShopCommonEnum.DELETE_0.getValue());

        obj.setServiceType(AfterSales.TYPE_ONLY_MONEY);

        boolean succ = afterSalesService.save(obj);
        log.info("新增售后, id: {}, succ: {}", obj.getId(), succ);

        int page = 0;
        int pageLimist = 10;

        Pageable pageQuery = Pageable.ofSize(pageLimist).withPage(page);
        log.info("page0 param: {}", pageQuery);
        AfterSalesQueryCriteria criteria = new AfterSalesQueryCriteria();

        PageResult<AfterSalesVo> result = afterSalesService.queryAll(criteria, pageQuery, false);
        log.info("page0 result: {} {}", result.getContent().size(), result.getTotalElements());

        long total = result.getTotalElements();
        while (page * pageLimist + result.getContent().size() < total) {
            page++;
            pageQuery = Pageable.ofSize(pageLimist).withPage(page);
            log.info("page{} param: {}", page, pageQuery);
            result = afterSalesService.queryAll(criteria, pageQuery, false);
            log.info("page{} reslut: {} {}", page, result.getContent().size(), result.getTotalElements());
        }
    }

    @Autowired
    private StoreOrderService storeOrderService;

    public void testOrderPage() {
        int page = 0;
        int pageLimist = 10000;

        Pageable pageQuery = Pageable.ofSize(pageLimist).withPage(page);
        log.info("page1: {}", pageQuery);

        YxStoreOrderQueryCriteria criteria = new YxStoreOrderQueryCriteria();

        PageResult<YxStoreOrderDto> result = storeOrderService.queryPagedResult(criteria, pageQuery);
        log.info("page1: {} {}", result.getContent().size(), result.getTotalElements());

        long total = result.getTotalElements();
        while ((page - 1) * pageLimist + result.getContent().size() < total) {
            page++;
            pageQuery = Pageable.ofSize(pageLimist).withPage(page);
            log.info("page{}: {}", page, pageQuery);
            result = storeOrderService.queryPagedResult(criteria, pageQuery);
            log.info("page{}: {} {}", page, result.getContent().size(), result.getTotalElements());
        }
    }

    @Autowired
    private ProductService productService;

    public void testPrepublish() {
        productService.checkPrePublishProduct2Onsale();
    }

    @Autowired
    private WeAppCommonService weAppCommonService;

    void testWechatAccessToken() {
        String ac = weAppCommonService.getAccessToken();
        log.info("ac from wechat: {}", ac);

        ac = weAppCommonService.getAccessToken();
        log.info("ac from cache: {}", ac);
    }

    public void testAll() {
        // testPrepublish();

        // testCache();
        // testWeAppCode();
        // testUploadDeliveryInfo();
        // testUpdateDeliveryInfo();
        // testQueryOrderDeliveryStatus();
        // testGetOrderList();
        // testConfirmNotify();
        // testSetMsgPath();
        // testGetTradeManaged();
        testWechatAccessToken();
        // testQueryShipper();

        // testAfterSales();

        // testOrderPage();
    }
}
