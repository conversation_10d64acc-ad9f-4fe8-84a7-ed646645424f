package com.leway.foo;

import com.leway.constant.ShopConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;

/**
 * Spring 的 AOP 功能，包括 @Cacheable，会对 Spring 容器内管理的 Bean 生效，如果对象不是由 Spring 容器创建和管理的，就不能享受由 Spring 提供的这些特性。
 * 如果BarService类被Spring管理（即已经加上了像@Component，@Service，@Repository，@Controller等任意一个 Spring 注解）就可以像获取service那样获取。
 */
@Slf4j
@Component
//@CacheConfig(cacheNames = "cache-key-bar-service#60")
@CacheConfig(cacheNames = "test-prefix-#60")
public class BarService {
    /**
     * 缓存
     * @return
     */
//    @Cacheable(key="'k1'", sync = true)
    @Cacheable(key= ShopConstants.YSHOP_REDIS_INDEX_KEY, sync = true)
    public String testSetExpire() {
        System.out.println("testSetExpire");
        return "result";
    }

    @Cacheable(key= ShopConstants.YSHOP_REDIS_INDEX_KEY + "：#p1", sync = true)
    public String testParam(String p1) {
        log.info("testParam:{}", p1);
        return "result";
    }

    /**
     * 清除缓存
     */
//    @CacheEvict(key="'k1'")
    @CacheEvict(key= ShopConstants.YSHOP_REDIS_INDEX_KEY, allEntries = true)
    public void testEvict() {
        System.out.println("evict test cache");
    }

   
}
