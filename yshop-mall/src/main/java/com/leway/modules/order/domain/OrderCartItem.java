/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>

 */
package com.leway.modules.order.domain;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.leway.modules.cart.vo.YxStoreCartQueryVo;
import com.leway.modules.product.vo.ProductQueryVo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import lombok.Data;

/**
 * 订单对应的商品信息
 * 一个订单包含多个该信息
 * 
 * <AUTHOR>
 * @date 2020-05-12
 */

@Data
@TableName("yx_store_order_cart_info")
public class OrderCartItem implements Serializable {

    /**
     * 可申请售后
     */
    public static final int CAN_APPLY_AFTER_SALES = 1;
    /**
     * 不可申请售后，已申请过
     */
    public static final int ALREADY_APPLIED_AFTER_SALES = 0;

    @TableId
    private Long id;

    /** 订单id */
    private Long oid;

    // public static Long getSOid(Object o) {
    // if (o instanceof OrderCartItem) {
    // return ((OrderCartItem) o).getOid();
    // }
    // return 0l;
    // }

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 购物车id
     * 购物车信息中包含商品SKU，即productAttrUnique
     */
    private Long cartId;

    /** 商品ID */
    private Long productId;

    // public Long getSPid(Object o) {
    // if (o instanceof OrderCartItem) {
    // return ((OrderCartItem) o).getProductId();
    // }
    // return 0l;
    // }

    /**
     * 购买东西的详细信息(JSON)
     * YxStoreCartQueryVo
     * {"refundNum":0,"uid":10,"productAttrUnique":"40ebc07132eb4d48ba0244fa97743870","productId":624,"trueStock":5,"id":524,"truePrice":160.0,"type":"product","cartNum":1,"productInfo":{"specType":0,"cate1stId":"15","code":"2","depotId":1,"description":"[{\"type\":\"image\",\"cover\":\"\",\"url\":\"https://img0.lubiandian.shop/WX20221021-172700.png\"}]","isPostage":0,"sales":0,"bannerImage":"","price":160.00,"storeName":"郊眠寺专业手工拨片CTPK-003苍蝇乐队","id":624,"prePublishStatus":0,"keyword":"","stock":5,"image":"https://img0.lubiandian.shop/O1CN01qDBGyB1EEbQODpjvc_!!2268730320.jpg_400x400.jpg","limitBuy":0,"unitName":"件","prePublishTime":1701995626000,"sort":0,"sliderImageArr":["https://img0.lubiandian.shop/O1CN01qDBGyB1EEbQODpjvc_!!2268730320.jpg_400x400.jpg"],"sliderImage":"https://img0.lubiandian.shop/O1CN01qDBGyB1EEbQODpjvc_!!2268730320.jpg_400x400.jpg","isHide":0,"isShow":1,"postage":0.00,"cateId":"20","producer":"万能青年旅店","style":"small","ficti":0,"storeInfo":"","tempId":34,"attrInfo":{"image":"https://img0.lubiandian.shop/O1CN01qDBGyB1EEbQODpjvc_!!2268730320.jpg_400x400.jpg","productId":624,"userWant":false,"otPrice":160.00,"weight":1000.00,"sales":0,"barCode":"2","volume":0.00,"price":160.00,"unique":"40ebc07132eb4d48ba0244fa97743870","id":188,"sku":"默认","stock":5},"browse":0}}
     */
    private String cartInfo;

    /** 唯一id */
    @TableField(value = "`unique`")
    private String unique;

    /** 是否能售后0不能1能 */
    private Integer isAfterSales;

    public static YxStoreCartQueryVo parseCartInfo(String infoJSONStr) {
        YxStoreCartQueryVo cartInfo = JSONObject.parseObject(infoJSONStr, YxStoreCartQueryVo.class);
        return cartInfo;
    }

    public YxStoreCartQueryVo cartInfoVO() {
        return OrderCartItem.parseCartInfo(cartInfo);
    }

    public void copy(OrderCartItem source) {
        BeanUtil.copyProperties(source, this, CopyOptions.create().setIgnoreNullValue(true));
    }

    /**
     * 获取购物车联合商品名称
     * 
     * @param cartItemList
     * @return
     */
    public static String getCombinedName(List<OrderCartItem> cartItemList) {
        Map<Long, String> productNameMapper = new HashMap<>();
        List<String> names = new ArrayList<>();
        for (OrderCartItem item : cartItemList) {
            String productNameTmp = item.cartInfoVO().getProductInfo().getStoreName();
            if (productNameMapper.containsKey(item.getProductId())) {
                continue;
            }
            productNameMapper.put(item.getProductId(), productNameTmp);
            names.add(productNameTmp);
        }
        String productName = names.stream().reduce((a, b) -> a + "、" + b).orElse("");
        return productName;
    }

    /**
     * 获取购物车联合商品名称
     * 
     * @param cartItemList 允许sku重复，应对合单情况
     * @return
     */
    public static String getSkuCombinedNameWithNum(List<OrderCartItem> cartItemList) {
        Map<String, String> skuNameMapper = new HashMap<>();
        Map<String, Integer> skuNumMapper = new HashMap<>();
        for (OrderCartItem item : cartItemList) {
            ProductQueryVo product = item.cartInfoVO().getProductInfo();
            String skuNameTmp = String.format("%s-%s", product.getStoreName(), product.getAttrInfo().getSku());
            String key = String.format("%d-%s", product.getId(), product.getAttrInfo().getSku());
            if (!skuNameMapper.containsKey(key)) {
                skuNameMapper.put(key, skuNameTmp);
            }

            Integer num = skuNumMapper.getOrDefault(key, 0);
            num += item.cartInfoVO().getCartNum();

            skuNumMapper.put(key, num);
        }

        List<String> names = new ArrayList<>();
        // 需要合并为 skuName*Num 的形式，顿号分割
        skuNameMapper.forEach((key, value) -> {
            Integer num = skuNumMapper.get(key);
            names.add(String.format("%s*%d", value, num));
        });

        names.sort(Comparator.naturalOrder());
        String ret = names.stream().reduce((a, b) -> a + "；" + b).orElse("");

        // 替换特殊字符
        // 去除emoji
        ret = ret.replaceAll("[\\ud800\\udc00-\\udbff\\udfff\\ud800-\\udfff]", "");

        // 去除 +
        ret = ret.replaceAll("\\+", "加");

        return ret;
    }
}
