package com.leway.modules.order.service.dto;

import com.leway.serializer.BigDecimalSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 订单价格相关信息组
 * 
 * @ClassName PriceGroup
 * <AUTHOR>
 * @Date 2019/10/27
 **/
@Data
public class PriceGroupDto {

    /**
     * 包邮金额
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal storeFreePostage;

    /**
     * 邮费
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal storePostage;

    /**
     * 商品总价
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalPrice;

    /**
     * 保价总价
     */
    @JsonSerialize(using = BigDecimalSerializer.class)
    private BigDecimal totalInsurePrice;

    /**
     * VIP 价格
     */
    // @JsonSerialize(using = BigDecimalSerializer.class)
    // private BigDecimal vipPrice;

}
