package com.leway.modules.order.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 运费模板数据
 * @ClassName TemplateDTO
 * <AUTHOR>
 * @Date 2020/5/28
 **/
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TemplateDto {
    /**
     * 数量
     */
    private Double number;
    /**
     * 价格
     */
    private BigDecimal price;
    /**
     * 首件数量
     */
    private Double first;
    /**
     * 首件运费价格
     */
    private BigDecimal firstPrice;
    /**
     * 续件数量
     */
    private Double _continue;
    /**
     * 续件运费价格
     */
    private BigDecimal continuePrice;
    /**
     * 运费模板ID
     */
    private Integer tempId;
    /**
     * 城市ID
     */
    private Integer cityId;
}
