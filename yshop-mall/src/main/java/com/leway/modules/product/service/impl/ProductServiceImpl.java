/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>
 * 注意：
 * 本软件为************开发研制，未经购买不得使用
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.leway.modules.product.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.pagehelper.PageInfo;
import com.leway.api.YshopException;
import com.leway.common.service.impl.BaseServiceImpl;
import com.leway.common.util.SnowFlakeService;
import com.leway.common.utils.QueryHelpPlus;
import com.leway.constant.ShopConstants;
import com.leway.constant.SystemConfigConstants;
import com.leway.dozer.service.IGenerator;
import com.leway.enums.CommonEnum;
import com.leway.enums.ProductEnum;
import com.leway.enums.ShopCommonEnum;
import com.leway.enums.SortEnum;
import com.leway.exception.BadRequestException;
import com.leway.modules.category.domain.YxStoreCategory;
import com.leway.modules.category.service.YxStoreCategoryService;
import com.leway.modules.inventory.domain.InventoryRecord;
import com.leway.modules.inventory.domain.InventoryTransaction;
import com.leway.modules.inventory.service.InventoryRecordService;
import com.leway.modules.inventory.service.InventoryTransactionService;
import com.leway.modules.inventory.vo.InventoryTranscationParam;
import com.leway.modules.product.domain.Product;
import com.leway.modules.product.domain.ProductRelation;
import com.leway.modules.product.domain.SKU;
import com.leway.modules.product.param.ProductQueryParam;
import com.leway.modules.product.service.ProductAttrService;
import com.leway.modules.product.service.ProductRelationService;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.product.service.SKUService;
import com.leway.modules.product.service.TmpInventoryService;
import com.leway.modules.product.service.dto.AdminProductDto;
import com.leway.modules.product.service.dto.DetailDto;
import com.leway.modules.product.service.dto.FromatDetailDto;
import com.leway.modules.product.service.dto.SkuFormatDto;
import com.leway.modules.product.service.dto.ProductResultDto;
import com.leway.modules.product.service.dto.StoreProductDto;
import com.leway.modules.product.service.dto.YxStoreProductQueryCriteria;
import com.leway.modules.product.service.mapper.StoreProductMapper;
import com.leway.modules.product.vo.MaxSalesParam;
import com.leway.modules.product.vo.ProductAttrQueryVo;
import com.leway.modules.product.vo.ProductQueryVo;
import com.leway.modules.product.vo.ProductVo;
import com.leway.modules.shop.service.YxSystemStoreService;
import com.leway.modules.template.domain.YxShippingTemplates;
import com.leway.modules.template.service.YxShippingTemplatesService;
import com.leway.modules.user.service.YxUserService;
import com.leway.utils.RedisLock;
import com.leway.utils.RedisUtil;
import com.leway.utils.RedisUtils;
import com.leway.utils.RegexUtil;
import com.qiniu.util.StringUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2020-05-12
 */
@Slf4j
@Service
@SuppressWarnings("unchecked")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class ProductServiceImpl extends BaseServiceImpl<StoreProductMapper, Product>
        implements ProductService {

    @Autowired
    private IGenerator generator;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private StoreProductMapper storeProductMapper;

    @Autowired
    private YxStoreCategoryService yxStoreCategoryService;

    @Autowired
    private ProductAttrService productAttrService;

    @Autowired
    private SKUService skuService;

    @Autowired
    private YxUserService userService;

    @Autowired
    private RedisLock redisLock;

    @Autowired
    private SnowFlakeService snowFlakeService;

    // @Autowired
    // private ProductReplyService replyService;

    @Autowired
    private ProductRelationService relationService;

    @Autowired
    private YxSystemStoreService systemStoreService;

    @Autowired
    private YxShippingTemplatesService shippingTemplatesService;

    @Autowired
    private InventoryRecordService inventoryRecordService;

    @Autowired
    private TmpInventoryService tmpInventoryService;

    @Autowired
    private InventoryTransactionService inventoryTransactionService;

    /**
     * 增加实时库存 减少销量
     *
     * @param num       数量
     * @param productId 商品id
     * @param unique    sku唯一值
     */
    @Override
    public void incSkuStock(Integer num, Long productId, String unique, String orderId) {
        // 处理sku库存
        if (StrUtil.isNotEmpty(unique)) {
            productAttrService.incProductAttrStock(num, productId, unique, orderId);
        } else {
            throw new YshopException("SKU unique 为空");
        }
        // 处理商品库存
        // storeProductMapper.incStockDecSales(num, productId);

    }

    /**
     * 库存不变 减少销量
     * 
     * @param num       数量
     * @param productId 商品id
     * @param unique    sku唯一值
     */
    @Override
    public void keepSkuStockDecSales(Integer num, Long productId, String unique, String orderId) {
        // 处理sku库存
        if (StrUtil.isNotEmpty(unique)) {
            productAttrService.keepSkuStockDecSales(num, productId, unique, orderId);
        } else {
            throw new YshopException("SKU unique 为空");
        }
    }

    /**
     * 减少库存与增加销量
     *
     * @param num       数量
     * @param productId 商品id
     * @param unique    sku
     */
    @Override
    public void decSkuStock(int num, Long productId, String unique, String orderId) {
        // 处理sku库存
        if (StrUtil.isNotEmpty(unique)) {
            productAttrService.decProductAttrStock(num, productId, unique, orderId);
        } else {
            throw new YshopException("SKU unique 为空");
        }
    }

    @Override
    public Product getProductInfo(Long id) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getId, id);
        Product storeProduct = this.baseMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new YshopException(1, "商品不存在或已下架");
        }

        if (storeProduct.getIsDel().equals(ShopCommonEnum.DEFAULT_1.getValue())) {
            throw new YshopException(1, "商品已删除");
        }

        if (storeProduct.getIsShow().equals(ShopCommonEnum.SALE_STS_OFFSALE.getValue())) {
            throw new YshopException(1, "商品已下架");
        }

        return storeProduct;
    }

    /**
     * 获取单个商品
     *
     * @param id 商品id
     * @return YxStoreProductQueryVo
     */
    @Override
    public ProductQueryVo getStoreProductById(Long id) {
        return generator.convert(this.baseMapper.selectById(id), ProductQueryVo.class);
    }

    /**
     * 返回普通商品库存
     *
     * @param productId 商品id
     * @param unique    sku唯一值
     * @return int
     */
    @Override
    public int getProductStock(Long productId, String unique) {
        SKU storeProductAttrValue = skuService
                .getOne(Wrappers.<SKU>lambdaQuery()
                        .eq(SKU::getUnique, unique)
                        .eq(SKU::getProductId, productId));

        if (storeProductAttrValue == null) {
            log.warn("商品没有库存信息, productID: {} unique: {} ", productId, unique);
            return 0;
        }

        return storeProductAttrValue.getStock();

    }

    @Override
    public SKU getSKU(Long productId, String unique) {
        return skuService.getOne(Wrappers.<SKU>lambdaQuery()
                .eq(SKU::getUnique, unique)
                .eq(SKU::getProductId, productId));
    }

    /**
     * 商品列表
     *
     * @param productQueryParam YxStoreProductQueryParam
     * @return list
     */
    @Override
    public List<ProductQueryVo> getGoodsList(ProductQueryParam productQueryParam) {

        // LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        // wrapper.eq(Product::getIsShow, CommonEnum.SHOW_STATUS_1.getValue());
        // wrapper.eq(Product::getIsDel, CommonEnum.DEL_STATUS_0.getValue());
        // // wrapper.eq(YxStoreProduct::getIsIntegral,
        // // CommonEnum.SHOW_STATUS_1.getValue());

        // // 多字段模糊查询分类搜索
        // if (StrUtil.isNotBlank(productQueryParam.getSid()) &&
        // !ShopConstants.YSHOP_ZERO.equals(productQueryParam.getSid())) {
        // wrapper.eq(Product::getCateId, productQueryParam.getSid());
        // }
        // // 关键字搜索
        // if (StrUtil.isNotEmpty(productQueryParam.getKeyword())) {
        // wrapper.and(wrapper1 -> {
        // wrapper1.or();
        // wrapper1.like(Product::getStoreName, productQueryParam.getKeyword());
        // wrapper1.or();
        // wrapper1.like(Product::getStoreInfo, productQueryParam.getKeyword());
        // wrapper1.or();
        // wrapper1.like(Product::getKeyword, productQueryParam.getKeyword());
        // });
        // }
        // // 新品搜索
        // if (StrUtil.isNotBlank(productQueryParam.getNews()) &&
        // !ShopConstants.YSHOP_ZERO.equals(productQueryParam.getNews())) {
        // wrapper.eq(Product::getIsNew, ShopCommonEnum.IS_NEW_1.getValue());
        // }

        // // 销量排序
        // if (SortEnum.DESC.equalsTo(productQueryParam.getSalesOrder())) {
        // wrapper.orderByDesc(Product::getSales);
        // } else if (SortEnum.ASC.equalsTo(productQueryParam.getSalesOrder())) {
        // wrapper.orderByAsc(Product::getSales);
        // }

        // // 价格排序
        // if (SortEnum.DESC.equalsTo(productQueryParam.getPriceOrder())) {
        // wrapper.orderByDesc(Product::getPrice);
        // } else if (SortEnum.ASC.equalsTo(productQueryParam.getPriceOrder())) {
        // wrapper.orderByAsc(Product::getPrice);
        // }

        // // 无其他排序条件时,防止因为商品排序导致商品重复
        // if (StringUtils.isNullOrEmpty(productQueryParam.getPriceOrder())
        // && StringUtils.isNullOrEmpty(productQueryParam.getSalesOrder())) {
        // wrapper.orderByDesc(Product::getId);
        // wrapper.orderByDesc(Product::getSort);
        // }
        // Page<Product> pageModel = new Page<>(productQueryParam.getPage(),
        // productQueryParam.getLimit());

        // IPage<Product> pageList = storeProductMapper.selectPage(pageModel, wrapper);

        // List<ProductQueryVo> list = generator.convert(pageList.getRecords(),
        // ProductQueryVo.class);

        List<ProductQueryVo> list = new ArrayList<>();
        return list;
    }

    /**
     * 商品详情
     *
     * @param id        商品id
     * @param uid       用户id
     * @param latitude  纬度
     * @param longitude 经度
     * @return ProductVo
     */
    @Override
    public ProductVo goodsDetail(Long id, Long uid, String latitude, String longitude) {
        // 0. 获取商品
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Product::getId, id);

        Product storeProduct = storeProductMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new YshopException(1, "商品不存在或已下架");
        }

        // 2. 获取商品sku
        // 包含商品规格组属性，以及这些属性组合后对应的sku
        Map<String, Object> returnMap = productAttrService.getProductAttrDetail(id);

        ProductVo productVo = new ProductVo();
        ProductQueryVo storeProductQueryVo = generator.convert(storeProduct, ProductQueryVo.class);

        // 设置销量
        storeProductQueryVo.setSales(storeProductQueryVo.getSales() + storeProductQueryVo.getFicti());

        if (uid.longValue() > 0) {
            // 设置VIP价格
            // double vipPrice = userService.setLevelPrice(
            // storeProductQueryVo.getPrice().doubleValue(), uid);
            // storeProductQueryVo.setVipPrice(BigDecimal.valueOf(vipPrice));

            // 收藏
            // boolean isCollect = relationService.isProductCollectRelation(id, uid);
            // storeProductQueryVo.setUserCollect(isCollect);

        }
        // 总条数
        // Long totalCount = replyService.productReplyCount(id);
        // productVo.setReplyCount(totalCount);

        // 评价
        // ProductReplyQueryVo storeProductReplyQueryVo = replyService.getReply(id);
        // productVo.setReply(storeProductReplyQueryVo);

        // 好评比例
        // String replyPer = replyService.replyPer(id);
        // productVo.setReplyChance(replyPer);

        // 获取运费模板名称
        String tempName = "";
        if (SystemConfigConstants.STORE_FREE_POSTAGE_AMOUNT.compareTo(BigDecimal.ZERO) == 0) {
            tempName = "全国包邮";
        } else {
            YxShippingTemplates shippingTemplates = shippingTemplatesService.getById(storeProduct.getTempId());
            if (ObjectUtil.isNotNull(shippingTemplates)) {
                tempName = shippingTemplates.getName();
                log.info("运费模板名称: {}", tempName);
            } else {
                throw new BadRequestException("请配置运费模板");
            }

        }
        productVo.setTempName(tempName);

        // 设置商品相关信息
        productVo.setStoreInfo(storeProductQueryVo);
        productVo.setProductAttr((List<ProductAttrQueryVo>) returnMap.get("productAttr"));

        Map<String, SKU> productValueMap = (Map<String, SKU>) returnMap.get("productValue");

        Map<String, SKU> filledProductValueMap = new HashMap<String, SKU>();
        if (uid > 0) {
            // 缺货登记
            for (Map.Entry<String, SKU> entry : productValueMap.entrySet()) {
                SKU sku = entry.getValue();
                ProductRelation r = relationService.productWantRelation(id, uid, sku.getUnique());
                boolean want = false;
                if (r != null) {
                    want = r.getStatus() != null && r.getStatus() == ProductRelation.STS_UNSEND;
                }
                sku.setUserWant(want);
                entry.setValue(sku);
                filledProductValueMap.put(entry.getKey(), sku);
            }
        } else {
            filledProductValueMap = productValueMap;
        }
        productVo.setProductValue(filledProductValueMap);

        // 门店
        // productVo.setSystemStore(systemStoreService.getStoreInfo(latitude,
        // longitude));
        // productVo.setMapKey(RedisUtil.get(ShopKeyUtils.getTengXunMapKey()));

        if (uid.longValue() > 0) {
            // 添加足迹更新
            /*
             * 暂时不记录用户浏览足迹
             * YxStoreProductRelation foot = relationService.getOne(new
             * LambdaQueryWrapper<YxStoreProductRelation>()
             * .eq(YxStoreProductRelation::getUid, uid)
             * .eq(YxStoreProductRelation::getProductId, storeProductQueryVo.getId())
             * .eq(YxStoreProductRelation::getType, YxStoreProductRelation.PR_FOOT));
             *
             * if (ObjectUtil.isNotNull(foot)) {
             * foot.setCreateTime(new Date());
             * relationService.saveOrUpdate(foot);
             * } else {
             * YxStoreProductRelation storeProductRelation = new YxStoreProductRelation();
             * storeProductRelation.setProductId(storeProductQueryVo.getId());
             * storeProductRelation.setUid(uid);
             * storeProductRelation.setCreateTime(new Date());
             * storeProductRelation.setType(YxStoreProductRelation.PR_FOOT);
             * relationService.save(storeProductRelation);
             * }
             */
        }

        return productVo;
    }

    /**
     * 商品浏览量
     *
     * @param productId
     */
    @Override
    public long incBrowseNum(Long productId) {
        // 无浏览量显示
        // storeProductMapper.incBrowseNum(productId);

        return redisUtils.increaseProductViews(productId);
    }

    /**
     * 商品列表
     *
     * @param page  页码
     * @param limit 条数
     * @param type  ProductEnum
     * @return List
     */
    // @Override
    // public List<ProductQueryVo> getList(int page, int limit, int type) {
    // LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
    // wrapper.eq(Product::getIsShow, ShopCommonEnum.SALE_STS_ONSALE.getValue())
    // .eq(Product::getIsDel, ShopCommonEnum.DELETE_0.getValue())
    // .orderByDesc(Product::getSort);
    // // order
    // switch (ProductEnum.toType(type)) {
    // // 精品推荐
    // case TYPE_1:
    // wrapper.eq(Product::getIsBest,
    // ShopCommonEnum.IS_STATUS_YES.getValue());
    // break;
    // // 热门榜单
    // case TYPE_2:
    // wrapper.eq(Product::getIsHot,
    // ShopCommonEnum.IS_STATUS_YES.getValue());
    // break;
    // // 首发新品
    // case TYPE_3:
    // wrapper.eq(Product::getIsNew,
    // ShopCommonEnum.IS_STATUS_YES.getValue());
    // break;
    // // 猜你喜欢
    // case TYPE_4:
    // wrapper.eq(Product::getIsBenefit,
    // ShopCommonEnum.IS_STATUS_YES.getValue());
    // break;
    // default:
    // }
    // Page<Product> pageModel = new Page<>(page, limit);

    // IPage<Product> pageList = storeProductMapper.selectPage(pageModel, wrapper);

    // return generator.convert(pageList.getRecords(), ProductQueryVo.class);
    // }

    /**
     * 在售商品
     *
     * @param parentCategoryID 类型:出品/发行
     */
    // @Override
    // public List<ProductQueryVo> getOnSaleList(int page, int limit, int
    // parentCategoryID) {
    // LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
    // // 上架
    // wrapper.eq(Product::getIsShow, ShopCommonEnum.SALE_STS_ONSALE.getValue())
    // // 未删除
    // .eq(Product::getIsDel, ShopCommonEnum.DELETE_0.getValue())
    // // 非预发布状态
    // .eq(Product::getPrePublishStatus, ShopConstants.NORMAL)
    // // 可售
    // .eq(Product::getIsHide, ShopCommonEnum.VISIBLE_STS_SHOW.getValue())
    // .orderByAsc(Product::getSort);
    // if (parentCategoryID != 0) {
    // wrapper = wrapper.eq(Product::getCate1stId,
    // parentCategoryID);
    // }
    // Page<Product> pageModel = new Page<>(page, limit);

    // IPage<Product> pageList = storeProductMapper.selectPage(pageModel, wrapper);

    // return generator.convert(pageList.getRecords(), ProductQueryVo.class);
    // }

    /**
     * 首页在售商品列表
     * 
     * @param page
     * @return List
     */
    @Override
    public List<ProductQueryVo> getIndexPageListByCat(int page, int limit, String categoryID) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        // 上架
        wrapper.eq(Product::getIsShow, ShopCommonEnum.SALE_STS_ONSALE.getValue())
                // .eq(Product::getIsShelf, ShopCommonEnum.SHELF_STS_FEATURED.getValue())
                // 未删除
                .eq(Product::getIsDel, ShopCommonEnum.DELETE_0.getValue())
                // 非预发布状态
                .eq(Product::getPrePublishStatus, ShopConstants.NORMAL)
                // 可售
                .eq(Product::getIsHide, ShopCommonEnum.VISIBLE_STS_SHOW.getValue())
                .orderByAsc(Product::getSort);

        if (com.leway.utils.StringUtils.isNotEmpty(categoryID)) {
            wrapper = wrapper.eq(Product::getCateId,
                    categoryID);
        }
        Page<Product> pageModel = new Page<>(page, limit);

        IPage<Product> pageList = storeProductMapper.selectPage(pageModel, wrapper);

        return generator.convert(pageList.getRecords(), ProductQueryVo.class);
    }

    @Override
    public List<ProductQueryVo> getRecList() {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(
                // 在售
                Product::getIsShow, ShopCommonEnum.SALE_STS_ONSALE.getValue())
                // 没有删除
                .eq(Product::getIsDel, ShopCommonEnum.DELETE_0.getValue())
                // 非预发布状态
                .eq(Product::getPrePublishStatus, ShopConstants.NORMAL)
                // 没有隐藏
                .eq(Product::getIsHide, ShopCommonEnum.VISIBLE_STS_SHOW.getValue())
                // 推荐
                .eq(Product::getIsBest,
                        ShopCommonEnum.IS_STATUS_YES.getValue())
                // 推荐排序
                .orderByDesc(Product::getId);
        int page = 0;
        int limit = 1000;
        Page<Product> pageModel = new Page<>(page, limit);

        IPage<Product> pageList = storeProductMapper.selectPage(pageModel,
                wrapper);

        List<Product> productList = pageList.getRecords();
        long seed = System.currentTimeMillis();
        Collections.shuffle(productList, new Random(seed));

        return generator.convert(productList, ProductQueryVo.class);
    }

    // ============ 分割线================//

    /**
     * 管理后台：商品查询
     *
     * @return {
     *         content: List<AdminProductDto>,
     *         totalElements: total
     *         }
     */
    @Override
    public Map<String, Object> queryAll(YxStoreProductQueryCriteria criteria, Pageable pageable) {
        getPageByStartPage0(pageable);
        PageInfo<Product> page = new PageInfo<>(queryAll(criteria));
        Map<String, Object> map = new LinkedHashMap<>(2);

        List<Product> productList = page.getList();
        productList = mashupSKUInfo(productList);

        List<AdminProductDto> contentList = generator.convert(productList, AdminProductDto.class);

        for (int i = 0; i < productList.size(); i++) {
            Product p = productList.get(i);
            Map<String, Integer> tmpSkuStocks = new LinkedHashMap();
            Map<String, Integer> skuInventoryQuantity = new LinkedHashMap();
            for (SKU sku : p.getSkuList()) {
                // 临时库存
                List<Integer> tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
                int totalTmpStock = tmpStocks.stream() // 创建流
                        .mapToInt(Integer::intValue) // 将Integer转换为int
                        .sum(); // 对int进行求和

                tmpSkuStocks.put(sku.getUnique(), totalTmpStock);

                // 库存
                InventoryRecord record = inventoryRecordService.findBySkuId(sku.getId());
                if (record == null) {
                    skuInventoryQuantity.put(sku.getUnique(), 0);
                } else {
                    skuInventoryQuantity.put(sku.getUnique(), record.getCurrentQuantity());
                }
            }

            AdminProductDto apDto = contentList.get(i);
            apDto.setTmpSkuStocks(tmpSkuStocks);
            apDto.setSkuInventoryQuantity(skuInventoryQuantity);
            contentList.set(i, apDto);
        }

        // 商品
        map.put("content", contentList);
        // 数量
        map.put("totalElements", page.getTotal());
        return map;
    }

    // @Override
    // public Map<String, Object> queryAllBySort(YxStoreProductQueryCriteria
    // criteria, Pageable pageable) {
    // getPage(pageable);

    // QueryWrapper<Product> qw = QueryHelpPlus.getPredicate(Product.class,
    // criteria);
    // qw = qw.orderByAsc("sort");

    // PageInfo<Product> page = new PageInfo<>(queryAll(qw));
    // Map<String, Object> map = new LinkedHashMap<>(2);

    // List<Product> productList = page.getList();
    // productList = mashupSKUInfo(productList);

    // List<AdminProductDto> contentList = generator.convert(productList,
    // AdminProductDto.class);

    // map.put("content", contentList);
    // map.put("totalElements", page.getTotal());
    // return map;
    // }

    @Override
    public List<Product> queryAll(YxStoreProductQueryCriteria criteria) {
        QueryWrapper<Product> queryWrapper = QueryHelpPlus.getPredicate(Product.class, criteria);
        List<Product> productList = baseMapper
                .selectList(queryWrapper);
        productList.forEach(yxStoreProduct -> {
            // 附带商品分类
            YxStoreCategory yxStoreCategory = yxStoreCategoryService.getById(yxStoreProduct.getCateId());
            if (yxStoreCategory != null) {
                yxStoreProduct.setStoreCategory(yxStoreCategory);
            } else {
                yxStoreCategory = new YxStoreCategory();
                yxStoreCategory.setCateName("未分类");
                yxStoreProduct.setStoreCategory(yxStoreCategory);
            }
        });
        return productList;
    }

    /**
     * 补充SKU信息
     * 
     * @param list
     * @return
     */
    @Override
    public List<Product> mashupSKUInfo(List<Product> list) {
        list = list.stream().map(p -> mashupSKUInfo(p)).collect(Collectors.toList());
        return list;
    }

    /**
     * 补充SKU信息
     * 
     * @param p
     * @return
     */
    @Override
    public Product mashupSKUInfo(Product p) {
        List<SKU> skuList = skuService.getByProductId(p.getId());
        p.setSkuList(skuList);
        return p;
    }

    // @Override
    // public void download(List<AdminProductDto> all, HttpServletResponse
    // response) throws IOException {
    // List<Map<String, Object>> list = new ArrayList<>();
    // for (AdminProductDto yxStoreProduct : all) {
    // Map<String, Object> map = new LinkedHashMap<>();
    // map.put("商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)", yxStoreProduct.getMerId());
    // map.put("商品图片", yxStoreProduct.getImage());
    // map.put("轮播图", yxStoreProduct.getSliderImage());
    // map.put("商品名称", yxStoreProduct.getStoreName());
    // map.put("商品简介", yxStoreProduct.getStoreInfo());
    // map.put("关键字", yxStoreProduct.getKeyword());
    // map.put("产品条码（一维码）", yxStoreProduct.getBarCode());
    // map.put("分类id", yxStoreProduct.getCateId());
    // map.put("商品价格", yxStoreProduct.getPrice());
    // map.put("会员价格", yxStoreProduct.getVipPrice());
    // map.put("市场价", yxStoreProduct.getOtPrice());
    // map.put("邮费", yxStoreProduct.getPostage());
    // map.put("单位名", yxStoreProduct.getUnitName());
    // map.put("排序", yxStoreProduct.getSort());
    // map.put("销量", yxStoreProduct.getSales());
    // map.put("库存", yxStoreProduct.getStock());
    // map.put("状态（0：未上架，1：上架）", yxStoreProduct.getIsShow());
    // map.put("是否热卖", yxStoreProduct.getIsHot());
    // map.put("是否优惠", yxStoreProduct.getIsBenefit());
    // map.put("是否精品", yxStoreProduct.getIsBest());
    // map.put("是否新品", yxStoreProduct.getIsNew());
    // map.put("产品描述", yxStoreProduct.getDescription());
    // map.put("添加时间", yxStoreProduct.getAddTime());
    // map.put("是否包邮", yxStoreProduct.getIsPostage());
    // map.put("是否删除", yxStoreProduct.getIsDel());
    // map.put("商户是否代理 0不可代理1可代理", yxStoreProduct.getMerUse());
    // map.put("获得积分", yxStoreProduct.getPrice());
    // map.put("成本价", yxStoreProduct.getCost());
    // map.put("秒杀状态 0 未开启 1已开启", yxStoreProduct.getIsSeckill());
    // map.put("是否优品推荐", yxStoreProduct.getIsGood());
    // map.put("虚拟销量", yxStoreProduct.getFicti());
    // map.put("浏览量", yxStoreProduct.getBrowse());
    // map.put("产品二维码地址(用户小程序海报)", yxStoreProduct.getCodePath());
    // map.put("淘宝京东1688类型", yxStoreProduct.getSoureLink());
    // list.add(map);
    // }
    // FileUtil.downloadExcel(list, response);
    // }

    /**
     * 商品上架下架
     *
     * @param id     商品id
     * @param status ShopCommonEnum
     */
    @Override
    public void onSale(Long id, Integer status) {
        if (ShopCommonEnum.SALE_STS_ONSALE.equalsTo(status)) {
            status = ShopCommonEnum.SALE_STS_OFFSALE.getValue();
        } else {
            status = ShopCommonEnum.SALE_STS_ONSALE.getValue();
        }
        storeProductMapper.updateOnsale(status, id);
    }

    /**
     * 隐藏、显示
     * 
     * @param id
     * @param status
     */
    @Override
    public void switchVisible(Long id, Integer status) {
        if (ShopCommonEnum.VISIBLE_STS_SHOW.equalsTo(status)) {
            status = ShopCommonEnum.VISIBLE_STS_HIDE.getValue();
        } else {
            status = ShopCommonEnum.VISIBLE_STS_SHOW.getValue();
        }
        storeProductMapper.updateHide(status, id);
    }

    /**
     * 新增/保存商品
     * 这个方法不修改库存、不修改销售限额
     *
     * @param storeProductDto 商品
     */
    @Override
    public void insertAndEditYxStoreProduct(StoreProductDto storeProductDto) {
        storeProductDto.setDescription(RegexUtil.converProductDescription(storeProductDto.getDescription()));
        ProductResultDto resultDTO = this.computedProduct(storeProductDto.getAttrs());

        // 添加商品
        Product product = new Product();
        BeanUtil.copyProperties(storeProductDto, product, "sliderImage");
        if (storeProductDto.getSliderImage().isEmpty()) {
            throw new YshopException("请上传轮播图");
        }

        // 使用sku的数据，设置spu
        product.setPrice(BigDecimal.valueOf(resultDTO.getMinPrice()));
        product.setOtPrice(BigDecimal.valueOf(resultDTO.getMinOtPrice()));
        // yxStoreProduct.setCost(BigDecimal.valueOf(resultDTO.getMinCost()));
        // 交给别的方法修改库存，防止并发问题
        // product.setStock(resultDTO.getStock());

        product.setSliderImage(String.join(",", storeProductDto.getSliderImage()));

        // 设置预发布
        product.setPrePublishStatus(storeProductDto.getPrePublishStatus());
        product.setPrePublishTime(storeProductDto.getPrePublishTime());

        // if (storeProductDto.getId() != null) {
        // // 清空商品转发图
        // deleteForwardImg(storeProductDto.getId());
        // }

        boolean succ = this.saveOrUpdate(product);
        log.info("新增或更新商品成功: {} pid:{}", succ, product.getId());
        if (storeProductDto.getId() == null || storeProductDto.getId() == 0) {
            storeProductDto.setId(product.getId());
        }

        // 属性处理
        if (storeProductDto.isSingleSpec()) {
            // 处理单SKU
            // 单SKU会默认规格

            String attrSpec = "规格";
            String sku = "默认";

            // 属性
            FromatDetailDto formatDetailDto = FromatDetailDto.builder()
                    .value(attrSpec)
                    .detailValue("")
                    .attrHidden("")
                    .detail(ListUtil.toList(sku))
                    .build();

            // SKU List 属性组合后结果
            List<SkuFormatDto> attrs = storeProductDto.getAttrs();

            // 属性值(SKU)
            SkuFormatDto productFormatDto = attrs.get(0);
            productFormatDto.setSku(sku);

            Map<String, String> map = new HashMap<>();
            map.put(attrSpec, sku);
            productFormatDto.setDetail(map);

            productAttrService.insertYxStoreProductAttr(ListUtil.toList(formatDetailDto),
                    ListUtil.toList(productFormatDto), product.getId());
        } else {
            productAttrService.insertYxStoreProductAttr(storeProductDto.getItems(),
                    storeProductDto.getAttrs(), product.getId());
        }

        // 建立库存记录
        List<SKU> skuList = skuService
                .list(new LambdaQueryWrapper<SKU>().eq(SKU::getProductId, storeProductDto.getId()));
        // int depotId = storeProductDto.getDepotId();
        Long productId = storeProductDto.getId();
        for (SKU sku : skuList) {
            InventoryRecord record = inventoryRecordService.findBySkuId(sku.getId());
            if (record == null) {
                // 新增存货记录
                record = new InventoryRecord();
                record.setSpuId(productId);
                record.setSkuId(sku.getId());
                record.setCurrentQuantity(0);
                // record.setWarehouseId(depotId);
                record = inventoryRecordService.saveInventoryRecord(record);
            }
        }
    }

    /**
     * 获取生成的属性
     *
     * @param id      商品id
     * @param jsonStr jsonStr
     * @return map
     */
    @Override
    public Map<String, Object> getFormatAttr(Long id, String jsonStr, boolean isActivity) {
        JSONObject jsonObject = JSON.parseObject(jsonStr);
        Map<String, Object> resultMap = new LinkedHashMap<>(3);

        if (jsonObject == null || jsonObject.get("attrs") == null || jsonObject.getJSONArray("attrs").isEmpty()) {
            resultMap.put("attr", new ArrayList<>());
            resultMap.put("value", new ArrayList<>());
            resultMap.put("header", new ArrayList<>());
            return resultMap;
        }

        List<FromatDetailDto> formatDetailDTOList = JSON.parseArray(jsonObject.get("attrs").toString(),
                FromatDetailDto.class);

        // formatDetailDTOList
        DetailDto detailDto = this.attrFormat(formatDetailDTOList);

        List<Map<String, Object>> headerMapList = null;
        List<Map<String, Object>> valueMapList = new ArrayList<>();
        String align = "center";
        Map<String, Object> headerMap = new LinkedHashMap<>();
        for (Map<String, Map<String, String>> map : detailDto.getRes()) {
            Map<String, String> detail = map.get("detail");
            String[] detailArr = detail.values().toArray(new String[] {});
            Arrays.sort(detailArr);

            String sku = String.join(",", detailArr);

            Map<String, Object> valueMap = new LinkedHashMap<>();

            List<String> detailKeys = detail.entrySet()
                    .stream()
                    .map(Map.Entry::getKey)
                    .collect(Collectors.toList());

            int i = 0;
            headerMapList = new ArrayList<>();
            for (String title : detailKeys) {
                headerMap.put("title", title);
                headerMap.put("minWidth", "130");
                headerMap.put("align", align);
                headerMap.put("key", "value" + (i + 1));
                headerMap.put("slot", "value" + (i + 1));
                headerMapList.add(ObjectUtil.clone(headerMap));
                i++;
            }

            String[] detailValues = detail.values().toArray(new String[] {});
            for (int j = 0; j < detailValues.length; j++) {
                String key = "value" + (j + 1);
                valueMap.put(key, detailValues[j]);
            }
            // /** 拼团属性对应的金额 */
            // private BigDecimal pinkPrice;
            //
            // /** 秒杀属性对应的金额 */
            // private BigDecimal seckillPrice;
            // /** 拼团库存属性对应的库存 */
            // private Integer pinkStock;
            //
            // private Integer seckillStock;
            valueMap.put("detail", detail);
            valueMap.put("sku", "");
            valueMap.put("pic", "");
            valueMap.put("price", 0);
            // 暂时去掉成本价
            // valueMap.put("cost", 0);
            valueMap.put("ot_price", 0);
            valueMap.put("stock", 0);
            valueMap.put("bar_code", "");
            valueMap.put("weight", 0);
            valueMap.put("volume", 0);
            valueMap.put("integral", 0);
            valueMap.put("limitBuy", 0);
            if (id > 0) {
                SKU storeProductAttrValue = skuService
                        .getOne(Wrappers.<SKU>lambdaQuery()
                                .eq(SKU::getProductId, id)
                                .eq(SKU::getSku, sku));
                if (storeProductAttrValue != null) {
                    valueMap.put("sku", storeProductAttrValue.getSku());
                    valueMap.put("pic", storeProductAttrValue.getImage());
                    valueMap.put("price", storeProductAttrValue.getPrice());
                    // valueMap.put("cost", storeProductAttrValue.getCost());
                    valueMap.put("ot_price", storeProductAttrValue.getOtPrice());
                    valueMap.put("stock", storeProductAttrValue.getStock());
                    valueMap.put("bar_code", storeProductAttrValue.getBarCode());
                    valueMap.put("weight", storeProductAttrValue.getWeight());
                    valueMap.put("volume", storeProductAttrValue.getVolume());
                    valueMap.put("limitBuy", storeProductAttrValue.getLimitBuy());
                    // valueMap.put("integral", storeProductAttrValue.getIntegral());
                }
            }

            valueMapList.add(ObjectUtil.clone(valueMap));

        }

        this.addMap(headerMap, headerMapList, align, isActivity);

        resultMap.put("attr", formatDetailDTOList);
        resultMap.put("value", valueMapList);
        resultMap.put("header", headerMapList);

        return resultMap;
    }

    /**
     * 计算产品数据
     *
     * @param attrs attrs
     * @return ProductResultDto
     */
    private ProductResultDto computedProduct(List<SkuFormatDto> attrs) {
        // 取最小价格
        Double minPrice = attrs
                .stream()
                .map(SkuFormatDto::getPrice)
                .min(Comparator.naturalOrder())
                .orElse(0d);

        // 取最小积分
        Integer minIntegral = attrs
                .stream()
                .map(SkuFormatDto::getIntegral)
                .min(Comparator.naturalOrder())
                .orElse(0);

        Double minOtPrice = attrs
                .stream()
                .map(SkuFormatDto::getOtPrice)
                .min(Comparator.naturalOrder())
                .orElse(0d);

        // Double minCost = attrs
        // .stream()
        // .map(ProductFormatDto::getCost)
        // .min(Comparator.naturalOrder())
        // .orElse(0d);

        // ERP 和商城联动
        // 计算库存
        Integer stock = attrs
                .stream()
                .map(SkuFormatDto::getStock)
                .reduce(Integer::sum)
                .orElse(0);

        if (stock < 0) {
            throw new YshopException("库存不能低于0");
        }

        return ProductResultDto.builder()
                .minPrice(minPrice)
                .minOtPrice(minOtPrice)
                // .minCost(minCost)
                .stock(stock)
                .minIntegral(minIntegral)
                .build();
    }

    /**
     * mapToBean
     *
     * @param listMap listMap
     * @return list
     */
    private List<SkuFormatDto> ListMapToListBean(List<Map<String, Object>> listMap) {
        List<SkuFormatDto> list = new ArrayList<>();
        // 循环遍历出map对象
        for (Map<String, Object> m : listMap) {
            list.add(BeanUtil.fillBeanWithMap(m, new SkuFormatDto(), true));
        }
        return list;
    }

    /**
     * 增加表头
     *
     * @param headerMap     headerMap
     * @param headerMapList headerMapList
     * @param align         align
     */
    private void addMap(Map<String, Object> headerMap, List<Map<String, Object>> headerMapList, String align,
            boolean isActivity) {
        headerMap.put("title", "图片");
        headerMap.put("slot", "pic");
        headerMap.put("align", align);
        headerMap.put("minWidth", 80);
        headerMapList.add(ObjectUtil.clone(headerMap));

        headerMap.put("title", "售价");
        headerMap.put("slot", "price");
        headerMap.put("align", align);
        headerMap.put("minWidth", 120);
        headerMapList.add(ObjectUtil.clone(headerMap));

        // headerMap.put("title", "成本价");
        // headerMap.put("slot", "cost");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));

        headerMap.put("title", "原价");
        headerMap.put("slot", "ot_price");
        headerMap.put("align", align);
        headerMap.put("minWidth", 140);
        headerMapList.add(ObjectUtil.clone(headerMap));

        // headerMap.put("title", "库存");
        // headerMap.put("slot", "stock");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));

        // 2025-04-24 取消产品编号
        // headerMap.put("title", "产品编号");
        // headerMap.put("slot", "bar_code");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));
        // 2025-04-24 取消产品编号

        headerMap.put("title", "重量(G)");
        headerMap.put("slot", "weight");
        headerMap.put("align", align);
        headerMap.put("minWidth", 140);
        headerMapList.add(ObjectUtil.clone(headerMap));

        headerMap.put("title", "限购数量");
        headerMap.put("slot", "limitBuy");
        headerMap.put("align", align);
        headerMap.put("minWidth", 140);
        headerMapList.add(ObjectUtil.clone(headerMap));

        // SKU 体积信息
        // headerMap.put("title", "体积(m³)");
        // headerMap.put("slot", "volume");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));

        // headerMap.put("title", "所需兑换积分");
        // headerMap.put("slot", "integral");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));

        // if (isActivity) {
        // headerMap.put("title", "拼团价");
        // headerMap.put("slot", "pink_price");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));

        // headerMap.put("title", "拼团活动库存");
        // headerMap.put("slot", "pink_stock");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));

        // headerMap.put("title", "秒杀价");
        // headerMap.put("slot", "seckill_price");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));

        // headerMap.put("title", "秒杀活动库存");
        // headerMap.put("slot", "seckill_stock");
        // headerMap.put("align", align);
        // headerMap.put("minWidth", 140);
        // headerMapList.add(ObjectUtil.clone(headerMap));
        // }

        headerMap.put("title", "操作");
        headerMap.put("slot", "action");
        headerMap.put("align", align);
        headerMap.put("minWidth", 70);
        headerMapList.add(ObjectUtil.clone(headerMap));
    }

    /**
     * 组合规则属性算法
     *
     * @param formatDetailDTOList
     * @return DetailDto
     */
    private DetailDto attrFormat(List<FromatDetailDto> formatDetailDTOList) {

        List<String> data = new ArrayList<>();
        List<Map<String, Map<String, String>>> res = new ArrayList<>();

        formatDetailDTOList.stream()
                .map(FromatDetailDto::getDetail)
                .forEach(i -> {
                    if (i == null || i.isEmpty()) {
                        throw new YshopException("请至少添加一个规格值哦");
                    }
                    String str = ArrayUtil.join(i.toArray(), ",");
                    if (str.contains("-")) {
                        throw new YshopException("规格值里包含'-',请重新添加");
                    }
                });

        if (formatDetailDTOList.size() > 1) {
            for (int i = 0; i < formatDetailDTOList.size() - 1; i++) {
                if (i == 0) {
                    data = formatDetailDTOList.get(i).getDetail();
                }
                List<String> tmp = new LinkedList<>();
                for (String v : data) {
                    for (String g : formatDetailDTOList.get(i + 1).getDetail()) {
                        String rep2 = "";
                        if (i == 0) {
                            rep2 = formatDetailDTOList.get(i).getValue() + "_" + v + "-"
                                    + formatDetailDTOList.get(i + 1).getValue() + "_" + g;
                        } else {
                            rep2 = v + "-"
                                    + formatDetailDTOList.get(i + 1).getValue() + "_" + g;
                        }

                        tmp.add(rep2);

                        if (i == formatDetailDTOList.size() - 2) {
                            Map<String, Map<String, String>> rep4 = new LinkedHashMap<>();
                            Map<String, String> reptemp = new LinkedHashMap<>();
                            for (String h : Arrays.asList(rep2.split("-"))) {
                                List<String> rep3 = Arrays.asList(h.split("_"));
                                if (rep3.size() > 1) {
                                    reptemp.put(rep3.get(0), rep3.get(1));
                                } else {
                                    reptemp.put(rep3.get(0), "");
                                }
                            }
                            rep4.put("detail", reptemp);

                            res.add(rep4);
                        }
                    }

                }

                if (!tmp.isEmpty()) {
                    data = tmp;
                }
            }
        } else {
            List<String> dataArr = new ArrayList<>();
            for (FromatDetailDto formatDetailDTO : formatDetailDTOList) {
                for (String str : formatDetailDTO.getDetail()) {
                    Map<String, Map<String, String>> map2 = new LinkedHashMap<>();
                    dataArr.add(formatDetailDTO.getValue() + "_" + str);
                    Map<String, String> map1 = new LinkedHashMap<>();
                    map1.put(formatDetailDTO.getValue(), str);
                    map2.put("detail", map1);
                    res.add(map2);
                }
            }
            String s = StrUtil.join("-", dataArr);
            data.add(s);
        }

        DetailDto detailDto = new DetailDto();
        detailDto.setData(data);
        detailDto.setRes(res);

        return detailDto;
    }

    // @Override
    // public void deleteForwardImg(Long id) {
    // baseMapper.deleteForwardImg(id, "_product_detail_wap");
    // }

    @Override
    public void checkPrePublishProduct2Onsale() {
        // 1. 查询
        YxStoreProductQueryCriteria query = new YxStoreProductQueryCriteria();
        query.setPrePublishStatus(ShopConstants.NEED_PRE_PUBLISH);
        List<Product> productList = queryAll(query);

        // 2. 发布
        productList.forEach(p -> {
            // 预发布时间
            Date t = p.getPrePublishTime();
            if (t == null) {
                log.error("Product " + p.getStoreName() + "pre publish time is empty");
                return;
            }

            // 预发布时间比当前时间早一分钟，即可进行发布了
            Date now = new Date();
            // 已发布时间的早一分钟
            t.setTime(t.getTime() - 60 * 1000);

            if (t.before(now)) {
                p.setPrePublishStatus(ShopConstants.NORMAL);
                // !!!不改变在首页的状态
                // shelf

                // 上架
                p.setIsShow(ShopCommonEnum.SALE_STS_ONSALE.getValue());
                // 不隐藏
                p.setIsHide(ShopCommonEnum.VISIBLE_STS_SHOW.getValue());
                // 更新
                boolean succ = saveOrUpdate(p);
                log.info("定时上架商品: {} {} {}", p.getId(), p.getStoreName(), succ);
            }
        });
    }

    /**
     * 商品设置销售限额
     * 
     * @param pid 商品id
     */
    @Override
    public void stockSetMaxSaleLimit(Long pid, List<MaxSalesParam> limitList) {
        // 1. 获取商品
        Product product = getById(pid);

        // 2. 获取所有sku
        List<SKU> skuList = skuService
                .list(new LambdaQueryWrapper<SKU>().eq(SKU::getProductId, pid));

        // 3. sku Id 升序
        limitList.sort(Comparator.comparing(MaxSalesParam::getSkuId));
        List<Long> skuIds = limitList.stream().map(MaxSalesParam::getSkuId).collect(Collectors.toList());

        String clientId = snowFlakeService.getMySnowFlake().nextIdStr();
        long startTime = System.currentTimeMillis();
        long startProcTime = System.currentTimeMillis();

        List<Long> lockedSkuIds = new ArrayList<>();

        try {
            // !!!顺序加锁
            for (Long skuId : skuIds) {
                String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                if (!redisLock.tryLockWithWait(lockKey, clientId)) {
                    String msg = String.format("stockSetMaxSaleLimit 获取锁失败: %s，客户端id: %s, 超时时间: %d, product: %d %s",
                            lockKey, clientId,
                            System.currentTimeMillis() - startTime, pid, product.getStoreName());
                    log.error(msg);
                    throw new YshopException("系统繁忙，请稍后再试");
                }
                lockedSkuIds.add(skuId);
                log.info("stockSetMaxSaleLimit 锁定商品: {} {}, lockKey:{} clientId:{}", skuId, product.getStoreName(),
                        lockKey, clientId);
            }
            startProcTime = System.currentTimeMillis();

            for (MaxSalesParam saleSetting : limitList) {
                SKU sku = skuList.stream().filter(x -> x.getId().longValue() == saleSetting.getSkuId().longValue())
                        .findFirst().orElse(null);
                if (sku == null) {
                    throw new YshopException("未找到SKU");
                }

                // 3. 获取存货记录
                InventoryRecord record = inventoryRecordService.findBySkuId(sku.getId());
                if (record == null) {
                    throw new YshopException("未找到存货记录");
                }

                // 4. 检查存货是否足够
                List<Integer> tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
                int totalTmpStock = tmpStocks.stream() // 创建流
                        .mapToInt(Integer::intValue) // 将Integer转换为int
                        .sum(); // 对int进行求和

                int avaliableQuantity = record.getCurrentQuantity().intValue() + totalTmpStock;

                // 获取当前“飘在外边的库存"：
                // 1. 待付款订单，会占用库存
                // 2. 已付款订单，但是尚未发货，会占用库存
                // 3. 已付款订单，已经发货，尚未收货，占用库存
                // 4. 已经确认收货，尚未在微信确认，尚未结算，占用库存
                if (saleSetting.getMaxSales() > avaliableQuantity) {
                    String msg = String.format("%s销售限额：%d，大于当前库存：%d", sku.getSku(), saleSetting.getMaxSales(),
                            avaliableQuantity);
                    if (product.isSingleSpec()) {
                        msg = String.format("%s销售限额：%d，大于当前库存：%d", product.getStoreName(), saleSetting.getMaxSales(),
                                avaliableQuantity);
                    }
                    throw new YshopException(msg);
                }

                // 5. 修改库存
                sku.setStock(saleSetting.getMaxSales());
                UpdateWrapper<SKU> skuUpdateWrapper = new UpdateWrapper<>();
                skuUpdateWrapper.eq("id", sku.getId());
                boolean succ = skuService.update(sku, skuUpdateWrapper);
                if (!succ) {
                    throw new YshopException("更新SKU销售限额失败");
                }

                // 6. 单规格商品修改商品属性
                if (product.isSingleSpec()) {
                    product.setStock(saleSetting.getMaxSales());
                    UpdateWrapper<Product> productUpdateWrapper = new UpdateWrapper<>();
                    productUpdateWrapper.eq("id", pid);
                    succ = update(product, productUpdateWrapper);
                    if (!succ) {
                        throw new YshopException("更新商品销售限额失败");
                    }
                }
            }
        } catch (Exception e) {
            log.error("stockSetMaxSaleLimit 异常: {}", e.getMessage());
            throw new YshopException("更新销售限额失败");
        } finally {
            Collections.reverse(lockedSkuIds);

            for (Long skuId : lockedSkuIds) {
                String lockKey = String.format(ShopConstants.LOCK_STOCK_SKU, skuId);
                int errorCode = redisLock.releaseLock(lockKey, clientId);
                if (errorCode == 1) {
                    log.warn("stockSetMaxSaleLimit 锁已经释放，无需重复操作: {}, clientId:{}, skuId: {}, errorCode:{}", lockKey,
                            clientId,
                            skuId,
                            errorCode);
                } else if (errorCode != 0) {
                    log.error("stockSetMaxSaleLimit 释放锁失败: {}, clientId:{}, skuId: {}, errorCode:{}", lockKey, clientId,
                            skuId,
                            errorCode);
                } else {
                    log.info("stockSetMaxSaleLimit 释放锁成功: {}, clientId:{}, skuId: {}", lockKey, clientId, skuId);
                }
            }
            long endProcTime = System.currentTimeMillis();
            log.info("stockSetMaxSaleLimit 锁耗时: {}ms, 执行耗时: {}ms, 锁定商品: {}", (startProcTime - startTime),
                    endProcTime - startProcTime, lockedSkuIds);
        }
    }

    /**
     * 入库商品
     * 
     * @param pid          商品id
     * @param transcations sku入库信息列表
     */
    @Override
    public void stockIn(Long pid, List<InventoryTranscationParam> transcations, Long operatorId) {
        // 1. 获取商品
        Product product = getById(pid);

        // 2. 获取所有sku
        List<SKU> skuList = skuService
                .list(new LambdaQueryWrapper<SKU>().eq(SKU::getProductId, pid));


        for (InventoryTranscationParam transcationParam : transcations) {
            SKU sku = skuList.stream().filter(x -> x.getId().longValue() == transcationParam.getSkuId().longValue())
                    .findFirst().orElse(null);
            if (sku == null) {
                throw new YshopException("未找到SKU");
            }

            if (transcationParam.getQuantity() == 0) {
                continue;
            }

            // 4. 获取存货记录
            InventoryRecord record = inventoryRecordService.findBySkuId(sku.getId());
            if (record == null) {
                throw new YshopException("未找到存货记录");
            }

            // 4. 新增库存入库记录
            InventoryTransaction it = new InventoryTransaction();
            it.setOperatorId(operatorId);
            it.setInventoryId(record.getInventoryId());
            it.setCost(transcationParam.getCost());
            it.setBatchNumber(transcationParam.getBatchNumber());
            it.setTransactionType(InventoryTransaction.TRANSACTION_TYPE_STOCK_IN);
            it.setQuantity(transcationParam.getQuantity());
            it.setTransactionDate(new Date());
            it.setRemarks(transcationParam.getRemarks());

            it = inventoryTransactionService.saveTransaction(it);

            // 5. 修改库存
            int newQuantity = record.getCurrentQuantity() + transcationParam.getQuantity();
            record.setCurrentQuantity(newQuantity);
            record = inventoryRecordService.saveInventoryRecord(record);

            // 不影响销售限额和临时库存记录
        }
    }

    /**
     * 出库商品
     * 
     * @param productId
     * @param transcations
     * @param operatorId
     */
    @Override
    public void stockOut(Long productId, List<InventoryTranscationParam> transcations, Long operatorId) {
        // 1. 获取商品
        Product product = getById(productId);

        // 2. 获取所有sku
        List<SKU> skuList = skuService
                .list(new LambdaQueryWrapper<SKU>().eq(SKU::getProductId, productId));
        // int depotId = product.getDepotId();
        for (InventoryTranscationParam transcationParam : transcations) {
            SKU sku = skuList.stream().filter(x -> x.getId().longValue() == transcationParam.getSkuId().longValue())
                    .findFirst().orElse(null);
            if (sku == null) {
                throw new YshopException("未找到SKU");
            }

            if (transcationParam.getQuantity() == 0) {
                continue;
            }

            // 4. 获取存货记录
            InventoryRecord record = inventoryRecordService.findBySkuId(sku.getId());
            if (record == null) {
                throw new YshopException("未找到存货记录");
            }

            if (transcationParam.getQuantity() > record.getCurrentQuantity()) {
                throw new YshopException("出库数量错误，请检查");
            }

            // 4. 新增库存出库记录
            InventoryTransaction it = new InventoryTransaction();
            it.setOperatorId(operatorId);
            it.setInventoryId(record.getInventoryId());
            it.setCost(null);
            it.setBatchNumber("-");
            it.setTransactionType(InventoryTransaction.TRANSACTION_TYPE_STOCK_OUT);
            it.setQuantity(-transcationParam.getQuantity());
            it.setTransactionDate(new Date());
            it.setRemarks(transcationParam.getRemarks());

            it = inventoryTransactionService.saveTransaction(it);

            // 5. 修改库存
            int newQuantity = record.getCurrentQuantity() - transcationParam.getQuantity();
            record.setCurrentQuantity(newQuantity);
            record = inventoryRecordService.saveInventoryRecord(record);

            // 不影响销售限额和临时库存记录
        }
    }

    /**
     * 报损商品
     * 
     * @param productId    商品id
     * @param transcations sku报损信息列表
     */
    @Override
    public void stockWriteOff(Long pid, List<InventoryTranscationParam> transcations, Long operatorId) {
        // 1. 获取商品
        Product product = getById(pid);

        // 2. 获取所有sku
        List<SKU> skuList = skuService
                .list(new LambdaQueryWrapper<SKU>().eq(SKU::getProductId, pid));
        // int depotId = product.getDepotId();
        for (InventoryTranscationParam transcationParam : transcations) {
            SKU sku = skuList.stream().filter(x -> x.getId().longValue() == transcationParam.getSkuId().longValue())
                    .findFirst().orElse(null);
            if (sku == null) {
                throw new YshopException("未找到SKU");
            }

            if (transcationParam.getQuantity() == 0) {
                continue;
            }

            // 2.0 获取临时库存
            // 查询sku的unique
            List<Integer> tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
            int totalTmpStock = tmpStocks.stream() // 创建流
                    .mapToInt(Integer::intValue) // 将Integer转换为int
                    .sum(); // 对int进行求和

            // 2.1 获取存货记录
            InventoryRecord record = inventoryRecordService.findBySkuId(sku.getId());
            if (record == null) {
                throw new YshopException("未找到存货记录");
            }

            if (transcationParam.getQuantity() > record.getCurrentQuantity()) {
                String msg = String.format("%s报损库存：%d，大于当前库存：%d", sku.getSku(), transcationParam.getQuantity(),
                        record.getCurrentQuantity());
                if (product.isSingleSpec()) {
                    msg = String.format("%s报损库存: %d，大于当前库存：%d", product.getStoreName(), transcationParam.getQuantity(),
                            record.getCurrentQuantity());
                }
                throw new YshopException(msg);
            }

            // 当报损后: 实时库存 < 销售限额
            // 需要修改销售限额
            int realTimeStock = record.getCurrentQuantity() + totalTmpStock;
            int salesLimitNow = sku.getStock();

            if (salesLimitNow > realTimeStock - transcationParam.getQuantity()) {
                String msg = String.format("当前销售限额:%d超过报损后的真实库存:%d，请修改销售限额", salesLimitNow,
                        realTimeStock - transcationParam.getQuantity());
                throw new YshopException(msg);
            }

            // 2.2 新增库存报损记录
            InventoryTransaction it = new InventoryTransaction();
            it.setOperatorId(operatorId);
            it.setInventoryId(record.getInventoryId());
            it.setCost(null);
            it.setBatchNumber(transcationParam.getBatchNumber());
            it.setTransactionType(InventoryTransaction.TRANSACTION_TYPE_WRITE_OFF);
            it.setQuantity(-transcationParam.getQuantity());
            it.setTransactionDate(new Date());
            it.setRemarks(transcationParam.getRemarks());

            // save point 1
            it = inventoryTransactionService.saveTransaction(it);

            // 2.3. 修改库存
            int newQuantity = record.getCurrentQuantity() - transcationParam.getQuantity();
            record.setCurrentQuantity(newQuantity);
            // save point 2
            record = inventoryRecordService.saveInventoryRecord(record);
        }
    }
}
