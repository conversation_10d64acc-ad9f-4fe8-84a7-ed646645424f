/**
* Copyright (C) 2019-2023
* All rights reserved, <NAME_EMAIL>
* 注意：
* 本软件为************开发研制，未经购买不得使用
* 一经发现盗用、分享等行为，将追究法律责任，后果自负
*/
package com.leway.modules.template.service;

import com.leway.common.service.BaseService;
import com.leway.modules.template.domain.YxShippingTemplatesFree;
import com.leway.modules.template.service.dto.YxShippingTemplatesFreeDto;
import com.leway.modules.template.service.dto.YxShippingTemplatesFreeQueryCriteria;
import org.springframework.data.domain.Pageable;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 包邮模板
 * yx_shipping_templates_free
 * <AUTHOR>
 * @date 2020-06-29
 */
public interface YxShippingTemplatesFreeService extends BaseService<YxShippingTemplatesFree> {

    /**
     * 查询数据分页
     * 
     * @param criteria 条件
     * @param pageable 分页参数
     * @return Map<String,Object>
     */
    Map<String, Object> queryAll(YxShippingTemplatesFreeQueryCriteria criteria, Pageable pageable);

    /**
     * 查询所有数据不分页
     * 
     * @param criteria 条件参数
     * @return List<YxShippingTemplatesFreeDto>
     */
    List<YxShippingTemplatesFree> queryAll(YxShippingTemplatesFreeQueryCriteria criteria);

    /**
     * 导出数据
     * 
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    void download(List<YxShippingTemplatesFreeDto> all, HttpServletResponse response) throws IOException;
}
