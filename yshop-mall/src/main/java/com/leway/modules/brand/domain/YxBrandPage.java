/**
* Copyright (C) 2019-2023
* All rights reserved, <NAME_EMAIL>
* 注意：
* 本软件为************开发研制，未经购买不得使用
* 一经发现盗用、分享等行为，将追究法律责任，后果自负
*/
package com.leway.modules.brand.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import javax.validation.constraints.*;
import java.util.Date;
import com.leway.domain.BaseDomain;

/**
* <AUTHOR>
* @date 2025-04-17
*/
@Data
@TableName("yx_brand_page")
public class YxBrandPage extends BaseDomain {
    /** 页面ID */
    @TableId
    private Long id;

    /** 厂牌ID */
    @NotNull
    private Long brandId;

    /** 页面名称 */
    @NotBlank
    private String name;

    /**
     * 页面描述，JSON格式的字符串
     * 例如：[{\"type\":\"follow\",\"icon\":\"https://img00.cabletemple.cc/undefined___1745396711775___自然友谊头像.jpg\",\"title\":\"关注自然友谊\",\"url\":\"https://mp.weixin.qq.com/s/-v_67jkFaI3R1mGnJUiEdQ\"},{\"type\":\"image\",\"cover\":\"\",\"url\":\"https://img00.cabletemple.cc/undefined___1746689580369___nf-title.jpg\"},{\"type\":\"text\",\"text\":\"独立音乐出品发行机构。自然而然，友谊第一。\",\"style\":{\"bold\":false,\"align\":\"center\",\"type\":\"p\"}},{\"type\":\"image\",\"cover\":\"\",\"url\":\"https://img00.cabletemple.cc/undefined___1746684927338___自然友谊2024.jpg\"},{\"type\":\"text\",\"text\":\"自然友谊2024\",\"style\":{\"bold\":false,\"align\":\"center\",\"type\":\"p\"}}]
     */
    private String description;

    /** 排序 */
    private Integer sort;

    /** 状态（0：停用，1：启用） */
    private Integer status;





    public void copy(YxBrandPage source){
        BeanUtil.copyProperties(source,this, CopyOptions.create().setIgnoreNullValue(true));
    }
}
