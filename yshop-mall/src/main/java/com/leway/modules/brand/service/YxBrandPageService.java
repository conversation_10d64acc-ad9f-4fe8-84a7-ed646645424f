/**
* Copyright (C) 2019-2023
* All rights reserved, <NAME_EMAIL>
* 注意：
* 本软件为************开发研制，未经购买不得使用
* 一经发现盗用、分享等行为，将追究法律责任，后果自负
*/
package com.leway.modules.brand.service;
import com.leway.common.service.BaseService;
import com.leway.modules.brand.domain.YxBrandPage;
import com.leway.modules.brand.service.dto.YxBrandPageDto;
import com.leway.modules.brand.service.dto.YxBrandPageQueryCriteria;
import org.springframework.data.domain.Pageable;
import java.util.Map;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.leway.domain.PageResult;
/**
* <AUTHOR>
* @date 2025-04-17
*/
public interface YxBrandPageService  extends BaseService<YxBrandPage>{

    /**
    * 查询数据分页
    * @param criteria 条件
    * @param pageable 分页参数
    * @return Map<String,Object>
    */
    PageResult<YxBrandPageDto>  queryAll(YxBrandPageQueryCriteria criteria, Pageable pageable);

    /**
    * 查询所有数据不分页
    * @param criteria 条件参数
    * @return List<YxBrandPageDto>
    */
    List<YxBrandPage> queryAll(YxBrandPageQueryCriteria criteria);

    /**
    * 导出数据
    * @param all 待导出的数据
    * @param response /
    * @throws IOException /
    */
    void download(List<YxBrandPageDto> all, HttpServletResponse response) throws IOException;
    
    /**
    * 根据厂牌ID查询页面列表
    * @param brandId 厂牌ID
    * @return 页面列表
    */
    List<YxBrandPage> getPagesByBrandId(Long brandId);
    
    /**
     * APP 端接口
     * 根据厂牌ID查询页面列表，并处理每个页面中的description字段
     * 当description中包含type为product的数据时，会查询对o的商品信息并添加到返回结果中
     * 当商品隐藏时候，不返回
     * @param brandId 厂牌ID
     * @return 处理后的页面列表
     */
    List<YxBrandPage> getPagesWithProductsByBrandId(Long brandId);
}
