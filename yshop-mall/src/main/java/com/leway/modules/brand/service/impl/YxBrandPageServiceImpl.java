/**
* Copyright (C) 2019-2023
* All rights reserved, <NAME_EMAIL>
* 注意：
* 本软件为************开发研制，未经购买不得使用
* 一经发现盗用、分享等行为，将追究法律责任，后果自负
*/
package com.leway.modules.brand.service.impl;

import com.leway.modules.brand.domain.YxBrandPage;
import com.leway.common.service.impl.BaseServiceImpl;
import lombok.AllArgsConstructor;
import com.leway.dozer.service.IGenerator;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.leway.common.utils.QueryHelpPlus;
import com.leway.utils.ValidationUtil;
import com.leway.utils.FileUtil;
import com.leway.modules.brand.service.YxBrandPageService;
import com.leway.modules.brand.service.dto.YxBrandPageDto;
import com.leway.modules.brand.service.dto.YxBrandPageQueryCriteria;
import com.leway.modules.brand.service.mapper.YxBrandPageMapper;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.product.vo.ProductQueryVo;
import com.leway.enums.ShopCommonEnum;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import com.leway.domain.PageResult;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
/**
* <AUTHOR>
* @date 2025-04-17
*/
@Slf4j
@Service
@AllArgsConstructor
//@CacheConfig(cacheNames = "yxBrandPage#60")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class YxBrandPageServiceImpl extends BaseServiceImpl<YxBrandPageMapper, YxBrandPage> implements YxBrandPageService {

    private final IGenerator generator;
    private final ProductService productService;

    @Override
    public PageResult<YxBrandPageDto> queryAll(YxBrandPageQueryCriteria criteria, Pageable pageable) {
        getPageByStartPage0(pageable);
        PageInfo<YxBrandPage> page = new PageInfo<>(queryAll(criteria));
        return generator.convertPageInfo(page,YxBrandPageDto.class);
    }


    @Override
    public List<YxBrandPage> queryAll(YxBrandPageQueryCriteria criteria){
        return baseMapper.selectList(QueryHelpPlus.getPredicate(YxBrandPage.class, criteria));
    }


    @Override
    public void download(List<YxBrandPageDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (YxBrandPageDto yxBrandPage : all) {
            Map<String,Object> map = new LinkedHashMap<>();
            map.put("厂牌ID", yxBrandPage.getBrandId());
            map.put("页面名称", yxBrandPage.getName());
            map.put("排序", yxBrandPage.getSort());
            map.put("状态（0：停用，1：启用）", yxBrandPage.getStatus());
            map.put("创建时间", yxBrandPage.getCreateTime());
            map.put("修改时间", yxBrandPage.getUpdateTime());
            map.put("是否删除（0：否，1：是）", yxBrandPage.getIsDel());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
    
    @Override
    public List<YxBrandPage> getPagesByBrandId(Long brandId) {
        LambdaQueryWrapper<YxBrandPage> wrapper = new LambdaQueryWrapper<>();
        // 根据厂牌ID查询
        wrapper.eq(YxBrandPage::getBrandId, brandId);
        // 只查询启用状态的页面
        wrapper.eq(YxBrandPage::getStatus, 1);
        // 只查询未删除的页面
        wrapper.eq(YxBrandPage::getIsDel, 0);
        // 按排序字段正序排序
        wrapper.orderByAsc(YxBrandPage::getSort);
        
        return list(wrapper);
    }
    
    @Override
    public List<YxBrandPage> getPagesWithProductsByBrandId(Long brandId) {
        // 先获取基础的页面列表
        List<YxBrandPage> pages = getPagesByBrandId(brandId);
        
        // 处理每个页面的description字段
        for (YxBrandPage page : pages) {
            processPageDescription(page);
        }
        
        return pages;
    }
    
    /**
     * 处理页面的description字段，解析其中的product类型，并查询相应的商品信息
     * @param page 页面对象
     */
    private void processPageDescription(YxBrandPage page) {
        String description = page.getDescription();
        if (description == null || description.isEmpty()) {
            return;
        }
        
        try {
            // 解析description为JSON数组
            JSONArray contentArray = JSON.parseArray(description);
            
            // 遍历所有内容块
            for (int i = 0; i < contentArray.size(); i++) {
                JSONObject contentBlock = contentArray.getJSONObject(i);
                String type = contentBlock.getString("type");
                
                // 如果是产品类型，则查询产品信息
                if ("product".equals(type) && contentBlock.containsKey("ids")) {
                    JSONArray productIds = contentBlock.getJSONArray("ids");
                    List<Long> idList = new ArrayList<>();
                    
                    // 收集所有产品ID
                    for (int j = 0; j < productIds.size(); j++) {
                        idList.add(productIds.getLong(j));
                    }
                    
                    // 查询产品信息
                    List<ProductQueryVo> products = new ArrayList<>();
                    for (Long productId : idList) {
                        ProductQueryVo product = productService.getStoreProductById(productId);
                        if (product != null) {
                            // 1. 商品未上架，跳过
                            if (product.getIsShow() == null || product.getIsShow().equals(ShopCommonEnum.SALE_STS_OFFSALE.getValue())) {
                                continue;
                            }

                            // 2. 商品隐藏，则跳过
                            if (product.getIsHide() != null && product.getIsHide().equals(ShopCommonEnum.VISIBLE_STS_HIDE.getValue())) {
                                continue;
                            }

                            products.add(product);
                        }
                    }
                    
                    // 将产品信息添加到内容块中
                    contentBlock.put("products", products);
                }
            }
            
            // 更新description字段
            page.setDescription(contentArray.toJSONString());
        } catch (Exception e) {
            log.error("处理页面description失败: {}", e.getMessage());
        }
    }
}
