/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 50729
 Source Host           : localhost:3306
 Source Schema         : yshopb2c

 Target Server Type    : MySQL
 Target Server Version : 50729
 File Encoding         : 65001

 Date: 21/12/2020 13:37:30
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for alipay_config
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_after_sales_status`;
CREATE TABLE `yx_store_after_sales_status` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `store_after_sales_id` bigint(20) DEFAULT NULL COMMENT '售后id',
  `change_type` tinyint(1) DEFAULT NULL COMMENT '操作类型',
  `change_message` varchar(128) COLLATE utf8_bin DEFAULT NULL COMMENT '操作备注',
  `change_time` datetime DEFAULT NULL COMMENT '操作时间',
  `operator` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '操作人',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='售后订单操作详情表';

DROP TABLE IF EXISTS `yx_store_after_sales_item`;
CREATE TABLE `yx_store_after_sales_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `store_after_sales_id` bigint(20) DEFAULT NULL COMMENT '售后id',
  `product_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `cart_info` text COLLATE utf8_bin NOT NULL COMMENT '退货东西的详情信息',
  `is_del` bit(1) DEFAULT NULL COMMENT '逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='售后子表';

DROP TABLE IF EXISTS `yx_store_after_sales`;
CREATE TABLE `yx_store_after_sales` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `order_code` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '订单号',
  `refund_amount` decimal(32,8) DEFAULT NULL COMMENT '退款金额',
  `service_type` tinyint(1) DEFAULT NULL COMMENT '服务类型0仅退款1退货退款',
  `reasons` text COLLATE utf8_bin COMMENT '申请原因',
  `explains` text COLLATE utf8_bin COMMENT '说明',
  `explain_img` text COLLATE utf8_bin COMMENT '说明图片->多个用逗号分割',
  `shipper_code` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '物流公司编码',
  `delivery_sn` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '物流单号',
  `delivery_name` varchar(32) COLLATE utf8_bin DEFAULT NULL COMMENT '物流名称',
  `state` tinyint(1) DEFAULT NULL COMMENT '状态 0已提交等待平台审核 1平台已审核 等待用户发货/退款 2 用户已发货 3退款成功',
  `sales_state` tinyint(1) DEFAULT NULL COMMENT '售后状态-0正常1用户取消2商家拒绝',
  `create_time` datetime DEFAULT NULL COMMENT '添加时间',
  `is_del` tinyint(1) DEFAULT NULL COMMENT '逻辑删除',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户id',
  `consignee` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '商家收货人',
  `phone_number` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '商家手机号',
  `address` varchar(255) COLLATE utf8_bin DEFAULT NULL COMMENT '商家地址',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8 COLLATE=utf8_bin COMMENT='售后记录表';


DROP TABLE IF EXISTS `alipay_config`;
CREATE TABLE `alipay_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `app_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '应用ID',
  `charset` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '编码',
  `format` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型 固定格式json',
  `gateway_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '网关地址',
  `notify_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '异步回调',
  `private_key` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '私钥',
  `public_key` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '公钥',
  `return_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '回调地址',
  `sign_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '签名方式',
  `sys_service_provider_id` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商户号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '支付宝配置类' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of alipay_config
-- ----------------------------
INSERT INTO `alipay_config` VALUES (1, '2016091700532697', 'utf-8', 'JSON', 'https://openapi.alipaydev.com/gateway.do', 'http://api.auauz.net/api/aliPay/notify', 'MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQC5js8sInU10AJ0cAQ8UMMyXrQ+oHZEkVt5lBwsStmTJ7YikVYgbskx1YYEXTojRsWCb+SH/kDmDU4pK/u91SJ4KFCRMF2411piYuXU/jF96zKrADznYh/zAraqT6hvAIVtQAlMHN53nx16rLzZ/8jDEkaSwT7+HvHiS+7sxSojnu/3oV7BtgISoUNstmSe8WpWHOaWv19xyS+Mce9MY4BfseFhzTICUymUQdd/8hXA28/H6osUfAgsnxAKv7Wil3aJSgaJczWuflYOve0dJ3InZkhw5Cvr0atwpk8YKBQjy5CdkoHqvkOcIB+cYHXJKzOE5tqU7inSwVbHzOLQ3XbnAgMBAAECggEAVJp5eT0Ixg1eYSqFs9568WdetUNCSUchNxDBu6wxAbhUgfRUGZuJnnAll63OCTGGck+EGkFh48JjRcBpGoeoHLL88QXlZZbC/iLrea6gcDIhuvfzzOffe1RcZtDFEj9hlotg8dQj1tS0gy9pN9g4+EBH7zeu+fyv+qb2e/v1l6FkISXUjpkD7RLQr3ykjiiEw9BpeKb7j5s7Kdx1NNIzhkcQKNqlk8JrTGDNInbDM6inZfwwIO2R1DHinwdfKWkvOTODTYa2MoAvVMFT9Bec9FbLpoWp7ogv1JMV9svgrcF9XLzANZ/OQvkbe9TV9GWYvIbxN6qwQioKCWO4GPnCAQKBgQDgW5MgfhX8yjXqoaUy/d1VjI8dHeIyw8d+OBAYwaxRSlCfyQ+tieWcR2HdTzPca0T0GkWcKZm0ei5xRURgxt4DUDLXNh26HG0qObbtLJdu/AuBUuCqgOiLqJ2f1uIbrz6OZUHns+bT/jGW2Ws8+C13zTCZkZt9CaQsrp3QOGDx5wKBgQDTul39hp3ZPwGNFeZdkGoUoViOSd5Lhowd5wYMGAEXWRLlU8z+smT5v0POz9JnIbCRchIY2FAPKRdVTICzmPk2EPJFxYTcwaNbVqL6lN7J2IlXXMiit5QbiLauo55w7plwV6LQmKm9KV7JsZs5XwqF7CEovI7GevFzyD3w+uizAQKBgC3LY1eRhOlpWOIAhpjG6qOoohmeXOphvdmMlfSHq6WYFqbWwmV4rS5d/6LNpNdL6fItXqIGd8I34jzql49taCmi+A2nlR/E559j0mvM20gjGDIYeZUz5MOE8k+K6/IcrhcgofgqZ2ZED1ksHdB/E8DNWCswZl16V1FrfvjeWSNnAoGAMrBplCrIW5xz+J0Hm9rZKrs+AkK5D4fUv8vxbK/KgxZ2KaUYbNm0xv39c+PZUYuFRCz1HDGdaSPDTE6WeWjkMQd5mS6ikl9hhpqFRkyh0d0fdGToO9yLftQKOGE/q3XUEktI1XvXF0xyPwNgUCnq0QkpHyGVZPtGFxwXiDvpvgECgYA5PoB+nY8iDiRaJNko9w0hL4AeKogwf+4TbCw+KWVEn6jhuJa4LFTdSqp89PktQaoVpwv92el/AhYjWOl/jVCm122f9b7GyoelbjMNolToDwe5pF5RnSpEuDdLy9MfE8LnE3PlbE7E5BipQ3UjSebkgNboLHH/lNZA5qvEtvbfvQ==', 'MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAut9evKRuHJ/2QNfDlLwvN/S8l9hRAgPbb0u61bm4AtzaTGsLeMtScetxTWJnVvAVpMS9luhEJjt+Sbk5TNLArsgzzwARgaTKOLMT1TvWAK5EbHyI+eSrc3s7Awe1VYGwcubRFWDm16eQLv0k7iqiw+4mweHSz/wWyvBJVgwLoQ02btVtAQErCfSJCOmt0Q/oJQjj08YNRV4EKzB19+f5A+HQVAKy72dSybTzAK+3FPtTtNen/+b5wGeat7c32dhYHnGorPkPeXLtsqqUTp1su5fMfd4lElNdZaoCI7osZxWWUo17vBCZnyeXc9fk0qwD9mK6yRAxNbrY72Xx5VqIqwIDAQAB', 'http://api.auauz.net/api/aliPay/return', 'RSA2', '2088102176044281');

-- ----------------------------
-- Table structure for column_config
-- ----------------------------
DROP TABLE IF EXISTS `column_config`;
CREATE TABLE `column_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `column_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `column_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `dict_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `extra` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `form_show` bit(1) NULL DEFAULT NULL,
  `form_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `key_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `list_show` bit(1) NULL DEFAULT NULL,
  `not_null` bit(1) NULL DEFAULT NULL,
  `query_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `date_annotation` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 386 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代码生成字段信息存储' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of column_config
-- ----------------------------
INSERT INTO `column_config` VALUES (1, 'gen_test', 'id', 'int', NULL, 'auto_increment', b'0', NULL, 'PRI', b'0', b'1', NULL, 'ID', NULL);
INSERT INTO `column_config` VALUES (2, 'gen_test', 'sex', 'int', NULL, '', b'1', NULL, '', b'1', b'0', 'NotNull', '性别', NULL);
INSERT INTO `column_config` VALUES (3, 'gen_test', 'create_time', 'datetime', NULL, '', b'0', NULL, '', b'1', b'0', 'BetWeen', '', NULL);
INSERT INTO `column_config` VALUES (139, 'users_roles', 'user_id', 'bigint', NULL, '', b'1', NULL, 'PRI', b'1', b'1', NULL, '用户ID', NULL);
INSERT INTO `column_config` VALUES (140, 'users_roles', 'role_id', 'bigint', NULL, '', b'1', NULL, 'PRI', b'1', b'1', NULL, '角色ID', NULL);
INSERT INTO `column_config` VALUES (141, 'user_avatar', 'id', 'bigint', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (142, 'user_avatar', 'real_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '真实文件名', NULL);
INSERT INTO `column_config` VALUES (143, 'user_avatar', 'path', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '路径', NULL);
INSERT INTO `column_config` VALUES (144, 'user_avatar', 'size', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '大小', NULL);
INSERT INTO `column_config` VALUES (145, 'user_avatar', 'create_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '创建时间', NULL);
INSERT INTO `column_config` VALUES (146, 'yx_material_group', 'id', 'varchar', NULL, '', b'1', NULL, 'PRI', b'1', b'1', NULL, 'PK', NULL);
INSERT INTO `column_config` VALUES (147, 'yx_material_group', 'user_id', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '所属租户', NULL);
INSERT INTO `column_config` VALUES (148, 'yx_material_group', 'del_flag', 'char', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '逻辑删除标记（0：显示；1：隐藏）', NULL);
INSERT INTO `column_config` VALUES (149, 'yx_material_group', 'create_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '创建时间', NULL);
INSERT INTO `column_config` VALUES (150, 'yx_material_group', 'update_time', 'timestamp', NULL, 'on update CURRENT_TIMESTAMP', b'1', NULL, '', b'1', b'1', NULL, '最后更新时间', NULL);
INSERT INTO `column_config` VALUES (151, 'yx_material_group', 'create_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '创建者ID', NULL);
INSERT INTO `column_config` VALUES (152, 'yx_material_group', 'name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '分组名', NULL);
INSERT INTO `column_config` VALUES (153, 'yx_material', 'id', 'varchar', NULL, '', b'1', NULL, 'PRI', b'1', b'1', NULL, 'PK', NULL);
INSERT INTO `column_config` VALUES (154, 'yx_material', 'user_id', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '所属租户', NULL);
INSERT INTO `column_config` VALUES (155, 'yx_material', 'del_flag', 'char', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '逻辑删除标记（0：显示；1：隐藏）', NULL);
INSERT INTO `column_config` VALUES (156, 'yx_material', 'create_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '创建时间', NULL);
INSERT INTO `column_config` VALUES (157, 'yx_material', 'update_time', 'timestamp', NULL, 'on update CURRENT_TIMESTAMP', b'1', NULL, '', b'1', b'1', NULL, '最后更新时间', NULL);
INSERT INTO `column_config` VALUES (158, 'yx_material', 'create_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '创建者ID', NULL);
INSERT INTO `column_config` VALUES (159, 'yx_material', 'type', 'char', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '类型1、图片；2、视频', NULL);
INSERT INTO `column_config` VALUES (160, 'yx_material', 'group_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '分组ID', NULL);
INSERT INTO `column_config` VALUES (161, 'yx_material', 'name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '素材名', NULL);
INSERT INTO `column_config` VALUES (162, 'yx_material', 'url', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '素材链接', NULL);
INSERT INTO `column_config` VALUES (163, 'yx_user', 'uid', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '用户id', NULL);
INSERT INTO `column_config` VALUES (164, 'yx_user', 'username', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '用户账户(跟accout一样)', NULL);
INSERT INTO `column_config` VALUES (165, 'yx_user', 'account', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '用户账号', NULL);
INSERT INTO `column_config` VALUES (166, 'yx_user', 'password', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '用户密码（跟pwd）', NULL);
INSERT INTO `column_config` VALUES (167, 'yx_user', 'pwd', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户密码', NULL);
INSERT INTO `column_config` VALUES (168, 'yx_user', 'real_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '真实姓名', NULL);
INSERT INTO `column_config` VALUES (169, 'yx_user', 'birthday', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '生日', NULL);
INSERT INTO `column_config` VALUES (170, 'yx_user', 'card_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '身份证号码', NULL);
INSERT INTO `column_config` VALUES (171, 'yx_user', 'mark', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '用户备注', NULL);
INSERT INTO `column_config` VALUES (172, 'yx_user', 'partner_id', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '合伙人id', NULL);
INSERT INTO `column_config` VALUES (173, 'yx_user', 'group_id', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '用户分组id', NULL);
INSERT INTO `column_config` VALUES (174, 'yx_user', 'nickname', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '用户昵称', NULL);
INSERT INTO `column_config` VALUES (175, 'yx_user', 'avatar', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '用户头像', NULL);
INSERT INTO `column_config` VALUES (176, 'yx_user', 'phone', 'char', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '手机号码', NULL);
INSERT INTO `column_config` VALUES (177, 'yx_user', 'add_time', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '添加时间', NULL);
INSERT INTO `column_config` VALUES (178, 'yx_user', 'add_ip', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '添加ip', NULL);
INSERT INTO `column_config` VALUES (179, 'yx_user', 'last_time', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '最后一次登录时间', NULL);
INSERT INTO `column_config` VALUES (180, 'yx_user', 'last_ip', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '最后一次登录ip', NULL);
INSERT INTO `column_config` VALUES (181, 'yx_user', 'now_money', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户余额', NULL);
INSERT INTO `column_config` VALUES (182, 'yx_user', 'brokerage_price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '佣金金额', NULL);
INSERT INTO `column_config` VALUES (183, 'yx_user', 'integral', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户剩余积分', NULL);
INSERT INTO `column_config` VALUES (184, 'yx_user', 'sign_num', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '连续签到天数', NULL);
INSERT INTO `column_config` VALUES (185, 'yx_user', 'status', 'tinyint', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '1为正常，0为禁止', NULL);
INSERT INTO `column_config` VALUES (186, 'yx_user', 'level', 'tinyint', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '等级', NULL);
INSERT INTO `column_config` VALUES (187, 'yx_user', 'spread_uid', 'int', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '推广元id', NULL);
INSERT INTO `column_config` VALUES (188, 'yx_user', 'spread_time', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '推广员关联时间', NULL);
INSERT INTO `column_config` VALUES (189, 'yx_user', 'user_type', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户类型', NULL);
INSERT INTO `column_config` VALUES (190, 'yx_user', 'is_promoter', 'tinyint', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '是否为推广员', NULL);
INSERT INTO `column_config` VALUES (191, 'yx_user', 'pay_count', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '用户购买次数', NULL);
INSERT INTO `column_config` VALUES (192, 'yx_user', 'spread_count', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '下级人数', NULL);
INSERT INTO `column_config` VALUES (193, 'yx_user', 'clean_time', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '清理会员时间', NULL);
INSERT INTO `column_config` VALUES (194, 'yx_user', 'addres', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '详细地址', NULL);
INSERT INTO `column_config` VALUES (195, 'yx_user', 'adminid', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '管理员编号 ', NULL);
INSERT INTO `column_config` VALUES (196, 'yx_user', 'login_type', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户登录类型，h5,wechat,routine', NULL);
INSERT INTO `column_config` VALUES (197, 'yx_wechat_media', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '微信视频音频id', NULL);
INSERT INTO `column_config` VALUES (198, 'yx_wechat_media', 'type', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '回复类型', NULL);
INSERT INTO `column_config` VALUES (199, 'yx_wechat_media', 'path', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '文件路径', NULL);
INSERT INTO `column_config` VALUES (200, 'yx_wechat_media', 'media_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '微信服务器返回的id', NULL);
INSERT INTO `column_config` VALUES (201, 'yx_wechat_media', 'url', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '地址', NULL);
INSERT INTO `column_config` VALUES (202, 'yx_wechat_media', 'temporary', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '是否永久或者临时 0永久1临时', NULL);
INSERT INTO `column_config` VALUES (203, 'yx_wechat_media', 'add_time', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '添加时间', NULL);
INSERT INTO `column_config` VALUES (204, 'yx_store_order', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '订单ID', NULL);
INSERT INTO `column_config` VALUES (205, 'yx_store_order', 'order_id', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '订单号', NULL);
INSERT INTO `column_config` VALUES (206, 'yx_store_order', 'extend_order_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '额外订单号', NULL);
INSERT INTO `column_config` VALUES (207, 'yx_store_order', 'uid', 'int', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '用户id', NULL);
INSERT INTO `column_config` VALUES (208, 'yx_store_order', 'real_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户姓名', NULL);
INSERT INTO `column_config` VALUES (209, 'yx_store_order', 'user_phone', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户电话', NULL);
INSERT INTO `column_config` VALUES (210, 'yx_store_order', 'user_address', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '详细地址', NULL);
INSERT INTO `column_config` VALUES (211, 'yx_store_order', 'cart_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '购物车id', NULL);
INSERT INTO `column_config` VALUES (212, 'yx_store_order', 'freight_price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '运费金额', NULL);
INSERT INTO `column_config` VALUES (213, 'yx_store_order', 'total_num', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '订单商品总数', NULL);
INSERT INTO `column_config` VALUES (214, 'yx_store_order', 'total_price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '订单总价', NULL);
INSERT INTO `column_config` VALUES (215, 'yx_store_order', 'total_postage', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '邮费', NULL);
INSERT INTO `column_config` VALUES (216, 'yx_store_order', 'pay_price', 'decimal', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '实际支付金额', NULL);
INSERT INTO `column_config` VALUES (217, 'yx_store_order', 'pay_postage', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '支付邮费', NULL);
INSERT INTO `column_config` VALUES (218, 'yx_store_order', 'deduction_price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '抵扣金额', NULL);
INSERT INTO `column_config` VALUES (219, 'yx_store_order', 'coupon_id', 'int', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '优惠券id', NULL);
INSERT INTO `column_config` VALUES (220, 'yx_store_order', 'coupon_price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '优惠券金额', NULL);
INSERT INTO `column_config` VALUES (221, 'yx_store_order', 'paid', 'tinyint', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '支付状态', NULL);
INSERT INTO `column_config` VALUES (222, 'yx_store_order', 'pay_time', 'int', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '支付时间', NULL);
INSERT INTO `column_config` VALUES (223, 'yx_store_order', 'pay_type', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '支付方式', NULL);
INSERT INTO `column_config` VALUES (224, 'yx_store_order', 'add_time', 'int', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '创建时间', NULL);
INSERT INTO `column_config` VALUES (225, 'yx_store_order', 'status', 'tinyint', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '订单状态（-1 : 申请退款 -2 : 退货成功 0：待发货；1：待收货；2：已收货；3：待评价；-1：已退款）', NULL);
INSERT INTO `column_config` VALUES (226, 'yx_store_order', 'refund_status', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '0 未退款 1 申请中 2 已退款', NULL);
INSERT INTO `column_config` VALUES (227, 'yx_store_order', 'refund_reason_wap_img', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '退款图片', NULL);
INSERT INTO `column_config` VALUES (228, 'yx_store_order', 'refund_reason_wap_explain', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '退款用户说明', NULL);
INSERT INTO `column_config` VALUES (229, 'yx_store_order', 'refund_reason_time', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '退款时间', NULL);
INSERT INTO `column_config` VALUES (230, 'yx_store_order', 'refund_reason_wap', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '前台退款原因', NULL);
INSERT INTO `column_config` VALUES (231, 'yx_store_order', 'refund_reason', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '不退款的理由', NULL);
INSERT INTO `column_config` VALUES (232, 'yx_store_order', 'refund_price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '退款金额', NULL);
INSERT INTO `column_config` VALUES (233, 'yx_store_order', 'delivery_sn', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '快递公司编号', NULL);
INSERT INTO `column_config` VALUES (234, 'yx_store_order', 'delivery_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '快递名称/送货人姓名', NULL);
INSERT INTO `column_config` VALUES (235, 'yx_store_order', 'delivery_type', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '发货类型', NULL);
INSERT INTO `column_config` VALUES (236, 'yx_store_order', 'delivery_id', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '快递单号/手机号', NULL);
INSERT INTO `column_config` VALUES (237, 'yx_store_order', 'gain_integral', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '消费赚取积分', NULL);
INSERT INTO `column_config` VALUES (238, 'yx_store_order', 'use_integral', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '使用积分', NULL);
INSERT INTO `column_config` VALUES (239, 'yx_store_order', 'back_integral', 'decimal', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '给用户退了多少积分', NULL);
INSERT INTO `column_config` VALUES (240, 'yx_store_order', 'mark', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '备注', NULL);
INSERT INTO `column_config` VALUES (241, 'yx_store_order', 'is_del', 'tinyint', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '是否删除', NULL);
INSERT INTO `column_config` VALUES (242, 'yx_store_order', 'unique', 'char', NULL, '', b'1', NULL, 'UNI', b'1', b'1', NULL, '唯一id(md5加密)类似id', NULL);
INSERT INTO `column_config` VALUES (243, 'yx_store_order', 'remark', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '管理员备注', NULL);
INSERT INTO `column_config` VALUES (244, 'yx_store_order', 'mer_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '商户ID', NULL);
INSERT INTO `column_config` VALUES (245, 'yx_store_order', 'is_mer_check', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '', NULL);
INSERT INTO `column_config` VALUES (246, 'yx_store_order', 'combination_id', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '拼团产品id0一般产品', NULL);
INSERT INTO `column_config` VALUES (247, 'yx_store_order', 'pink_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '拼团id 0没有拼团', NULL);
INSERT INTO `column_config` VALUES (248, 'yx_store_order', 'cost', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '成本价', NULL);
INSERT INTO `column_config` VALUES (249, 'yx_store_order', 'seckill_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '秒杀产品ID', NULL);
INSERT INTO `column_config` VALUES (250, 'yx_store_order', 'bargain_id', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '砍价id', NULL);
INSERT INTO `column_config` VALUES (251, 'yx_store_order', 'verify_code', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '核销码', NULL);
INSERT INTO `column_config` VALUES (252, 'yx_store_order', 'store_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '门店id', NULL);
INSERT INTO `column_config` VALUES (253, 'yx_store_order', 'shipping_type', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '配送方式 1=快递 ，2=门店自提', NULL);
INSERT INTO `column_config` VALUES (254, 'yx_store_order', 'is_channel', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '支付渠道(0微信公众号1微信小程序)', NULL);
INSERT INTO `column_config` VALUES (255, 'yx_store_order', 'is_remind', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (256, 'yx_store_order', 'is_system_del', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (257, 'yx_user_recharge', 'id', 'int', NULL, 'auto_increment', b'0', NULL, 'PRI', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (258, 'yx_user_recharge', 'uid', 'int', NULL, '', b'0', NULL, 'MUL', b'0', b'0', NULL, '充值用户UID', NULL);
INSERT INTO `column_config` VALUES (259, 'yx_user_recharge', 'order_id', 'varchar', NULL, '', b'0', NULL, 'UNI', b'1', b'0', NULL, '订单号', NULL);
INSERT INTO `column_config` VALUES (260, 'yx_user_recharge', 'price', 'decimal', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '充值金额', NULL);
INSERT INTO `column_config` VALUES (261, 'yx_user_recharge', 'recharge_type', 'varchar', NULL, '', b'0', NULL, 'MUL', b'1', b'0', NULL, '充值类型', NULL);
INSERT INTO `column_config` VALUES (262, 'yx_user_recharge', 'paid', 'tinyint', NULL, '', b'0', NULL, 'MUL', b'1', b'0', NULL, '是否充值', NULL);
INSERT INTO `column_config` VALUES (263, 'yx_user_recharge', 'pay_time', 'int', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '充值支付时间', NULL);
INSERT INTO `column_config` VALUES (264, 'yx_user_recharge', 'add_time', 'int', NULL, '', b'0', NULL, '', b'1', b'0', NULL, '充值时间', NULL);
INSERT INTO `column_config` VALUES (265, 'yx_user_recharge', 'refund_price', 'decimal', NULL, '', b'0', NULL, '', b'0', b'0', NULL, '退款金额', NULL);
INSERT INTO `column_config` VALUES (266, 'yx_user_recharge', 'nickname', 'varchar', NULL, '', b'0', NULL, '', b'1', b'0', 'Like', '昵称', NULL);
INSERT INTO `column_config` VALUES (267, 'yx_system_store', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (268, 'yx_system_store', 'name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '门店名称', NULL);
INSERT INTO `column_config` VALUES (269, 'yx_system_store', 'introduction', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '简介', NULL);
INSERT INTO `column_config` VALUES (270, 'yx_system_store', 'phone', 'char', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '手机号码', NULL);
INSERT INTO `column_config` VALUES (271, 'yx_system_store', 'address', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '省市区', NULL);
INSERT INTO `column_config` VALUES (272, 'yx_system_store', 'detailed_address', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '详细地址', NULL);
INSERT INTO `column_config` VALUES (273, 'yx_system_store', 'image', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '门店logo', NULL);
INSERT INTO `column_config` VALUES (274, 'yx_system_store', 'latitude', 'char', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '纬度', NULL);
INSERT INTO `column_config` VALUES (275, 'yx_system_store', 'longitude', 'char', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '经度', NULL);
INSERT INTO `column_config` VALUES (276, 'yx_system_store', 'valid_time', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '核销有效日期', NULL);
INSERT INTO `column_config` VALUES (277, 'yx_system_store', 'day_time', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '每日营业开关时间', NULL);
INSERT INTO `column_config` VALUES (278, 'yx_system_store', 'add_time', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '添加时间', NULL);
INSERT INTO `column_config` VALUES (279, 'yx_system_store', 'is_show', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '是否显示', NULL);
INSERT INTO `column_config` VALUES (280, 'yx_system_store', 'is_del', 'tinyint', NULL, '', b'1', NULL, '', b'0', b'1', NULL, '是否删除', NULL);
INSERT INTO `column_config` VALUES (281, 'yx_system_store_staff', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (282, 'yx_system_store_staff', 'uid', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '微信用户id', NULL);
INSERT INTO `column_config` VALUES (283, 'yx_system_store_staff', 'avatar', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '店员头像', NULL);
INSERT INTO `column_config` VALUES (284, 'yx_system_store_staff', 'store_id', 'int', NULL, '', b'1', 'Select', '', b'1', b'1', NULL, '门店id', NULL);
INSERT INTO `column_config` VALUES (285, 'yx_system_store_staff', 'staff_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', 'Like', '店员名称', NULL);
INSERT INTO `column_config` VALUES (286, 'yx_system_store_staff', 'phone', 'char', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '手机号码', NULL);
INSERT INTO `column_config` VALUES (287, 'yx_system_store_staff', 'verify_status', 'tinyint', NULL, '', b'1', 'Radio', '', b'1', b'1', NULL, '核销开关', NULL);
INSERT INTO `column_config` VALUES (288, 'yx_system_store_staff', 'status', 'tinyint', NULL, '', b'1', 'Radio', '', b'1', b'0', NULL, '状态', NULL);
INSERT INTO `column_config` VALUES (289, 'yx_system_store_staff', 'add_time', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '添加时间', NULL);
INSERT INTO `column_config` VALUES (290, 'yx_system_store_staff', 'nickname', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', 'Like', '微信昵称', NULL);
INSERT INTO `column_config` VALUES (291, 'yx_system_store_staff', 'store_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '所属门店', NULL);
INSERT INTO `column_config` VALUES (292, 'yx_wechat_user', 'uid', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '微信用户id', NULL);
INSERT INTO `column_config` VALUES (293, 'yx_wechat_user', 'unionid', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段', NULL);
INSERT INTO `column_config` VALUES (294, 'yx_wechat_user', 'openid', 'varchar', NULL, '', b'1', NULL, 'UNI', b'1', b'0', NULL, '用户的标识，对当前公众号唯一', NULL);
INSERT INTO `column_config` VALUES (295, 'yx_wechat_user', 'routine_openid', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '小程序唯一身份ID', NULL);
INSERT INTO `column_config` VALUES (296, 'yx_wechat_user', 'nickname', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户的昵称', NULL);
INSERT INTO `column_config` VALUES (297, 'yx_wechat_user', 'headimgurl', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户头像', NULL);
INSERT INTO `column_config` VALUES (298, 'yx_wechat_user', 'sex', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户的性别，值为1时是男性，值为2时是女性，值为0时是未知', NULL);
INSERT INTO `column_config` VALUES (299, 'yx_wechat_user', 'city', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户所在城市', NULL);
INSERT INTO `column_config` VALUES (300, 'yx_wechat_user', 'language', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户的语言，简体中文为zh_CN', NULL);
INSERT INTO `column_config` VALUES (301, 'yx_wechat_user', 'province', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户所在省份', NULL);
INSERT INTO `column_config` VALUES (302, 'yx_wechat_user', 'country', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户所在国家', NULL);
INSERT INTO `column_config` VALUES (303, 'yx_wechat_user', 'remark', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '公众号运营者对粉丝的备注，公众号运营者可在微信公众平台用户管理界面对粉丝添加备注', NULL);
INSERT INTO `column_config` VALUES (304, 'yx_wechat_user', 'groupid', 'smallint', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '用户所在的分组ID（兼容旧的用户分组接口）', NULL);
INSERT INTO `column_config` VALUES (305, 'yx_wechat_user', 'tagid_list', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '用户被打上的标签ID列表', NULL);
INSERT INTO `column_config` VALUES (306, 'yx_wechat_user', 'subscribe', 'tinyint', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '用户是否订阅该公众号标识', NULL);
INSERT INTO `column_config` VALUES (307, 'yx_wechat_user', 'subscribe_time', 'int', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '关注公众号时间', NULL);
INSERT INTO `column_config` VALUES (308, 'yx_wechat_user', 'add_time', 'int', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '添加时间', NULL);
INSERT INTO `column_config` VALUES (309, 'yx_wechat_user', 'stair', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '一级推荐人', NULL);
INSERT INTO `column_config` VALUES (310, 'yx_wechat_user', 'second', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '二级推荐人', NULL);
INSERT INTO `column_config` VALUES (311, 'yx_wechat_user', 'order_stair', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '一级推荐人订单', NULL);
INSERT INTO `column_config` VALUES (312, 'yx_wechat_user', 'order_second', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '二级推荐人订单', NULL);
INSERT INTO `column_config` VALUES (313, 'yx_wechat_user', 'now_money', 'decimal', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '佣金', NULL);
INSERT INTO `column_config` VALUES (314, 'yx_wechat_user', 'session_key', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '小程序用户会话密匙', NULL);
INSERT INTO `column_config` VALUES (315, 'yx_wechat_user', 'user_type', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '用户类型', NULL);
INSERT INTO `column_config` VALUES (316, 'yx_express', 'id', 'mediumint', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '快递公司id', NULL);
INSERT INTO `column_config` VALUES (317, 'yx_express', 'code', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '快递公司简称', NULL);
INSERT INTO `column_config` VALUES (318, 'yx_express', 'name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '快递公司全称', NULL);
INSERT INTO `column_config` VALUES (319, 'yx_express', 'sort', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '排序', NULL);
INSERT INTO `column_config` VALUES (320, 'yx_express', 'is_show', 'tinyint', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '是否显示', NULL);
INSERT INTO `column_config` VALUES (321, 'yx_user_level', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (322, 'yx_user_level', 'uid', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '用户uid', NULL);
INSERT INTO `column_config` VALUES (323, 'yx_user_level', 'level_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '等级vip', NULL);
INSERT INTO `column_config` VALUES (324, 'yx_user_level', 'grade', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '会员等级', NULL);
INSERT INTO `column_config` VALUES (325, 'yx_user_level', 'valid_time', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '过期时间', NULL);
INSERT INTO `column_config` VALUES (326, 'yx_user_level', 'is_forever', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '是否永久', NULL);
INSERT INTO `column_config` VALUES (327, 'yx_user_level', 'mer_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '商户id', NULL);
INSERT INTO `column_config` VALUES (328, 'yx_user_level', 'status', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '0:禁止,1:正常', NULL);
INSERT INTO `column_config` VALUES (329, 'yx_user_level', 'mark', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '备注', NULL);
INSERT INTO `column_config` VALUES (330, 'yx_user_level', 'remind', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '是否已通知', NULL);
INSERT INTO `column_config` VALUES (331, 'yx_user_level', 'is_del', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '是否删除,0=未删除,1=删除', NULL);
INSERT INTO `column_config` VALUES (332, 'yx_user_level', 'add_time', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '添加时间', NULL);
INSERT INTO `column_config` VALUES (333, 'yx_user_level', 'discount', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '享受折扣', NULL);
INSERT INTO `column_config` VALUES (334, 'yx_store_product_rule', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (335, 'yx_store_product_rule', 'rule_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '规格名称', NULL);
INSERT INTO `column_config` VALUES (336, 'yx_store_product_rule', 'rule_value', 'json', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '规格值', NULL);
INSERT INTO `column_config` VALUES (337, 'yx_store_product_rule', 'create_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (338, 'yx_store_product_rule', 'update_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (339, 'yx_store_product_rule', 'is_del', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (340, 'yx_shipping_templates', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '模板ID', NULL);
INSERT INTO `column_config` VALUES (341, 'yx_shipping_templates', 'name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '模板名称', NULL);
INSERT INTO `column_config` VALUES (342, 'yx_shipping_templates', 'type', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '计费方式', NULL);
INSERT INTO `column_config` VALUES (343, 'yx_shipping_templates', 'region_info', 'text', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '地域以及费用', NULL);
INSERT INTO `column_config` VALUES (344, 'yx_shipping_templates', 'appoint', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '指定包邮开关', NULL);
INSERT INTO `column_config` VALUES (345, 'yx_shipping_templates', 'appoint_info', 'text', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '指定包邮内容', NULL);
INSERT INTO `column_config` VALUES (346, 'yx_shipping_templates', 'create_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '添加时间', NULL);
INSERT INTO `column_config` VALUES (347, 'yx_shipping_templates', 'update_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (348, 'yx_shipping_templates', 'is_del', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (349, 'yx_shipping_templates', 'sort', 'int', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '排序', NULL);
INSERT INTO `column_config` VALUES (350, 'yx_shipping_templates_region', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '编号', NULL);
INSERT INTO `column_config` VALUES (351, 'yx_shipping_templates_region', 'province_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '省ID', NULL);
INSERT INTO `column_config` VALUES (352, 'yx_shipping_templates_region', 'temp_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '模板ID', NULL);
INSERT INTO `column_config` VALUES (353, 'yx_shipping_templates_region', 'city_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '城市ID', NULL);
INSERT INTO `column_config` VALUES (354, 'yx_shipping_templates_region', 'first', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '首件', NULL);
INSERT INTO `column_config` VALUES (355, 'yx_shipping_templates_region', 'first_price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '首件运费', NULL);
INSERT INTO `column_config` VALUES (356, 'yx_shipping_templates_region', 'continues', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '续件', NULL);
INSERT INTO `column_config` VALUES (357, 'yx_shipping_templates_region', 'continue_price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '续件运费', NULL);
INSERT INTO `column_config` VALUES (358, 'yx_shipping_templates_region', 'type', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '计费方式', NULL);
INSERT INTO `column_config` VALUES (359, 'yx_shipping_templates_region', 'uniqid', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '分组唯一值', NULL);
INSERT INTO `column_config` VALUES (360, 'yx_shipping_templates_free', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '编号', NULL);
INSERT INTO `column_config` VALUES (361, 'yx_shipping_templates_free', 'province_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '省ID', NULL);
INSERT INTO `column_config` VALUES (362, 'yx_shipping_templates_free', 'temp_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '模板ID', NULL);
INSERT INTO `column_config` VALUES (363, 'yx_shipping_templates_free', 'city_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '城市ID', NULL);
INSERT INTO `column_config` VALUES (364, 'yx_shipping_templates_free', 'number', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '包邮件数', NULL);
INSERT INTO `column_config` VALUES (365, 'yx_shipping_templates_free', 'price', 'decimal', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '包邮金额', NULL);
INSERT INTO `column_config` VALUES (366, 'yx_shipping_templates_free', 'type', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '计费方式', NULL);
INSERT INTO `column_config` VALUES (367, 'yx_shipping_templates_free', 'uniqid', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '分组唯一值', NULL);
INSERT INTO `column_config` VALUES (368, 'yx_system_city', 'id', 'int', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (369, 'yx_system_city', 'city_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '城市id', NULL);
INSERT INTO `column_config` VALUES (370, 'yx_system_city', 'level', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '省市级别', NULL);
INSERT INTO `column_config` VALUES (371, 'yx_system_city', 'parent_id', 'int', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '父级id', NULL);
INSERT INTO `column_config` VALUES (372, 'yx_system_city', 'area_code', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '区号', NULL);
INSERT INTO `column_config` VALUES (373, 'yx_system_city', 'name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '名称', NULL);
INSERT INTO `column_config` VALUES (374, 'yx_system_city', 'merger_name', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '合并名称', NULL);
INSERT INTO `column_config` VALUES (375, 'yx_system_city', 'lng', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '经度', NULL);
INSERT INTO `column_config` VALUES (376, 'yx_system_city', 'lat', 'varchar', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '纬度', NULL);
INSERT INTO `column_config` VALUES (377, 'yx_system_city', 'is_show', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '是否展示', NULL);
INSERT INTO `column_config` VALUES (378, 'yx_store_product_relation', 'id', 'bigint', NULL, 'auto_increment', b'1', NULL, 'PRI', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (379, 'yx_store_product_relation', 'uid', 'bigint', NULL, '', b'1', NULL, 'MUL', b'1', b'1', NULL, '用户ID', NULL);
INSERT INTO `column_config` VALUES (380, 'yx_store_product_relation', 'product_id', 'bigint', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '商品ID', NULL);
INSERT INTO `column_config` VALUES (381, 'yx_store_product_relation', 'type', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '类型(收藏(collect）、点赞(like))', NULL);
INSERT INTO `column_config` VALUES (382, 'yx_store_product_relation', 'category', 'varchar', NULL, '', b'1', NULL, 'MUL', b'1', b'0', NULL, '某种类型的商品(普通商品、秒杀商品)', NULL);
INSERT INTO `column_config` VALUES (383, 'yx_store_product_relation', 'create_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'1', NULL, '添加时间', NULL);
INSERT INTO `column_config` VALUES (384, 'yx_store_product_relation', 'update_time', 'datetime', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);
INSERT INTO `column_config` VALUES (385, 'yx_store_product_relation', 'is_del', 'tinyint', NULL, '', b'1', NULL, '', b'1', b'0', NULL, '', NULL);

-- ----------------------------
-- Table structure for dept
-- ----------------------------
DROP TABLE IF EXISTS `dept`;
CREATE TABLE `dept`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `pid` bigint(20) NOT NULL COMMENT '上级部门',
  `enabled` bit(1) NOT NULL COMMENT '状态',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '部门' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of dept
-- ----------------------------
INSERT INTO `dept` VALUES (1, 'YSHOP', 0, b'1', '2019-03-01 12:07:37', NULL, 0);
INSERT INTO `dept` VALUES (2, '研发部', 7, b'1', '2019-03-25 09:15:32', NULL, 0);
INSERT INTO `dept` VALUES (5, '运维部', 7, b'1', '2019-03-25 09:20:44', NULL, 0);
INSERT INTO `dept` VALUES (6, '测试部', 8, b'1', '2019-03-25 09:52:18', NULL, 0);
INSERT INTO `dept` VALUES (7, '华南分部', 1, b'1', '2019-03-25 11:04:50', NULL, 0);
INSERT INTO `dept` VALUES (8, '华北分部', 1, b'1', '2019-03-25 11:04:53', NULL, 0);
INSERT INTO `dept` VALUES (11, '人事部', 8, b'1', '2019-03-25 11:07:58', NULL, 0);
INSERT INTO `dept` VALUES (12, '7773', 1, b'1', '2020-05-18 19:43:53', NULL, 0);

-- ----------------------------
-- Table structure for dict
-- ----------------------------
DROP TABLE IF EXISTS `dict`;
CREATE TABLE `dict`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典名称',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据字典' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of dict
-- ----------------------------
INSERT INTO `dict` VALUES (1, 'user_status', '用户状态', '2019-10-27 20:31:36', NULL, 0);
INSERT INTO `dict` VALUES (4, 'dept_status', '部门状态', '2019-10-27 20:31:36', NULL, 0);
INSERT INTO `dict` VALUES (5, 'job_status', '岗位状态', '2019-10-27 20:31:36', NULL, 0);
INSERT INTO `dict` VALUES (6, '33', '3', '2020-05-18 19:55:49', NULL, 0);
INSERT INTO `dict` VALUES (16, 'force_update', '强制升级', '2020-12-09 11:13:21', NULL, 0);
INSERT INTO `dict` VALUES (17, 'is_enable', '是否启用', '2020-12-10 12:02:57', NULL, 0);

-- ----------------------------
-- Table structure for dict_detail
-- ----------------------------
DROP TABLE IF EXISTS `dict_detail`;
CREATE TABLE `dict_detail`  (
  `id` bigint(11) NOT NULL AUTO_INCREMENT,
  `label` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典标签',
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字典值',
  `sort` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '排序',
  `dict_id` bigint(11) NULL DEFAULT NULL COMMENT '字典id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FK5tpkputc6d9nboxojdbgnpmyb`(`dict_id`) USING BTREE,
  CONSTRAINT `FK5tpkputc6d9nboxojdbgnpmyb` FOREIGN KEY (`dict_id`) REFERENCES `dict` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '数据字典详情' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of dict_detail
-- ----------------------------
INSERT INTO `dict_detail` VALUES (1, '激活', 'true', '1', 1, '2019-10-27 20:31:36', NULL, 0);
INSERT INTO `dict_detail` VALUES (2, '禁用', 'false', '2', 1, NULL, NULL, 0);
INSERT INTO `dict_detail` VALUES (3, '启用', 'true', '1', 4, NULL, NULL, 0);
INSERT INTO `dict_detail` VALUES (4, '停用', 'false', '2', 4, '2019-10-27 20:31:36', NULL, 0);
INSERT INTO `dict_detail` VALUES (5, '启用2', 'true', '1', 5, NULL, NULL, 0);
INSERT INTO `dict_detail` VALUES (6, '停用', 'false', '2', 5, '2019-10-27 20:31:36', NULL, 0);
INSERT INTO `dict_detail` VALUES (7, '8', '8', '999', NULL, '2020-05-18 19:44:05', NULL, 0);
INSERT INTO `dict_detail` VALUES (8, '99', '999', '999', NULL, '2020-05-18 19:44:31', NULL, 0);
INSERT INTO `dict_detail` VALUES (20, '是', '1', '999', 16, '2020-12-09 11:41:30', NULL, 0);
INSERT INTO `dict_detail` VALUES (21, '否', '0', '999', 16, '2020-12-09 11:41:36', NULL, 0);
INSERT INTO `dict_detail` VALUES (22, '是', '1', '999', 17, '2020-12-10 12:03:09', NULL, 0);
INSERT INTO `dict_detail` VALUES (23, '否', '0', '999', 17, '2020-12-10 12:03:16', NULL, 0);

-- ----------------------------
-- Table structure for email_config
-- ----------------------------
DROP TABLE IF EXISTS `email_config`;
CREATE TABLE `email_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `from_user` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '收件人',
  `host` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮件服务器SMTP地址',
  `pass` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `port` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '端口',
  `adminUser` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发件者用户名',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '邮箱配置' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of email_config
-- ----------------------------
INSERT INTO `email_config` VALUES (1, '<EMAIL>', '111', '111', '111', '1');

-- ----------------------------
-- Table structure for gen_config
-- ----------------------------
DROP TABLE IF EXISTS `gen_config`;
CREATE TABLE `gen_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `table_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表名',
  `author` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '作者',
  `cover` bit(1) NULL DEFAULT NULL COMMENT '是否覆盖',
  `module_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模块名称',
  `pack` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '至于哪个包下',
  `path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '前端代码生成的路径',
  `api_path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '前端Api文件路径',
  `prefix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '表前缀',
  `api_alias` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接口名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代码生成器配置' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of gen_config
-- ----------------------------
INSERT INTO `gen_config` VALUES (3, 'gen_test', 'le', b'1', 'eladmin-system', 'me.zhengjie.gen', 'E:\\workspace\\me\\front\\eladmin-web\\src\\views\\gen', 'E:\\workspace\\me\\front\\eladmin-web\\src\\api', NULL, '测试生成');
INSERT INTO `gen_config` VALUES (4, 'yx_material_group', 'hupeng', b'0', 'yshop-shop', 'com.leway.modules.shop', 'E:\\output', 'E:\\output\\', NULL, '素材');
INSERT INTO `gen_config` VALUES (5, 'yx_material', 'hupeng', b'0', 'yshop-shop', 'com.leway.modules.shop', 'E:\\output', 'E:\\output\\', NULL, '素材管理');
INSERT INTO `gen_config` VALUES (6, 'yx_user', 'hupeng', b'0', 'yshop-system', 'com.leway.modules', 'aa', 'aa\\', NULL, '用户');
INSERT INTO `gen_config` VALUES (7, 'yx_wechat_media', 'hupeng', b'0', 'yshop-system', 'com.leway.modules', 'E:\\book\\img', 'E:\\book\\img\\', NULL, 'ceshi');
INSERT INTO `gen_config` VALUES (8, 'yx_user_recharge', 'hupeng', b'0', 'yshop-shop', 'com.leway.modules.shop', 'E:\\java\\yxshop-private\\yshop-web\\src\\views\\shop\\recharge', 'E:\\java\\yxshop-private\\yshop-web\\src\\api', '', '充值管理');
INSERT INTO `gen_config` VALUES (9, 'yx_system_store', 'hupeng', b'0', 'yshop-shop', 'com.leway.modules.shop', 'E:\\java\\yxshop-private\\yshop-web\\src\\views\\shop\\store', 'E:\\java\\yxshop-private\\yshop-web\\src\\api', NULL, '门店');
INSERT INTO `gen_config` VALUES (10, 'yx_system_store_staff', 'hupeng', b'1', 'yshop-shop', 'com.leway.modules.shop', 'E:\\java\\yxshop-private\\yshop-web\\src\\views\\shop\\storestaff', 'E:\\java\\yxshop-private\\yshop-web\\src\\api', NULL, '门店店员');
INSERT INTO `gen_config` VALUES (11, 'yx_store_product_rule', 'hupeng', b'0', 'yshop-mall', 'com.leway.modules', 'product', 'product\\', NULL, 'sku规则');
INSERT INTO `gen_config` VALUES (12, 'yx_shipping_templates', 'hupeng', b'0', 'yshop-mall', 'com.leway.modules', 'product', 'product\\', NULL, '运费模板');
INSERT INTO `gen_config` VALUES (13, 'yx_shipping_templates_region', 'hupeng', b'0', 'yshop-mall', 'com.leway.modules', 'product', 'product\\', NULL, '运费模板区域');
INSERT INTO `gen_config` VALUES (14, 'yx_shipping_templates_free', 'hupeng', b'0', 'yshop-mall', 'com.leway.modules', 'product', 'product\\', NULL, '运费模板免费');
INSERT INTO `gen_config` VALUES (15, 'yx_system_city', 'hupeng', b'0', 'yshop-mall', 'com.leway.modules', 'product', 'product\\', NULL, '城市');
INSERT INTO `gen_config` VALUES (16, 'yx_store_product_relation', 'hupeng', b'0', 'yshop-mall', 'com.leway.modules.product', 'd:/', 'd:/', NULL, 'ProductRelation');

-- ----------------------------
-- Table structure for gen_test
-- ----------------------------
DROP TABLE IF EXISTS `gen_test`;
CREATE TABLE `gen_test`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sex` int(255) NULL DEFAULT NULL COMMENT '性别',
  `create_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '代码生成测试' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of gen_test
-- ----------------------------

-- ----------------------------
-- Table structure for job
-- ----------------------------
DROP TABLE IF EXISTS `job`;
CREATE TABLE `job`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '岗位名称',
  `enabled` bit(1) NOT NULL COMMENT '岗位状态',
  `sort` bigint(20) NOT NULL COMMENT '岗位排序',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKmvhj0rogastlctflsxf1d6k3i`(`dept_id`) USING BTREE,
  CONSTRAINT `FKmvhj0rogastlctflsxf1d6k3i` FOREIGN KEY (`dept_id`) REFERENCES `dept` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '岗位' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of job
-- ----------------------------
INSERT INTO `job` VALUES (8, '人事专员', b'1', 3, 11, '2019-03-29 14:52:28', NULL, 0);
INSERT INTO `job` VALUES (10, '产品经理', b'1', 4, 2, '2019-03-29 14:55:51', NULL, 0);
INSERT INTO `job` VALUES (11, '全栈开发2', b'1', 2, 2, '2019-03-31 13:39:30', NULL, 0);
INSERT INTO `job` VALUES (12, '软件测试', b'1', 5, 2, '2019-03-31 13:39:43', NULL, 0);

-- ----------------------------
-- Table structure for local_storage
-- ----------------------------
DROP TABLE IF EXISTS `local_storage`;
CREATE TABLE `local_storage`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `real_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件真实的名称',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名',
  `suffix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '后缀',
  `path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型',
  `size` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大小',
  `operate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '操作人',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 64 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '本地存储' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of local_storage
-- ----------------------------
INSERT INTO `local_storage` VALUES (1, 'list_30-20200107120524671.png', 'list_30', 'png', 'E:\\yshop\\file\\图片\\list_30-20200107120524671.png', '图片', '28.57KB   ', 'admin', '2020-01-07 12:05:25');
INSERT INTO `local_storage` VALUES (2, 'list_34-20200109071140374.png', 'list_34', 'png', 'E:\\yshop\\file\\图片\\list_34-20200109071140374.png', '图片', '27.05KB   ', 'admin', '2020-01-09 19:11:40');
INSERT INTO `local_storage` VALUES (3, 'list_22-20200109072256534.png', 'list_22', 'png', 'E:\\yshop\\file\\pic\\list_22-20200109072256534.png', 'pic', '28.23KB   ', 'admin', '2020-01-09 19:22:57');
INSERT INTO `local_storage` VALUES (4, 'list_32-20200109093407741.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-20200109093407741.png', 'pic', '27.65KB   ', 'admin', '2020-01-09 21:34:08');
INSERT INTO `local_storage` VALUES (5, 'list_32-20200109094223500.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-20200109094223500.png', 'pic', '27.65KB   ', 'admin', '2020-01-09 21:42:24');
INSERT INTO `local_storage` VALUES (6, 'list_32-20200109094413459.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-20200109094413459.png', 'pic', '27.65KB   ', 'admin', '2020-01-09 21:44:13');
INSERT INTO `local_storage` VALUES (7, 'list_32-20200109094652138.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-20200109094652138.png', 'pic', '27.65KB   ', 'admin', '2020-01-09 21:46:52');
INSERT INTO `local_storage` VALUES (8, 'list_34-20200109095146476.png', 'list_34', 'png', 'E:\\yshop\\file\\pic\\list_34-20200109095146476.png', 'pic', '27.05KB   ', 'admin', '2020-01-09 21:51:47');
INSERT INTO `local_storage` VALUES (9, 'list_32-20200109095700685.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-20200109095700685.png', 'pic', '27.65KB   ', 'admin', '2020-01-09 21:57:01');
INSERT INTO `local_storage` VALUES (10, 'list_20-20200109095936988.png', 'list_20', 'png', 'E:\\yshop\\file\\pic\\list_20-20200109095936988.png', 'pic', '22.92KB   ', 'admin', '2020-01-09 21:59:37');
INSERT INTO `local_storage` VALUES (11, 'list_32-20200109100213309.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-20200109100213309.png', 'pic', '27.65KB   ', 'admin', '2020-01-09 22:02:13');
INSERT INTO `local_storage` VALUES (12, 'list_30-20200109104513493.png', 'list_30', 'png', 'E:\\yshop\\file\\pic\\list_30-20200109104513493.png', 'pic', '28.57KB   ', 'admin', '2020-01-09 22:45:13');
INSERT INTO `local_storage` VALUES (13, 'list_16-20200110120838173.png', 'list_16', 'png', 'E:\\yshop\\file\\pic\\list_16-20200110120838173.png', 'pic', '28.93KB   ', 'admin', '2020-01-10 12:08:38');
INSERT INTO `local_storage` VALUES (14, 'list_32-20200110035831202.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-20200110035831202.png', 'pic', '27.65KB   ', 'admin', '2020-01-10 15:58:31');
INSERT INTO `local_storage` VALUES (15, 'list_32-2020011004054091.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-2020011004054091.png', 'pic', '27.65KB   ', 'admin', '2020-01-10 16:05:40');
INSERT INTO `local_storage` VALUES (16, 'list_30-20200110053337209.png', 'list_30', 'png', 'E:\\yshop\\file\\pic\\list_30-20200110053337209.png', 'pic', '28.57KB   ', 'admin', '2020-01-10 17:33:37');
INSERT INTO `local_storage` VALUES (17, 'list_32-20200110064436937.png', 'list_32', 'png', 'E:\\yshop\\file\\pic\\list_32-20200110064436937.png', 'pic', '27.65KB   ', 'admin', '2020-01-10 18:44:37');
INSERT INTO `local_storage` VALUES (18, 'list_22-20200110104217508.png', 'list_22', 'png', 'E:\\yshop\\file\\pic\\list_22-20200110104217508.png', 'pic', '28.23KB   ', 'admin', '2020-01-10 22:42:18');
INSERT INTO `local_storage` VALUES (19, 'list_18-20200111051038569.png', 'list_18', 'png', 'E:\\yshop\\file\\pic\\list_18-20200111051038569.png', 'pic', '29.62KB   ', '15136175246', '2020-01-11 17:10:39');
INSERT INTO `local_storage` VALUES (20, 'list_30-2020011105115469.png', 'list_30', 'png', 'E:\\yshop\\file\\pic\\list_30-2020011105115469.png', 'pic', '28.57KB   ', '15136175246', '2020-01-11 17:11:54');
INSERT INTO `local_storage` VALUES (21, 'list_30-20200111051343335.png', 'list_30', 'png', 'E:\\yshop\\file\\pic\\list_30-20200111051343335.png', 'pic', '28.57KB   ', '15136175246', '2020-01-11 17:13:43');
INSERT INTO `local_storage` VALUES (22, 'list_16-20200111051622984.png', 'list_16', 'png', 'E:\\yshop\\file\\pic\\list_16-20200111051622984.png', 'pic', '28.93KB   ', '15136175246', '2020-01-11 17:16:23');
INSERT INTO `local_storage` VALUES (23, 'list_24-20200111051630227.png', 'list_24', 'png', 'E:\\yshop\\file\\pic\\list_24-20200111051630227.png', 'pic', '27.31KB   ', '15136175246', '2020-01-11 17:16:30');
INSERT INTO `local_storage` VALUES (24, 'logo-20200131103905417.png', 'logo', 'png', 'E:\\yshop\\file\\pic\\logo-20200131103905417.png', 'pic', '48.39KB   ', 'admin', '2020-01-31 10:39:05');
INSERT INTO `local_storage` VALUES (25, 'avatar-20200131103928688.png', 'avatar', 'png', 'E:\\yshop\\file\\pic\\avatar-20200131103928688.png', 'pic', '1.82KB   ', 'admin', '2020-01-31 10:39:29');
INSERT INTO `local_storage` VALUES (26, 'list_13-2020013110394473.png', 'list_13', 'png', 'E:\\yshop\\file\\pic\\list_13-2020013110394473.png', 'pic', '23.05KB   ', 'admin', '2020-01-31 10:39:44');
INSERT INTO `local_storage` VALUES (27, 'list_18-2020013110394988.png', 'list_18', 'png', 'E:\\yshop\\file\\pic\\list_18-2020013110394988.png', 'pic', '29.62KB   ', 'admin', '2020-01-31 10:39:49');
INSERT INTO `local_storage` VALUES (28, 'list_22-20200131103952926.png', 'list_22', 'png', 'E:\\yshop\\file\\pic\\list_22-20200131103952926.png', 'pic', '28.23KB   ', 'admin', '2020-01-31 10:39:53');
INSERT INTO `local_storage` VALUES (29, 'list_26-20200131103956836.png', 'list_26', 'png', 'E:\\yshop\\file\\pic\\list_26-20200131103956836.png', 'pic', '28.36KB   ', 'admin', '2020-01-31 10:39:57');
INSERT INTO `local_storage` VALUES (30, 'list_28-20200131104001169.png', 'list_28', 'png', 'E:\\yshop\\file\\pic\\list_28-20200131104001169.png', 'pic', '28.38KB   ', 'admin', '2020-01-31 10:40:01');
INSERT INTO `local_storage` VALUES (31, 'list_16-20200207061544740.png', 'list_16', 'png', 'E:\\yshop\\file\\pic\\list_16-20200207061544740.png', 'pic', '28.93KB   ', 'hupeng', '2020-02-07 18:15:45');
INSERT INTO `local_storage` VALUES (32, 'list_30-2020020706162996.png', 'list_30', 'png', 'E:\\yshop\\file\\pic\\list_30-2020020706162996.png', 'pic', '28.57KB   ', 'hupeng', '2020-02-07 18:16:29');
INSERT INTO `local_storage` VALUES (33, 'list_16-20200207061714702.png', 'list_16', 'png', 'E:\\yshop\\file\\pic\\list_16-20200207061714702.png', 'pic', '28.93KB   ', 'hupeng', '2020-02-07 18:17:15');
INSERT INTO `local_storage` VALUES (34, 'list_18-20200207061743361.png', 'list_18', 'png', 'E:\\yshop\\file\\pic\\list_18-20200207061743361.png', 'pic', '29.62KB   ', 'hupeng', '2020-02-07 18:17:43');
INSERT INTO `local_storage` VALUES (35, 'list_30-20200207062920744.png', 'list_30', 'png', 'E:\\yshop\\file\\pic\\list_30-20200207062920744.png', 'pic', '28.57KB   ', 'hupeng', '2020-02-07 18:29:21');
INSERT INTO `local_storage` VALUES (36, '05ea40b831858a8cf423aa709840507c-20200228083801500.png', '05ea40b831858a8cf423aa709840507c', 'png', 'E:\\yshop\\file\\pic\\05ea40b831858a8cf423aa709840507c-20200228083801500.png', 'pic', '5.19KB   ', 'admin', '2020-02-28 20:38:02');
INSERT INTO `local_storage` VALUES (37, '05ea40b831858a8cf423aa709840507c-20200311043711341.png', '05ea40b831858a8cf423aa709840507c', 'png', 'E:\\yshop\\file\\pic\\05ea40b831858a8cf423aa709840507c-20200311043711341.png', 'pic', '5.19KB   ', 'admin', '2020-03-11 16:37:11');
INSERT INTO `local_storage` VALUES (38, '秒杀-2020031104371672.png', '秒杀', 'png', 'E:\\yshop\\file\\pic\\秒杀-2020031104371672.png', 'pic', '6.07KB   ', 'admin', '2020-03-11 16:37:16');
INSERT INTO `local_storage` VALUES (39, '砍价-20200311043720679.png', '砍价', 'png', 'E:\\yshop\\file\\pic\\砍价-20200311043720679.png', 'pic', '6.13KB   ', 'admin', '2020-03-11 16:37:21');
INSERT INTO `local_storage` VALUES (40, '优惠券-20200311043724709.png', '优惠券', 'png', 'E:\\yshop\\file\\pic\\优惠券-20200311043724709.png', 'pic', '5.45KB   ', 'admin', '2020-03-11 16:37:25');
INSERT INTO `local_storage` VALUES (41, '资讯-20200311043727918.png', '资讯', 'png', 'E:\\yshop\\file\\pic\\资讯-20200311043727918.png', 'pic', '5.19KB   ', 'admin', '2020-03-11 16:37:28');
INSERT INTO `local_storage` VALUES (42, 'list_16-20200322071324803.png', 'list_16', 'png', 'E:\\yshop\\file\\pic\\list_16-20200322071324803.png', 'pic', '28.93KB   ', 'admin', '2020-03-22 19:13:25');
INSERT INTO `local_storage` VALUES (43, 'list_22-20200322071348844.png', 'list_22', 'png', 'E:\\yshop\\file\\pic\\list_22-20200322071348844.png', 'pic', '28.23KB   ', 'admin', '2020-03-22 19:13:49');
INSERT INTO `local_storage` VALUES (44, 'list_34-2020032207154023.png', 'list_34', 'png', 'E:\\yshop\\file\\pic\\list_34-2020032207154023.png', 'pic', '27.05KB   ', 'admin', '2020-03-22 19:15:40');
INSERT INTO `local_storage` VALUES (45, '钻石-20200328094531898.jpg', '钻石', 'jpg', 'E:\\yshop\\file\\pic\\钻石-20200328094531898.jpg', 'pic', '32.42KB   ', 'admin', '2020-03-28 21:45:32');
INSERT INTO `local_storage` VALUES (46, '20200527164526793230.png', 'list_13', 'png', 'D:\\yshop\\file\\pic\\20200527164526793230.png', 'pic', '23.05KB   ', 'admin', '2020-05-27 16:45:27');
INSERT INTO `local_storage` VALUES (47, '20200527164543489004.png', 'list_18', 'png', 'D:\\yshop\\file\\pic\\20200527164543489004.png', 'pic', '29.62KB   ', 'admin', '2020-05-27 16:45:43');
INSERT INTO `local_storage` VALUES (48, '20200623200105269693.png', 'list_13', 'png', 'D:\\yshop\\file\\pic\\20200623200105269693.png', 'pic', '23.05KB   ', 'yshop', '2020-06-23 20:01:05');
INSERT INTO `local_storage` VALUES (49, '20200623200118648188.png', 'list_18', 'png', 'D:\\yshop\\file\\pic\\20200623200118648188.png', 'pic', '29.62KB   ', 'yshop', '2020-06-23 20:01:19');
INSERT INTO `local_storage` VALUES (50, '20200626110808157592.png', 'list_34', 'png', 'D:\\yshop\\file\\pic\\20200626110808157592.png', 'pic', '27.05KB   ', 'yshop', '2020-06-26 11:08:08');
INSERT INTO `local_storage` VALUES (51, '20200626172220229003.png', 'list_16', 'png', 'D:\\yshop\\file\\pic\\20200626172220229003.png', 'pic', '28.93KB   ', 'yshop', '2020-06-26 17:22:20');
INSERT INTO `local_storage` VALUES (52, '20200626173520849370.png', 'list_18', 'png', 'D:\\yshop\\file\\pic\\20200626173520849370.png', 'pic', '29.62KB   ', 'yshop', '2020-06-26 17:35:21');
INSERT INTO `local_storage` VALUES (53, '20200626173943611335.png', 'list_34', 'png', 'D:\\yshop\\file\\pic\\20200626173943611335.png', 'pic', '27.05KB   ', 'yshop', '2020-06-26 17:39:44');
INSERT INTO `local_storage` VALUES (54, '20200903171003417769.png', 'avatar_default', 'png', '/home/<USER>/file/pic/20200903171003417769.png', 'pic', '54.06KB   ', 'yshop', '2020-09-03 17:10:03');
INSERT INTO `local_storage` VALUES (55, '20200903171208889668.png', 'avatar_default', 'png', '/home/<USER>/file/pic/20200903171208889668.png', 'pic', '54.06KB   ', 'yshop', '2020-09-03 17:12:09');
INSERT INTO `local_storage` VALUES (56, '20200903171351713145.jpg', '1', 'jpg', '/home/<USER>/file/pic/20200903171351713145.jpg', 'pic', '126.59KB   ', 'yshop', '2020-09-03 17:13:52');
INSERT INTO `local_storage` VALUES (57, '20200903171807568739.jpg', '4', 'jpg', '/home/<USER>/file/pic/20200903171807568739.jpg', 'pic', '68.57KB   ', 'yshop', '2020-09-03 17:18:08');
INSERT INTO `local_storage` VALUES (58, '20200903171807584910.jpg', '3', 'jpg', '/home/<USER>/file/pic/20200903171807584910.jpg', 'pic', '34.32KB   ', 'yshop', '2020-09-03 17:18:08');
INSERT INTO `local_storage` VALUES (59, '20200903171807625149.jpg', '5', 'jpg', '/home/<USER>/file/pic/20200903171807625149.jpg', 'pic', '81.55KB   ', 'yshop', '2020-09-03 17:18:08');
INSERT INTO `local_storage` VALUES (60, '20200903171807641535.jpg', '6', 'jpg', '/home/<USER>/file/pic/20200903171807641535.jpg', 'pic', '61.97KB   ', 'yshop', '2020-09-03 17:18:08');
INSERT INTO `local_storage` VALUES (61, '20200903171807647067.jpg', '7', 'jpg', '/home/<USER>/file/pic/20200903171807647067.jpg', 'pic', '76.04KB   ', 'yshop', '2020-09-03 17:18:08');
INSERT INTO `local_storage` VALUES (62, '20200911093912577832.png', 'avatar_default', 'png', '/home/<USER>/file/pic/20200911093912577832.png', 'pic', '54.06KB   ', 'admin', '2020-09-11 09:39:13');
INSERT INTO `local_storage` VALUES (63, '20200915074139926178.jpg', '1600126884(1)', 'jpg', '/home/<USER>/file/pic/20200915074139926178.jpg', 'pic', '23.64KB   ', 'yshop', '2020-09-15 07:41:40');

-- ----------------------------
-- Table structure for log
-- ----------------------------
DROP TABLE IF EXISTS `log`;
CREATE TABLE `log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `exception_detail` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `log_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `method` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `params` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `request_ip` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `time` bigint(20) NULL DEFAULT NULL,
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `browser` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `type` int(1) NULL DEFAULT 0,
  `uid` bigint(20) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 45 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统日志' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Table structure for menu
-- ----------------------------
DROP TABLE IF EXISTS `menu`;
CREATE TABLE `menu`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `i_frame` bit(1) NULL DEFAULT NULL COMMENT '是否外链',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '菜单名称',
  `component` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '组件',
  `pid` bigint(20) NOT NULL COMMENT '上级菜单ID',
  `sort` bigint(20) NOT NULL COMMENT '排序',
  `icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图标',
  `path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '链接地址',
  `cache` bit(1) NULL DEFAULT b'0' COMMENT '缓存',
  `hidden` bit(1) NULL DEFAULT b'0' COMMENT '是否隐藏',
  `component_name` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '-' COMMENT '组件名称',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `permission` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '权限',
  `type` int(11) NULL DEFAULT NULL COMMENT '类型',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `FKqcf9gem97gqa5qjm4d3elcqt5`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 264 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of menu
-- ----------------------------
INSERT INTO `menu` VALUES (1, b'0', '系统管理', NULL, 0, 99, 'system', 'system', b'0', b'0', NULL, '2018-12-18 15:11:29', NULL, 1, '2020-07-16 20:05:34', 0);
INSERT INTO `menu` VALUES (2, b'0', '用户管理', 'system/adminUser/index', 1, 2, 'peoples', 'adminUser', b'0', b'0', 'User', '2018-12-18 15:14:44', 'adminUser:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (3, b'0', '角色管理', 'system/role/index', 1, 3, 'role', 'role', b'0', b'0', 'Role', '2018-12-18 15:16:07', 'roles:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (5, b'0', '菜单管理', 'system/menu/index', 1, 5, 'menu', 'menu', b'0', b'0', 'Menu', '2018-12-18 15:17:28', 'menu:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (6, b'0', '系统监控', NULL, 0, 100, 'monitor', 'monitor', b'0', b'0', NULL, '2018-12-18 15:17:48', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (7, b'0', '操作日志', 'monitor/log/index', 6, 11, 'log', 'logs', b'1', b'0', 'Log', '2018-12-18 15:18:26', 'log:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (9, b'0', 'SQL监控', 'monitor/sql/index', 6, 14, 'sqlMonitor', 'druid', b'0', b'0', 'Sql', '2018-12-18 15:19:34', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (14, b'0', '邮件工具', 'tools/email/index', 36, 24, 'email', 'email', b'0', b'0', 'Email', '2018-12-27 10:13:09', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (18, b'0', '存储管理', 'tools/storage/index', 36, 23, 'qiniu', 'storage', b'0', b'0', 'Storage', '2018-12-31 11:12:15', 'storage:list', 1, '2020-08-06 22:57:28', 0);
INSERT INTO `menu` VALUES (19, b'0', '支付宝工具', 'tools/aliPay/index', 36, 27, 'alipay', 'aliPay', b'0', b'0', 'AliPay', '2018-12-31 14:52:38', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (28, b'0', '定时任务', 'system/timing/index', 36, 21, 'timing', 'timing', b'0', b'0', 'Timing', '2019-01-07 20:34:40', 'timing:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (30, b'0', '代码生成', 'generator/index', 36, 22, 'dev', 'generator', b'0', b'0', 'GeneratorIndex', '2019-01-11 15:45:55', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (32, b'0', '异常日志', 'monitor/log/errorLog', 6, 12, 'error', 'errorLog', b'0', b'0', 'ErrorLog', '2019-01-13 13:49:03', 'logError:list,logError:detail', 1, NULL, 0);
INSERT INTO `menu` VALUES (35, b'0', '部门管理', 'system/dept/index', 1, 6, 'dept', 'dept', b'0', b'0', 'Dept', '2019-03-25 09:46:00', 'dept:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (36, b'0', '系统工具', '', 0, 101, 'sys-tools', 'sys-tools', b'0', b'0', NULL, '2019-03-29 10:57:35', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (37, b'0', '岗位管理', 'system/job/index', 1, 7, 'Steve-Jobs', 'job', b'0', b'0', 'Job', '2019-03-29 13:51:18', 'adminUser:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (39, b'0', '字典管理', 'system/dict/index', 1, 8, 'dictionary', 'dict', b'0', b'0', 'Dict', '2019-04-10 11:49:04', 'dict:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (40, b'0', '商品管理', '', 0, 1, 'shop', 'shop', b'1', b'0', '', '2019-10-03 17:40:19', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (41, b'0', '商品分类', 'shop/cate/index', 40, 11, 'icon', 'cate', b'0', b'0', 'Cate', '2019-10-03 17:42:35', 'YXSTORECATEGORY_ALL,YXSTORECATEGORY_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (45, b'0', '管理商品', 'shop/goods/tab', 40, 12, 'develop', 'goods', b'1', b'0', 'Goods', '2019-10-04 15:34:35', 'YXSTOREPRODUCT_SELECT,YXSTORECATEGORY_ALL,YXSTORECATEGORY_SELECT,YXSYSTEMGROUPDATA_SELECT', 1, '2020-08-06 23:01:36', 0);
INSERT INTO `menu` VALUES (46, b'0', '会员管理', '', 0, 2, 'peoples', 'member', b'0', b'0', '', '2019-10-06 16:18:05', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (47, b'0', '会员', 'shop/adminUser/index', 46, 21, 'peoples', 'member', b'0', b'0', 'Member', '2019-10-06 16:20:17', 'YXUSER_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (48, b'0', '微信管理', '', 0, 14, 'weixin', 'wechat', b'0', b'0', '', '2019-10-06 18:28:54', NULL, 1, '2020-06-26 15:18:32', 0);
INSERT INTO `menu` VALUES (49, b'0', '微信菜单', 'wechat/menu/index', 48, 31, 'menu', 'wemenu', b'0', b'0', 'WeMenu', '2019-10-06 18:31:06', 'YxWechatMenu_ALL,YxWechatMenu_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (50, b'0', '图文管理', 'wechat/article/index', 48, 32, 'article', 'wearticle', b'0', b'0', 'WeArticle', '2019-10-07 17:33:45', 'YXARTICLE_ALL,YXARTICLE_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (51, b'0', '自动回复', 'wechat/reply/index', 48, 33, 'reply', 'wereply', b'0', b'0', 'Wereply', '2019-10-10 09:58:31', 'YXWECHATREPLY_ALL', 1, NULL, 0);
INSERT INTO `menu` VALUES (52, b'0', '公众号配置', 'wechat/config/index', 48, 34, 'configure', 'weconfig', b'0', b'0', 'WeConfig', '2019-10-10 15:52:24', 'YXSYSTEMCONFIG_ALL', 1, NULL, 0);
INSERT INTO `menu` VALUES (53, b'0', '订单管理', '', 0, 4, 'lock', 'order', b'0', b'0', '', '2019-10-14 14:35:18', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (54, b'0', '订单', 'shop/order/index', 53, 41, 'order', 'order', b'0', b'0', 'Order', '2019-10-14 14:36:28', 'YXSTOREORDER_SELECT,YXEXPRESS_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (55, b'0', '商城配置', '', 0, 15, 'configure', 'set', b'0', b'0', '', '2019-10-18 15:21:26', NULL, 1, '2020-06-26 15:18:20', 0);
INSERT INTO `menu` VALUES (60, b'0', '热门搜索', 'shop/set/hot', 55, 55, 'search', 'hot', b'0', b'0', 'Hot', '2019-10-26 18:21:54', 'YXSYSTEMGROUPDATA_ALL,YXSYSTEMGROUPDATA_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (61, b'0', '个人中心菜单', 'shop/set/usermenu', 55, 56, 'menu', 'userMenu', b'0', b'0', 'UserMenu', '2019-10-26 18:42:18', 'YXSYSTEMGROUPDATA_ALL,YXSYSTEMGROUPDATA_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (62, b'0', '评论管理', 'shop/reply/index', 53, 42, 'comment', 'reply', b'0', b'0', 'Reply', '2019-11-03 14:39:09', 'YXSTOREPRODUCTREPLY_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (63, b'0', '营销管理', '', 0, 6, 'yingxiao', 'activity', b'0', b'0', '', '2019-11-09 14:17:42', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (64, b'0', '优惠券制作', 'activity/coupon/index', 229, 61, 'coupon', 'coupon', b'0', b'0', 'Coupon', '2019-11-09 14:18:58', 'YXSTORECOUPON_SELECT', 1, '2020-06-26 15:16:40', 0);
INSERT INTO `menu` VALUES (65, b'0', '已发布优惠券', 'activity/couponissue/index', 229, 62, 'coupon2', 'couponissue', b'0', b'0', 'Couponissue', '2019-11-09 14:20:35', 'YXSTORECOUPONISSUE_SELECT', 1, '2020-06-26 15:16:48', 0);
INSERT INTO `menu` VALUES (66, b'0', '优惠券领取记录', 'activity/storecouponuser/index', 229, 63, 'log', 'couponuser', b'0', b'0', 'Couponuser', '2019-11-09 14:21:35', 'YXSTORECOUPONUSER_SELECT,YXSTORECOUPONUSER_SELECT', 1, '2020-08-06 23:11:10', 0);
INSERT INTO `menu` VALUES (68, b'0', '积分配置', 'wechat/config/point', 227, 59, 'configure', 'pointConfig', b'0', b'0', 'PointConfig', '2019-11-10 18:45:55', 'YXSYSTEMCONFIG_SELECT,YXSYSTEMCONFIG_SELECT', 1, '2020-08-06 23:09:31', 0);
INSERT INTO `menu` VALUES (69, b'0', '分销管理', '', 0, 7, 'fenxiao', 'promoter', b'0', b'0', '', '2019-11-11 10:42:16', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (70, b'0', '分销配置', 'wechat/config/promoter', 69, 71, 'configure', 'promoterconfig', b'0', b'0', 'Promoterconfig', '2019-11-11 10:48:37', 'YXSYSTEMCONFIG_SELECT,YXSYSTEMCONFIG_SELECT', 1, '2020-08-06 23:12:31', 0);
INSERT INTO `menu` VALUES (71, b'0', '分销员', 'shop/adminUser/aindex', 69, 72, 'adminUser', 'agent', b'0', b'0', 'Agent', '2019-11-13 18:32:00', 'YXUSER_SELECT', 1, NULL, 1);
INSERT INTO `menu` VALUES (72, b'0', '提现管理', 'activity/extract/index', 176, 81, 'tixian', 'extract', b'0', b'0', 'Extract', '2019-11-14 10:49:39', 'YXUSEREXTRACT_SELECT,YXUSEREXTRACT_SELECT', 1, '2020-08-06 23:07:28', 0);
INSERT INTO `menu` VALUES (73, b'0', '拼团产品', 'activity/combination/index', 63, 64, 'peoples', 'combination', b'0', b'0', 'Combination', '2019-11-18 14:23:04', 'YXSTORECOMBINATION_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (74, b'0', '拼团列表', 'activity/combinlist/index', 63, 65, 'list', 'pink', b'0', b'0', 'Pink', '2019-11-21 19:35:58', 'YXSTOREPINK_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (75, b'0', '微信支付配置', 'wechat/config/pay', 48, 35, 'wxpay', 'wxpayconfig', b'0', b'0', 'Wxpayconfig', '2019-11-28 17:06:22', 'YXSYSTEMCONFIG_ALL', 1, NULL, 0);
INSERT INTO `menu` VALUES (76, b'0', '小程序配置', 'wechat/config/wxapp', 48, 36, 'configure', 'wxapp', b'0', b'0', 'Wxapp', '2019-11-29 15:13:46', 'YXSYSTEMCONFIG_ALL', 1, NULL, 0);
INSERT INTO `menu` VALUES (77, b'0', '会员等级', 'shop/userlevel/index', 46, 22, 'dengji', 'userlevel', b'0', b'0', 'Userlevel', '2019-12-04 16:35:41', 'YXSYSTEMUSERLEVEL_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (78, b'0', '等级任务', 'shop/usertask/index', 46, 23, 'task manege', 'usertask', b'0', b'0', 'Usertask', '2019-12-04 17:26:19', 'YXSYSTEMUSERTASK_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (79, b'0', '签到天数配置', 'shop/set/sign', 227, 61, 'sign2', 'signday', b'0', b'0', 'Signday', '2019-12-05 14:12:16', 'YXSYSTEMGROUPDATA_ALL,YXSYSTEMGROUPDATA_SELECT', 1, '2020-06-26 15:09:25', 0);
INSERT INTO `menu` VALUES (80, b'0', '用户账单', 'shop/adminUser/bill', 46, 24, 'list', 'bill', b'0', b'0', 'Bill', '2019-12-11 17:28:38', 'YXUSERBILL_ALL,YXUSERBILL_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (81, b'0', '物流快递', 'shop/express/index', 53, 43, 'express', 'express', b'0', b'0', 'Express', '2019-12-12 16:36:00', 'YXEXPRESS_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (82, b'0', '微信模板消息', 'wechat/template/index', 48, 35, 'anq', 'template', b'0', b'0', 'Template', '2019-12-13 14:42:50', 'yxWechatTemplate:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (83, b'0', '秒杀产品', 'activity/seckill/index', 63, 66, 'seckill', 'seckill', b'0', b'0', 'Seckill', '2019-12-16 13:06:29', 'YXSTORESECKILL_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (84, b'0', '秒杀配置', 'shop/set/seckill', 63, 67, 'configure', 'seckillconfig', b'0', b'0', 'Seckillconfig', '2019-12-16 16:07:42', 'YXSYSTEMGROUPDATA_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (86, b'0', '砍价产品', 'activity/bargain/index', 63, 956, 'Sign', 'bargain', b'0', b'0', 'Bargain', '2019-12-22 12:25:55', 'YXSTOREBARGAIN_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (87, b'0', '生成配置', 'generator/config', 36, 33, 'dev', 'generator/config/:tableName', b'1', b'1', 'GeneratorConfig', '2019-11-17 20:08:56', '', 1, NULL, 0);
INSERT INTO `menu` VALUES (88, b'0', '生成预览', 'generator/preview', 36, 999, 'java', 'generator/preview/:tableName', b'1', b'1', 'Preview', '2019-11-26 14:54:36', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (117, b'0', '图表库', 'components/Echarts', 10, 50, 'chart', 'echarts', b'1', b'0', 'Echarts', '2019-11-21 09:04:32', '', 1, NULL, 0);
INSERT INTO `menu` VALUES (118, b'0', '商品新增', 'shop/goods/form', 40, 1, 'anq', 'goodsAdd', b'0', b'1', 'GoodsAdd', '2019-12-24 13:00:47', 'YXSTOREPRODUCT_EDIT', 1, '2020-07-10 16:45:19', 0);
INSERT INTO `menu` VALUES (119, b'0', '商品修改', 'shop/goods/form', 40, 3, 'anq', 'goodsEdit/:id', b'0', b'1', 'GoodsEdit', '2019-12-24 13:02:23', 'YXSTOREPRODUCT_CREATE', 1, '2020-07-10 16:45:33', 0);
INSERT INTO `menu` VALUES (120, b'0', '商品删除', NULL, 45, 4, NULL, NULL, b'0', b'0', NULL, '2019-12-24 13:03:51', 'YXSTOREPRODUCT_DELETE', 2, '2020-07-10 16:22:51', 0);
INSERT INTO `menu` VALUES (121, b'0', '在线用户', 'monitor/online/index', 6, 10, 'Steve-Jobs', 'online', b'0', b'0', 'OnlineUser', '2020-01-06 22:46:43', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (122, b'0', '浏览记录', 'monitor/log/mlog', 40, 13, 'log', 'viewlog', b'0', b'0', 'Viewlog', '2020-01-07 13:17:21', NULL, 1, NULL, 1);
INSERT INTO `menu` VALUES (123, b'0', '后台接口文档', 'tools/swagger/index', 36, 31, 'swagger', 'swagger2', b'0', b'0', 'Swagger', '2020-01-07 18:05:52', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (124, b'0', '在线会员', 'monitor/online/indext', 46, 25, 'Steve-Jobs', 'onlinet', b'0', b'0', 'OnlineMember', '2020-01-13 10:53:07', 'auth_online', 1, '2020-08-06 22:50:49', 0);
INSERT INTO `menu` VALUES (125, b'0', '邮费配置', 'wechat/config/postage', 55, 58, 'configure', 'postageConfig', b'0', b'0', 'PostageConfig', '2020-02-13 15:38:24', 'YXSYSTEMCONFIG_SELECT', 1, NULL, 1);
INSERT INTO `menu` VALUES (126, b'0', '编辑', NULL, 54, 1, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:05:28', 'YXSTOREORDER_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (127, b'0', '用户新增', NULL, 2, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:12:21', 'adminUser:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (128, b'0', '用户编辑', NULL, 2, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:12:47', 'adminUser:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (129, b'0', '用户删除', NULL, 2, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:13:08', 'adminUser:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (130, b'0', '角色创建', NULL, 3, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:13:49', 'roles:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (131, b'0', '角色修改', NULL, 3, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:14:11', 'roles:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (132, b'0', '角色删除', NULL, 3, 999, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:14:38', 'roles:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (133, b'0', '菜单新增', NULL, 5, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:15:05', 'menu:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (134, b'0', '菜单编辑', NULL, 5, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:18:44', 'menu:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (135, b'0', '菜单删除', NULL, 5, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:19:05', 'menu:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (136, b'0', '部门新增', NULL, 35, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:21:07', 'dept:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (137, b'0', '部门编辑', NULL, 35, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:21:33', 'dept:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (138, b'0', '部门删除', NULL, 35, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:21:53', 'dept:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (139, b'0', '岗位新增', NULL, 37, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:29:04', 'job:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (140, b'0', '岗位编辑', NULL, 37, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:48:38', 'job:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (141, b'0', '岗位删除', NULL, 37, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:49:00', 'job:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (142, b'0', '字典新增', NULL, 39, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:49:26', 'dict:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (143, b'0', '字典编辑', NULL, 39, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:49:39', 'dict:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (144, b'0', '字典删除', NULL, 39, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:49:56', 'dict:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (147, b'0', '上传文件', NULL, 18, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:53:49', 'storage:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (148, b'0', '文件编辑', NULL, 18, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:54:06', 'storage:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (149, b'0', '文件删除', NULL, 18, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:54:27', 'storage:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (150, b'0', '任务新增', NULL, 28, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:55:58', 'timing:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (151, b'0', '任务编辑', NULL, 28, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:56:54', 'timing:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (152, b'0', '任务删除', NULL, 28, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 21:57:10', 'timing:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (153, b'0', '新增分类', NULL, 41, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:00:41', 'YXSTORECATEGORY_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (154, b'0', '分类编辑', NULL, 41, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:01:15', 'YXSTORECATEGORY_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (155, b'0', '分类删除', NULL, 41, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:01:37', 'YXSTORECATEGORY_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (156, b'0', '修改会员', NULL, 47, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:03:40', 'YXUSER_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (157, b'0', '等级新增', NULL, 77, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:06:55', 'YXSYSTEMUSERLEVEL_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (158, b'0', '等级编辑', NULL, 77, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:08:03', 'YXSYSTEMUSERLEVEL_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (159, b'0', '等级删除', NULL, 77, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:08:41', 'YXSYSTEMUSERLEVEL_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (160, b'0', '编辑任务', NULL, 78, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:10:08', 'YXSYSTEMUSERTASK_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (161, b'0', '评论删除', NULL, 62, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:14:22', 'YXSTOREPRODUCTREPLY_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (162, b'0', '新增物流', NULL, 81, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:15:33', 'YXEXPRESS_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (163, b'0', '编辑物流', NULL, 81, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:15:53', 'YXEXPRESS_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (164, b'0', '删除物流', NULL, 81, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:16:11', 'YXEXPRESS_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (165, b'0', '新增优惠券', NULL, 64, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:18:32', 'YXSTORECOUPON_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (166, b'0', '编辑优惠券', NULL, 64, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:18:50', 'YXSTORECOUPON_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (167, b'0', '删除优惠券', NULL, 64, 4, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:19:10', 'YXSTORECOUPON_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (168, b'0', '编辑已发布', NULL, 65, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:20:23', 'YXSTORECOUPONISSUE_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (169, b'0', '删除已发布', NULL, 65, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:20:42', 'YXSTORECOUPONISSUE_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (170, b'0', '编辑拼团', NULL, 73, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:24:15', 'YXSTORECOMBINATION_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (171, b'0', '删除拼团', NULL, 73, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:24:37', 'YXSTORECOMBINATION_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (172, b'0', '编辑秒杀', NULL, 83, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:25:23', 'YXSTORESECKILL_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (173, b'0', '删除秒杀', NULL, 83, 3, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:25:41', 'YXSTORESECKILL_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (174, b'0', '编辑砍价', NULL, 86, 2, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:26:20', 'YXSTOREBARGAIN_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (175, b'0', '删除砍价', NULL, 86, 999, NULL, NULL, b'0', b'0', NULL, '2020-02-14 22:26:40', 'YXSTOREBARGAIN_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (176, b'0', '财务管理', NULL, 0, 8, 'price', 'price', b'0', b'0', NULL, '2020-03-02 22:30:23', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (177, b'0', '充值管理', 'shop/recharge/index', 176, 82, 'rec', 'recharge', b'0', b'0', 'Recharge', '2020-03-02 23:05:26', 'yxUserRecharge:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (178, b'0', '门店管理', NULL, 0, 9, 'store', 'store', b'0', b'0', NULL, '2020-03-03 17:27:53', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (179, b'0', '门店列表', 'shop/store/index', 178, 92, 'edit', 'storeinfo', b'0', b'0', 'Storeinfo', '2020-03-03 17:29:09', 'yxSystemStore:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (180, b'0', '门店配置', 'shop/store/set', 178, 91, 'configure', 'storeset', b'0', b'0', 'Storeset', '2020-03-04 13:09:54', 'YXSYSTEMCONFIG_SELECT,YXSYSTEMCONFIG_SELECT', 1, '2020-08-06 23:05:23', 0);
INSERT INTO `menu` VALUES (181, b'0', '核销订单', 'shop/order/indext', 178, 95, 'order', 'ordert', b'0', b'0', 'Ordert', '2020-03-05 17:04:12', 'YXSTOREORDER_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (182, b'0', '充值金额配置', 'shop/set/recharge', 176, 83, 'money', 'rechargeset', b'0', b'0', 'Rechargeset', '2020-03-21 14:24:05', 'YXSYSTEMGROUPDATA_ALL,YXSYSTEMGROUPDATA_SELECT', 1, '2020-06-26 15:11:06', 0);
INSERT INTO `menu` VALUES (183, b'0', '店员列表', 'shop/storestaff/index', 178, 94, 'peoples', 'staff', b'0', b'0', 'Staff', '2020-03-22 14:11:36', 'yxSystemStoreStaff:list', 1, NULL, 0);
INSERT INTO `menu` VALUES (184, b'0', '新增菜单', NULL, 49, 0, 'add', NULL, b'0', b'0', NULL, '2020-06-14 20:10:02', 'YxWechatMenu_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (185, b'0', '模板新增', NULL, 82, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:14:17', 'yxWechatTemplate:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (186, b'0', '模板修改', NULL, 82, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:14:46', 'yxWechatTemplate:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (187, b'0', '模板删除', NULL, 82, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:15:10', 'yxWechatTemplate:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (188, b'0', '新增幻灯片', NULL, 56, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:33:48', 'YXSYSTEMGROUPDATA_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (189, b'0', '修改幻灯片', NULL, 56, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:35:11', 'YXSYSTEMGROUPDATA_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (190, b'0', '删除幻灯片', NULL, 56, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:40:30', 'YXSYSTEMGROUPDATA_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (191, b'0', '新增导航按钮', NULL, 57, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:42:43', 'YXSYSTEMGROUPDATA_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (192, b'0', '修改导航按钮', NULL, 57, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:43:53', 'YXSYSTEMGROUPDATA_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (193, b'0', '删除导航按钮', NULL, 57, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:44:43', 'YXSYSTEMGROUPDATA_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (194, b'0', '新增滚动新闻', NULL, 59, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:48:32', 'YXSYSTEMGROUPDATA_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (195, b'0', '修改滚动新闻', NULL, 59, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:48:52', 'YXSYSTEMGROUPDATA_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (196, b'0', '删除滚动新闻', NULL, 59, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 20:49:32', 'YXSYSTEMGROUPDATA_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (197, b'0', '新增热门搜索', NULL, 60, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:14:25', 'YXSYSTEMGROUPDATA_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (198, b'0', '修改热门搜索', NULL, 60, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:14:55', 'YXSYSTEMGROUPDATA_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (199, b'0', '删除热门搜索', NULL, 60, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:15:25', 'YXSYSTEMGROUPDATA_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (200, b'0', '新增个人中心菜单', NULL, 61, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:17:47', 'YXSYSTEMGROUPDATA_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (201, b'0', '修改个人中心菜单', NULL, 61, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:18:37', 'YXSYSTEMGROUPDATA_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (202, b'0', '删除个人中心菜单', NULL, 61, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:19:47', 'YXSYSTEMGROUPDATA_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (203, b'0', '新增积分配置', NULL, 68, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:20:47', 'YXSYSTEMCONFIG_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (204, b'0', '新增签到天数', NULL, 79, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:26:32', 'YXSYSTEMGROUPDATA_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (205, b'0', '修改签到天数', NULL, 79, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:26:32', 'YXSYSTEMGROUPDATA_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (206, b'0', '删除签到天数', NULL, 79, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:26:52', 'YXSYSTEMGROUPDATA_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (207, b'0', '新增邮费配置', NULL, 125, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:29:20', 'YXSYSTEMCONFIG_CREATE', 2, NULL, 1);
INSERT INTO `menu` VALUES (208, b'0', '新增充值金额', NULL, 182, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:30:59', 'YXSYSTEMGROUPDATA_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (209, b'0', '修改充值金额', NULL, 182, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:30:30', 'YXSYSTEMGROUPDATA_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (210, b'0', '删除充值金额', NULL, 182, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:30:59', 'YXSYSTEMGROUPDATA_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (211, b'0', '新增秒杀配置', NULL, 84, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:43:36', 'YXSYSTEMGROUPDATA_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (212, b'0', '修改秒杀配置', NULL, 84, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:43:56', 'YXSYSTEMGROUPDATA_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (213, b'0', '删除秒杀配置', NULL, 84, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:23:36', 'YXSYSTEMGROUPDATA_DELETE', 2, NULL, 0);
INSERT INTO `menu` VALUES (214, b'0', '新增分销配置', NULL, 70, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:46:46', 'YXSYSTEMCONFIG_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (215, b'0', '提现审核', NULL, 72, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:56:11', 'YXUSEREXTRACT_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (216, b'0', '删除充值', NULL, 177, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:59:11', 'yxUserRecharge:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (217, b'0', '导出充值', NULL, 177, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 21:59:54', 'yxUserRecharge:list', 2, NULL, 0);
INSERT INTO `menu` VALUES (218, b'0', '新增门店', NULL, 179, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:01:57', 'yxSystemStore:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (219, b'0', '修改门店', NULL, 179, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:02:30', 'yxSystemStore:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (220, b'0', '删除门店', NULL, 179, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:02:57', 'yxSystemStore:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (221, b'0', '新增门店配置', NULL, 180, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:04:25', 'YXSYSTEMCONFIG_CREATE', 2, NULL, 0);
INSERT INTO `menu` VALUES (222, b'0', '编辑核销订单', NULL, 181, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:07:26', 'YXSTOREORDER_EDIT', 2, NULL, 0);
INSERT INTO `menu` VALUES (223, b'0', '新增店员', NULL, 183, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:11:13', 'yxSystemStoreStaff:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (224, b'0', '修改店员', NULL, 183, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:11:37', 'yxSystemStoreStaff:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (225, b'0', '删除店员', NULL, 183, 3, NULL, NULL, b'0', b'0', NULL, '2020-06-14 22:11:59', 'yxSystemStoreStaff:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (226, b'0', '分销记录', 'shop/adminUser/brobill', 69, 72, 'log', 'prolog', b'0', b'0', 'Prolog', '2020-06-26 14:52:30', 'YXUSERBILL_ALL,YXUSERBILL_SELECT,YXUSERBILL_SELECT', 1, '2020-08-06 23:12:57', 0);
INSERT INTO `menu` VALUES (227, b'0', '积分管理', NULL, 0, 10, 'Sign', 'syspoints', b'0', b'0', '-', '2020-06-26 15:00:59', NULL, 1, '2020-06-26 15:06:11', 0);
INSERT INTO `menu` VALUES (228, b'0', '积分记录', 'shop/adminUser/pobill', 227, 60, 'log', 'pobill', b'0', b'0', 'Pobill', '2020-06-26 15:04:17', 'YXUSERBILL_ALL,YXUSERBILL_SELECT,YXUSERBILL_SELECT', 1, '2020-08-06 23:09:57', 0);
INSERT INTO `menu` VALUES (229, b'0', '电子券管理', NULL, 0, 5, 'coupon', 'syscoupon', b'0', b'0', '', '2020-06-26 15:15:47', NULL, 1, '2020-06-26 15:22:16', 0);
INSERT INTO `menu` VALUES (230, b'0', '商城系统配置', 'wechat/config/shop', 55, 59, 'shop', 'shopconfig', b'0', b'0', 'Shopconfig', '2020-06-26 15:45:32', 'YXSYSTEMCONFIG_SELECT', 1, '2020-07-04 18:50:34', 0);
INSERT INTO `menu` VALUES (231, b'0', '阿里云短信', 'wechat/config/sms', 55, 60, 'sys-tools', 'smsconfig', b'0', b'0', 'Smsconfig', '2020-06-26 16:06:08', 'YXSYSTEMCONFIG_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (232, b'0', '快递鸟配置', 'wechat/config/express', 55, 61, 'news', 'expressconfig', b'0', b'0', 'Expressconfig', '2020-06-26 16:08:10', 'YXSYSTEMCONFIG_SELECT', 1, NULL, 0);
INSERT INTO `menu` VALUES (233, b'0', '商品规格', 'shop/storeProductRule/index', 40, 13, 'mnt', 'productRule', b'0', b'0', 'ProductRule', '2020-06-28 16:35:00', 'productRule:list', 1, '2020-07-10 16:21:48', 0);
INSERT INTO `menu` VALUES (234, b'0', '运费模板', 'shop/shippingTemplates/index', 40, 14, 'exit-fullscreen', 'shippingTemplates', b'0', b'0', 'ShippingTemplates', '2020-06-29 17:16:06', 'yxShippingTemplates:list', 1, '2020-07-10 16:57:13', 0);
INSERT INTO `menu` VALUES (235, b'0', '图文添加', 'wechat/article/form', 48, 36, 'article', 'artadd', b'0', b'1', 'Artadd', '2020-07-01 18:50:35', NULL, 1, '2020-07-02 11:24:45', 0);
INSERT INTO `menu` VALUES (236, b'0', '图文编辑', 'wechat/article/form', 48, 37, 'alipay', 'artadd/:id', b'0', b'1', 'Editadd', '2020-07-02 11:24:39', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (237, b'0', '规格新增、修改', NULL, 233, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-28 16:35:00', 'productRule:add,productRule:edit', 2, '2020-07-16 20:09:03', 0);
INSERT INTO `menu` VALUES (238, b'0', '规格删除', NULL, 233, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-28 16:35:00', 'productRule:del', 2, '2020-07-16 20:08:57', 0);
INSERT INTO `menu` VALUES (240, b'0', '新增、修改模板', NULL, 234, 1, NULL, NULL, b'0', b'0', NULL, '2020-06-29 17:16:06', 'yxShippingTemplates:add', 2, '2020-07-16 20:09:11', 0);
INSERT INTO `menu` VALUES (241, b'0', '删除模板', NULL, 234, 2, NULL, NULL, b'0', b'0', NULL, '2020-06-29 17:16:06', 'yxShippingTemplates:del', 2, '2020-07-16 20:09:16', 0);
INSERT INTO `menu` VALUES (242, b'0', '直播管理', 'wechat/live/index', 48, 999, 'weixin', 'wxlive', b'0', b'0', 'Wxlive', '2020-08-10 17:20:54', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (243, b'0', '直播商品管理', 'wechat/goods/index', 48, 999, 'weixin', 'wxlivegoods', b'0', b'0', 'WxliveGoods', '2020-08-10 17:20:54', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (244, b'0', '拼团商品添加', 'activity/combination/form', 63, 999, NULL, 'combinationAdd', b'0', b'1', 'CombinationAdd', '2020-08-13 21:28:45', 'YXSTORECOMBINATION_EDIT', 1, '2020-08-13 21:31:26', 0);
INSERT INTO `menu` VALUES (245, b'0', '拼团商品修改', 'activity/combination/form', 63, 3, 'anq', 'combinationEdit/:id', b'0', b'1', 'CombinationEdit', '2019-12-24 13:02:23', 'YXSTORECOMBINATION_EDIT', 1, '2020-07-10 16:45:33', 0);
INSERT INTO `menu` VALUES (246, b'0', '秒杀商品添加', 'activity/seckill/form', 63, 999, NULL, 'secKillAdd', b'0', b'1', 'SecKillAdd', '2020-08-13 21:28:45', 'YXSTORESECKILL_EDIT', 1, '2020-08-13 21:31:26', 0);
INSERT INTO `menu` VALUES (247, b'0', '秒杀商品修改', 'activity/seckill/form', 63, 3, 'anq', 'secKillEdit/:id', b'0', b'1', 'SecKillEdit', '2019-12-24 13:02:23', 'YXSTORESECKILL_EDIT', 1, '2020-07-10 16:45:33', 0);
INSERT INTO `menu` VALUES (248, b'0', '多级菜单', NULL, 0, 999, 'menu', 'nested', b'0', b'0', '-', '2020-08-19 11:31:10', NULL, 0, NULL, 0);
INSERT INTO `menu` VALUES (249, b'0', '二级菜单1', 'nested/menu1/index', 248, 999, 'menu', 'menu1', b'0', b'0', '-', '2020-08-19 11:34:34', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (250, b'0', '三级菜单1', 'nested/menu1/menu1-1', 249, 999, 'menu', 'menu1-1', b'0', b'0', '-', '2020-08-19 11:35:52', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (251, b'0', '三级菜单2', 'nested/menu1/menu1-2', 249, 999, 'menu', 'menu1-2', b'0', b'0', '-', '2020-08-19 11:37:48', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (252, b'0', '二级菜单2', 'nested/menu2/index', 248, 999, 'menu', 'menu2', b'0', b'0', '-', '2020-08-19 11:38:35', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (253, b'0', '浏览记录', 'monitor/log/mlog', 40, 15, 'log', 'viewlog', b'0', b'0', 'Viewlog', '2020-07-31 09:47:11', 'log:list', 1, '2020-07-31 09:49:39', 0);
INSERT INTO `menu` VALUES (256, b'0', '商品收藏', 'shop/collect/index', 40, 16, 'menu', 'productRelation', b'0', b'0', 'ProductRelation', '2020-09-03 14:32:49', 'yxStoreProductRelation:list', 1, '2020-09-03 16:21:08', 0);
INSERT INTO `menu` VALUES (257, b'0', '用户足迹', 'shop/foot/index', 40, 17, 'list', 'footRelation', b'0', b'0', 'FootRelation', '2020-09-03 16:20:21', 'yxStoreProductRelation:list', 1, '2020-09-03 16:21:16', 0);
INSERT INTO `menu` VALUES (258, b'0', '订单详情', 'shop/order/detail', 53, 999, 'sqlMonitor', 'detail/:id', b'0', b'1', 'Detail', '2020-09-10 07:29:34', NULL, 1, '2020-09-10 08:52:09', 0);
INSERT INTO `menu` VALUES (259, b'0', 'App版本', 'shop/appVersion/index', 55, 999, 'app', 'appVersion', b'0', b'0', '', '2020-12-09 11:04:33', 'yxAppVersion:list', 1, '2020-12-09 11:07:13', 0);
INSERT INTO `menu` VALUES (260, b'0', '订单消息管理', 'shop/customer/index', 55, 4, 'peoples', 'storeCustomer', b'0', b'0', 'msgInfo', '2020-04-02 14:16:43', 'yxStoreCustomer:list', 1, '2020-12-10 11:44:49', 0);
INSERT INTO `menu` VALUES (261, b'0', '添加人员', NULL, 260, 1, NULL, NULL, b'0', b'0', NULL, '2020-04-02 14:18:30', 'yxStoreCustomer:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (262, b'0', '编辑人员', NULL, 260, 2, NULL, NULL, b'0', b'0', NULL, '2020-04-02 14:18:47', 'yxStoreCustomer:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (263, b'0', '删除人员', NULL, 260, 3, NULL, NULL, b'0', b'0', NULL, '2020-04-02 14:19:07', 'yxStoreCustomer:del', 2, NULL, 0);
INSERT INTO `menu` VALUES (264, b'0', '终端装修', NULL, 0, 0, 'theme', 'theme', b'0', b'0', '--', '2021-02-25 19:33:17', '', 1, '2021-02-25 19:33:32', 0);
INSERT INTO `menu` VALUES (265, b'0', '商城装修', 'theme/container/index', 264, 999, 'theme', 'container', b'0', b'0', 'Container', '2021-02-25 19:35:13', NULL, 1, NULL, 0);
INSERT INTO `menu` VALUES (266, b'0', '售后', 'shop/afterSeals/index', 53, 44, 'order', 'afterSeals', b'0', b'0', 'AfterSeals', '2021-06-30 15:23:38', 'yxStoreAfterSales:list', 1, '2021-06-30 15:33:14', 0);
INSERT INTO `menu` VALUES (267, b'0', '新增', NULL, 266, 999, NULL, NULL, b'0', b'0', '-', '2021-06-30 15:34:17', 'yxStoreAfterSales:add', 2, NULL, 0);
INSERT INTO `menu` VALUES (268, b'0', '修改', NULL, 266, 999, NULL, NULL, b'0', b'0', '-', '2021-06-30 15:34:39', 'yxStoreAfterSales:edit', 2, NULL, 0);
INSERT INTO `menu` VALUES (269, b'0', '删除', NULL, 266, 999, NULL, NULL, b'0', b'0', '-', '2021-06-30 15:34:55', 'yxStoreAfterSales:del', 2, NULL, 0);
-- ----------------------------
-- Table structure for monitor_server
-- ----------------------------
DROP TABLE IF EXISTS `monitor_server`;
CREATE TABLE `monitor_server`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `cpu_core` int(11) NULL DEFAULT NULL COMMENT 'CPU内核数',
  `cpu_rate` double NULL DEFAULT NULL COMMENT 'CPU使用率',
  `disk_total` double NULL DEFAULT NULL COMMENT '磁盘总量',
  `disk_used` double NULL DEFAULT NULL COMMENT '磁盘使用量',
  `mem_total` double NULL DEFAULT NULL COMMENT '内存总数',
  `mem_used` double NULL DEFAULT NULL COMMENT '内存使用量',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `port` int(11) NULL DEFAULT NULL COMMENT '访问端口',
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  `state` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '状态',
  `swap_total` double NULL DEFAULT NULL COMMENT '交换区总量',
  `swap_used` double NULL DEFAULT NULL COMMENT '交换区使用量',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '服务地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '服务监控' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of monitor_server
-- ----------------------------
INSERT INTO `monitor_server` VALUES (1, 8, 0.05924018, 465.12402, 91.66521, 7.849415, 7.6052284, '本地', 8777, 999, '0', 14.599415, 11.263367, 'localhost');

-- ----------------------------
-- Table structure for picture
-- ----------------------------
DROP TABLE IF EXISTS `picture`;
CREATE TABLE `picture`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '上传日期',
  `delete_url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '删除的URL',
  `filename` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片名称',
  `height` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片高度',
  `size` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片大小',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片地址',
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名称',
  `width` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片宽度',
  `md5code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件的MD5值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'Sm.Ms图床' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of picture
-- ----------------------------

-- ----------------------------
-- Table structure for qiniu_config
-- ----------------------------
DROP TABLE IF EXISTS `qiniu_config`;
CREATE TABLE `qiniu_config`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `access_key` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'accessKey',
  `bucket` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Bucket 识别符',
  `host` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '外链域名',
  `secret_key` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT 'secretKey',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '空间类型',
  `zone` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '机房',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '七牛云配置' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of qiniu_config
-- ----------------------------
INSERT INTO `qiniu_config` VALUES (1, 'OJou_rI3QATSsY0MMqkpq0FPvAXpEjs1o4KHuvCx', 'yixiang', 'https://image.dayouqiantu.cn', 'XwNNqLeAnXP754LhkJYmBM6nu1djcc1LUXTDYylY', '公开', '华东');

-- ----------------------------
-- Table structure for qiniu_content
-- ----------------------------
DROP TABLE IF EXISTS `qiniu_content`;
CREATE TABLE `qiniu_content`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `bucket` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Bucket 识别符',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件名称',
  `size` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件大小',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件类型：私有或公开',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '上传或同步的时间',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文件url',
  `suffix` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '七牛云文件存储' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of qiniu_content
-- ----------------------------
INSERT INTO `qiniu_content` VALUES (1, 'yixiang', 'list_34', '27.05KB   ', '公开', '2020-01-07 12:05:41', 'https://pic.dayouqiantu.cn/list_34.png', 'png');
INSERT INTO `qiniu_content` VALUES (2, 'yixiang', 'list_32', '27.65KB   ', '公开', '2020-01-07 12:11:15', 'https://pic.dayouqiantu.cn/list_32.png', 'png');
INSERT INTO `qiniu_content` VALUES (3, 'yixiang', 'list_30', '28.57KB   ', '公开', '2020-01-07 12:13:26', 'https://image.dayouqiantu.cn/list_30.png', 'png');
INSERT INTO `qiniu_content` VALUES (4, 'yixiang', 'list_34', '27.05KB   ', '公开', '2020-01-07 12:32:15', 'https://image.dayouqiantu.cn/list_34.png', 'png');
INSERT INTO `qiniu_content` VALUES (5, 'yixiang', 'list_32', '27.65KB   ', '公开', '2020-01-07 12:53:11', 'https://image.dayouqiantu.cn/list_32.png', 'png');
INSERT INTO `qiniu_content` VALUES (6, 'yixiang', 'list_34', '27.05KB   ', '公开', '2020-01-07 13:05:55', 'https://image.dayouqiantu.cn/list_34.png', 'png');
INSERT INTO `qiniu_content` VALUES (7, 'yixiang', 'list_32', '27.65KB   ', '公开', '2020-01-07 13:08:32', 'https://image.dayouqiantu.cn/list_32.png', 'png');
INSERT INTO `qiniu_content` VALUES (8, 'yixiang', 'list_22', '28.23KB   ', '公开', '2020-01-07 13:08:38', 'https://image.dayouqiantu.cn/list_22.png', 'png');
INSERT INTO `qiniu_content` VALUES (9, 'yixiang', 'list_32', '27.65KB   ', '公开', '2020-01-07 13:08:49', 'https://image.dayouqiantu.cn/list_32.png', 'png');
INSERT INTO `qiniu_content` VALUES (10, 'yixiang', 'list_34', '27.05KB   ', '公开', '2020-01-10 12:02:24', 'https://image.dayouqiantu.cn/list_34.png', 'png');
INSERT INTO `qiniu_content` VALUES (11, 'yixiang', 'list_32', '27.65KB   ', '公开', '2020-01-10 16:49:48', 'https://image.dayouqiantu.cn/list_32.png', 'png');
INSERT INTO `qiniu_content` VALUES (12, 'yixiang', 'list_20', '22.92KB   ', '公开', '2020-01-10 22:43:47', 'https://image.dayouqiantu.cn/list_20.png', 'png');
INSERT INTO `qiniu_content` VALUES (13, 'yixiang', 'list_24', '27.31KB   ', '公开', '2020-01-10 22:44:21', 'https://image.dayouqiantu.cn/list_24.png', 'png');
INSERT INTO `qiniu_content` VALUES (14, 'yixiang', 'list_28', '28.38KB   ', '公开', '2020-01-11 17:06:00', 'https://image.dayouqiantu.cn/list_28.png', 'png');
INSERT INTO `qiniu_content` VALUES (15, 'yixiang', 'goods', '2.82KB   ', '公开', '2020-06-26 18:20:58', 'https://image.dayouqiantu.cn/goods.png', 'png');
INSERT INTO `qiniu_content` VALUES (16, 'yixiang', 'coupon', '2.92KB   ', '公开', '2020-06-26 18:20:58', 'https://image.dayouqiantu.cn/coupon.png', 'png');
INSERT INTO `qiniu_content` VALUES (17, 'yixiang', 'buy', '2.59KB   ', '公开', '2020-06-26 18:20:58', 'https://image.dayouqiantu.cn/buy.png', 'png');
INSERT INTO `qiniu_content` VALUES (18, 'yixiang', 'collect', '3.65KB   ', '公开', '2020-06-26 18:20:58', 'https://image.dayouqiantu.cn/collect.png', 'png');
INSERT INTO `qiniu_content` VALUES (19, 'yixiang', 'bargin', '3.39KB   ', '公开', '2020-06-26 18:24:51', 'https://image.dayouqiantu.cn/bargin.png', 'png');
INSERT INTO `qiniu_content` VALUES (20, 'yixiang', 'add', '3.17KB   ', '公开', '2020-06-26 18:24:51', 'https://image.dayouqiantu.cn/add.png', 'png');
INSERT INTO `qiniu_content` VALUES (21, 'yixiang', 'sign', '2.63KB   ', '公开', '2020-06-26 18:25:07', 'https://image.dayouqiantu.cn/sign.png', 'png');
INSERT INTO `qiniu_content` VALUES (22, 'yixiang', 'sekill', '3.54KB   ', '公开', '2020-06-26 18:25:07', 'https://image.dayouqiantu.cn/sekill.png', 'png');
INSERT INTO `qiniu_content` VALUES (23, 'yixiang', 'pink', '3.25KB   ', '公开', '2020-06-26 18:25:07', 'https://image.dayouqiantu.cn/pink.png', 'png');
INSERT INTO `qiniu_content` VALUES (24, 'yixiang', 'news', '2.70KB   ', '公开', '2020-06-26 18:25:07', 'https://image.dayouqiantu.cn/news.png', 'png');
INSERT INTO `qiniu_content` VALUES (25, 'yixiang', '5ca04fa9c08ef', '314.06KB   ', '公开', '2020-06-26 18:25:23', 'https://image.dayouqiantu.cn/5ca04fa9c08ef.jpg', 'jpg');
INSERT INTO `qiniu_content` VALUES (26, 'yixiang', '5ca0786c5d2c1', '388.24KB   ', '公开', '2020-06-26 18:25:23', 'https://image.dayouqiantu.cn/5ca0786c5d2c1.jpg', 'jpg');
INSERT INTO `qiniu_content` VALUES (27, 'yixiang', '5ca081af6183f', '339.37KB   ', '公开', '2020-06-26 18:25:23', 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', 'jpg');

-- ----------------------------
-- Table structure for quartz_job
-- ----------------------------
DROP TABLE IF EXISTS `quartz_job`;
CREATE TABLE `quartz_job`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `bean_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Spring Bean名称',
  `cron_expression` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'cron 表达式',
  `is_pause` bit(1) NULL DEFAULT NULL COMMENT '状态：1暂停、0启用',
  `job_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '任务名称',
  `method_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '方法名称',
  `params` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '参数',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of quartz_job
-- ----------------------------
INSERT INTO `quartz_job` VALUES (1, 'visitsTask', '0 0 0 * * ?', b'0', '更新访客记录', 'run', NULL, '每日0点创建新的访客记录', '2019-01-08 14:53:31', NULL, 0);
INSERT INTO `quartz_job` VALUES (2, 'testTask', '0/5 * * * * ?', b'1', '测试1', 'run1', 'test', '带参测试，多参使用json', '2019-08-22 14:08:29', NULL, 0);
INSERT INTO `quartz_job` VALUES (3, 'testTask', '0/5 * * * * ?', b'1', '测试43', 'run', '44', '不带参测试', '2019-09-26 16:44:39', NULL, 0);

-- ----------------------------
-- Table structure for quartz_log
-- ----------------------------
DROP TABLE IF EXISTS `quartz_log`;
CREATE TABLE `quartz_log`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `baen_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `cron_expression` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `exception_detail` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `is_success` bit(1) NULL DEFAULT NULL,
  `job_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `method_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `params` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `time` bigint(20) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '定时任务日志' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of quartz_log
-- ----------------------------
INSERT INTO `quartz_log` VALUES (1, 'visitsTask', '2020-01-10 00:00:00', '0 0 0 * * ?', NULL, b'1', '更新访客记录', 'run', NULL, 160, NULL, 0);
INSERT INTO `quartz_log` VALUES (2, 'visitsTask', '2020-03-10 00:00:00', '0 0 0 * * ?', NULL, b'1', '更新访客记录', 'run', NULL, 42, NULL, 0);

-- ----------------------------
-- Table structure for role
-- ----------------------------
DROP TABLE IF EXISTS `role`;
CREATE TABLE `role`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '名称',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注',
  `data_scope` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '数据权限',
  `level` int(255) NULL DEFAULT NULL COMMENT '角色级别',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `permission` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '功能权限',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色表' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of role
-- ----------------------------
INSERT INTO `role` VALUES (1, '超级管理员', '-', '全部', 1, '2018-11-23 11:04:37', 'admin', NULL, 0);
INSERT INTO `role` VALUES (2, '普通用户', '-', '本级', 2, '2018-11-23 13:09:06', 'common', NULL, 0);
INSERT INTO `role` VALUES (3, '管理员2', '222', '全部', 3, '2020-01-31 16:53:25', '22', NULL, 0);

-- ----------------------------
-- Table structure for roles_depts
-- ----------------------------
DROP TABLE IF EXISTS `roles_depts`;
CREATE TABLE `roles_depts`  (
  `role_id` bigint(20) NOT NULL,
  `dept_id` bigint(20) NOT NULL,
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE,
  INDEX `FK7qg6itn5ajdoa9h9o78v9ksur`(`dept_id`) USING BTREE,
  CONSTRAINT `FK7qg6itn5ajdoa9h9o78v9ksur` FOREIGN KEY (`dept_id`) REFERENCES `dept` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKrg1ci4cxxfbja0sb0pddju7k` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色部门关联' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of roles_depts
-- ----------------------------

-- ----------------------------
-- Table structure for roles_menus
-- ----------------------------
DROP TABLE IF EXISTS `roles_menus`;
CREATE TABLE `roles_menus`  (
  `menu_id` bigint(20) NOT NULL COMMENT '菜单ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`menu_id`, `role_id`) USING BTREE,
  INDEX `FKcngg2qadojhi3a651a5adkvbq`(`role_id`) USING BTREE,
  CONSTRAINT `FKo7wsmlrrxb2osfaoavp46rv2r` FOREIGN KEY (`menu_id`) REFERENCES `menu` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKtag324maketmxffly3pdyh193` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '角色菜单关联' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of roles_menus
-- ----------------------------
INSERT INTO `roles_menus` VALUES (1, 1);
INSERT INTO `roles_menus` VALUES (2, 1);
INSERT INTO `roles_menus` VALUES (3, 1);
INSERT INTO `roles_menus` VALUES (5, 1);
INSERT INTO `roles_menus` VALUES (6, 1);
INSERT INTO `roles_menus` VALUES (7, 1);
INSERT INTO `roles_menus` VALUES (9, 1);
INSERT INTO `roles_menus` VALUES (14, 1);
INSERT INTO `roles_menus` VALUES (18, 1);
INSERT INTO `roles_menus` VALUES (19, 1);
INSERT INTO `roles_menus` VALUES (28, 1);
INSERT INTO `roles_menus` VALUES (30, 1);
INSERT INTO `roles_menus` VALUES (32, 1);
INSERT INTO `roles_menus` VALUES (35, 1);
INSERT INTO `roles_menus` VALUES (36, 1);
INSERT INTO `roles_menus` VALUES (37, 1);
INSERT INTO `roles_menus` VALUES (39, 1);
INSERT INTO `roles_menus` VALUES (40, 1);
INSERT INTO `roles_menus` VALUES (41, 1);
INSERT INTO `roles_menus` VALUES (45, 1);
INSERT INTO `roles_menus` VALUES (46, 1);
INSERT INTO `roles_menus` VALUES (47, 1);
INSERT INTO `roles_menus` VALUES (48, 1);
INSERT INTO `roles_menus` VALUES (49, 1);
INSERT INTO `roles_menus` VALUES (50, 1);
INSERT INTO `roles_menus` VALUES (51, 1);
INSERT INTO `roles_menus` VALUES (52, 1);
INSERT INTO `roles_menus` VALUES (53, 1);
INSERT INTO `roles_menus` VALUES (54, 1);
INSERT INTO `roles_menus` VALUES (55, 1);
INSERT INTO `roles_menus` VALUES (56, 1);
INSERT INTO `roles_menus` VALUES (57, 1);
INSERT INTO `roles_menus` VALUES (59, 1);
INSERT INTO `roles_menus` VALUES (60, 1);
INSERT INTO `roles_menus` VALUES (61, 1);
INSERT INTO `roles_menus` VALUES (62, 1);
INSERT INTO `roles_menus` VALUES (63, 1);
INSERT INTO `roles_menus` VALUES (64, 1);
INSERT INTO `roles_menus` VALUES (65, 1);
INSERT INTO `roles_menus` VALUES (66, 1);
INSERT INTO `roles_menus` VALUES (68, 1);
INSERT INTO `roles_menus` VALUES (69, 1);
INSERT INTO `roles_menus` VALUES (70, 1);
INSERT INTO `roles_menus` VALUES (72, 1);
INSERT INTO `roles_menus` VALUES (73, 1);
INSERT INTO `roles_menus` VALUES (74, 1);
INSERT INTO `roles_menus` VALUES (75, 1);
INSERT INTO `roles_menus` VALUES (76, 1);
INSERT INTO `roles_menus` VALUES (77, 1);
INSERT INTO `roles_menus` VALUES (78, 1);
INSERT INTO `roles_menus` VALUES (79, 1);
INSERT INTO `roles_menus` VALUES (80, 1);
INSERT INTO `roles_menus` VALUES (81, 1);
INSERT INTO `roles_menus` VALUES (82, 1);
INSERT INTO `roles_menus` VALUES (83, 1);
INSERT INTO `roles_menus` VALUES (84, 1);
INSERT INTO `roles_menus` VALUES (86, 1);
INSERT INTO `roles_menus` VALUES (87, 1);
INSERT INTO `roles_menus` VALUES (88, 1);
INSERT INTO `roles_menus` VALUES (118, 1);
INSERT INTO `roles_menus` VALUES (119, 1);
INSERT INTO `roles_menus` VALUES (120, 1);
INSERT INTO `roles_menus` VALUES (121, 1);
INSERT INTO `roles_menus` VALUES (123, 1);
INSERT INTO `roles_menus` VALUES (124, 1);
INSERT INTO `roles_menus` VALUES (126, 1);
INSERT INTO `roles_menus` VALUES (127, 1);
INSERT INTO `roles_menus` VALUES (128, 1);
INSERT INTO `roles_menus` VALUES (129, 1);
INSERT INTO `roles_menus` VALUES (130, 1);
INSERT INTO `roles_menus` VALUES (131, 1);
INSERT INTO `roles_menus` VALUES (132, 1);
INSERT INTO `roles_menus` VALUES (133, 1);
INSERT INTO `roles_menus` VALUES (134, 1);
INSERT INTO `roles_menus` VALUES (135, 1);
INSERT INTO `roles_menus` VALUES (136, 1);
INSERT INTO `roles_menus` VALUES (137, 1);
INSERT INTO `roles_menus` VALUES (138, 1);
INSERT INTO `roles_menus` VALUES (139, 1);
INSERT INTO `roles_menus` VALUES (140, 1);
INSERT INTO `roles_menus` VALUES (141, 1);
INSERT INTO `roles_menus` VALUES (142, 1);
INSERT INTO `roles_menus` VALUES (143, 1);
INSERT INTO `roles_menus` VALUES (144, 1);
INSERT INTO `roles_menus` VALUES (147, 1);
INSERT INTO `roles_menus` VALUES (148, 1);
INSERT INTO `roles_menus` VALUES (149, 1);
INSERT INTO `roles_menus` VALUES (150, 1);
INSERT INTO `roles_menus` VALUES (151, 1);
INSERT INTO `roles_menus` VALUES (152, 1);
INSERT INTO `roles_menus` VALUES (153, 1);
INSERT INTO `roles_menus` VALUES (154, 1);
INSERT INTO `roles_menus` VALUES (155, 1);
INSERT INTO `roles_menus` VALUES (156, 1);
INSERT INTO `roles_menus` VALUES (157, 1);
INSERT INTO `roles_menus` VALUES (158, 1);
INSERT INTO `roles_menus` VALUES (159, 1);
INSERT INTO `roles_menus` VALUES (160, 1);
INSERT INTO `roles_menus` VALUES (161, 1);
INSERT INTO `roles_menus` VALUES (162, 1);
INSERT INTO `roles_menus` VALUES (163, 1);
INSERT INTO `roles_menus` VALUES (164, 1);
INSERT INTO `roles_menus` VALUES (165, 1);
INSERT INTO `roles_menus` VALUES (166, 1);
INSERT INTO `roles_menus` VALUES (167, 1);
INSERT INTO `roles_menus` VALUES (168, 1);
INSERT INTO `roles_menus` VALUES (169, 1);
INSERT INTO `roles_menus` VALUES (170, 1);
INSERT INTO `roles_menus` VALUES (171, 1);
INSERT INTO `roles_menus` VALUES (172, 1);
INSERT INTO `roles_menus` VALUES (173, 1);
INSERT INTO `roles_menus` VALUES (174, 1);
INSERT INTO `roles_menus` VALUES (175, 1);
INSERT INTO `roles_menus` VALUES (176, 1);
INSERT INTO `roles_menus` VALUES (177, 1);
INSERT INTO `roles_menus` VALUES (178, 1);
INSERT INTO `roles_menus` VALUES (179, 1);
INSERT INTO `roles_menus` VALUES (180, 1);
INSERT INTO `roles_menus` VALUES (181, 1);
INSERT INTO `roles_menus` VALUES (182, 1);
INSERT INTO `roles_menus` VALUES (183, 1);
INSERT INTO `roles_menus` VALUES (184, 1);
INSERT INTO `roles_menus` VALUES (185, 1);
INSERT INTO `roles_menus` VALUES (186, 1);
INSERT INTO `roles_menus` VALUES (187, 1);
INSERT INTO `roles_menus` VALUES (188, 1);
INSERT INTO `roles_menus` VALUES (189, 1);
INSERT INTO `roles_menus` VALUES (190, 1);
INSERT INTO `roles_menus` VALUES (191, 1);
INSERT INTO `roles_menus` VALUES (192, 1);
INSERT INTO `roles_menus` VALUES (193, 1);
INSERT INTO `roles_menus` VALUES (194, 1);
INSERT INTO `roles_menus` VALUES (195, 1);
INSERT INTO `roles_menus` VALUES (196, 1);
INSERT INTO `roles_menus` VALUES (197, 1);
INSERT INTO `roles_menus` VALUES (198, 1);
INSERT INTO `roles_menus` VALUES (199, 1);
INSERT INTO `roles_menus` VALUES (200, 1);
INSERT INTO `roles_menus` VALUES (201, 1);
INSERT INTO `roles_menus` VALUES (202, 1);
INSERT INTO `roles_menus` VALUES (203, 1);
INSERT INTO `roles_menus` VALUES (204, 1);
INSERT INTO `roles_menus` VALUES (205, 1);
INSERT INTO `roles_menus` VALUES (206, 1);
INSERT INTO `roles_menus` VALUES (208, 1);
INSERT INTO `roles_menus` VALUES (209, 1);
INSERT INTO `roles_menus` VALUES (210, 1);
INSERT INTO `roles_menus` VALUES (211, 1);
INSERT INTO `roles_menus` VALUES (212, 1);
INSERT INTO `roles_menus` VALUES (213, 1);
INSERT INTO `roles_menus` VALUES (214, 1);
INSERT INTO `roles_menus` VALUES (215, 1);
INSERT INTO `roles_menus` VALUES (216, 1);
INSERT INTO `roles_menus` VALUES (217, 1);
INSERT INTO `roles_menus` VALUES (218, 1);
INSERT INTO `roles_menus` VALUES (219, 1);
INSERT INTO `roles_menus` VALUES (220, 1);
INSERT INTO `roles_menus` VALUES (221, 1);
INSERT INTO `roles_menus` VALUES (222, 1);
INSERT INTO `roles_menus` VALUES (223, 1);
INSERT INTO `roles_menus` VALUES (224, 1);
INSERT INTO `roles_menus` VALUES (225, 1);
INSERT INTO `roles_menus` VALUES (226, 1);
INSERT INTO `roles_menus` VALUES (227, 1);
INSERT INTO `roles_menus` VALUES (228, 1);
INSERT INTO `roles_menus` VALUES (229, 1);
INSERT INTO `roles_menus` VALUES (230, 1);
INSERT INTO `roles_menus` VALUES (231, 1);
INSERT INTO `roles_menus` VALUES (232, 1);
INSERT INTO `roles_menus` VALUES (233, 1);
INSERT INTO `roles_menus` VALUES (234, 1);
INSERT INTO `roles_menus` VALUES (235, 1);
INSERT INTO `roles_menus` VALUES (236, 1);
INSERT INTO `roles_menus` VALUES (237, 1);
INSERT INTO `roles_menus` VALUES (238, 1);
INSERT INTO `roles_menus` VALUES (240, 1);
INSERT INTO `roles_menus` VALUES (241, 1);
INSERT INTO `roles_menus` VALUES (242, 1);
INSERT INTO `roles_menus` VALUES (243, 1);
INSERT INTO `roles_menus` VALUES (244, 1);
INSERT INTO `roles_menus` VALUES (245, 1);
INSERT INTO `roles_menus` VALUES (246, 1);
INSERT INTO `roles_menus` VALUES (247, 1);
INSERT INTO `roles_menus` VALUES (248, 1);
INSERT INTO `roles_menus` VALUES (249, 1);
INSERT INTO `roles_menus` VALUES (250, 1);
INSERT INTO `roles_menus` VALUES (251, 1);
INSERT INTO `roles_menus` VALUES (252, 1);
INSERT INTO `roles_menus` VALUES (253, 1);
INSERT INTO `roles_menus` VALUES (256, 1);
INSERT INTO `roles_menus` VALUES (257, 1);
INSERT INTO `roles_menus` VALUES (258, 1);
INSERT INTO `roles_menus` VALUES (259, 1);
INSERT INTO `roles_menus` VALUES (260, 1);
INSERT INTO `roles_menus` VALUES (261, 1);
INSERT INTO `roles_menus` VALUES (262, 1);
INSERT INTO `roles_menus` VALUES (263, 1);
INSERT INTO `roles_menus` VALUES (264, 1);
INSERT INTO `roles_menus` VALUES (265, 1);


-- ----------------------------
-- Table structure for adminUser
-- ----------------------------
DROP TABLE IF EXISTS `adminUser`;
CREATE TABLE `adminUser`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `avatar_id` bigint(20) NULL DEFAULT NULL COMMENT '头像',
  `email` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `enabled` bigint(20) NULL DEFAULT NULL COMMENT '状态：1启用、0禁用',
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '密码',
  `username` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户名',
  `dept_id` bigint(20) NULL DEFAULT NULL COMMENT '部门名称',
  `phone` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `job_id` bigint(20) NULL DEFAULT NULL COMMENT '岗位名称',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `last_password_reset_time` datetime(0) NULL DEFAULT NULL COMMENT '最后修改密码的日期',
  `nick_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `sex` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK_kpubos9gc2cvtkb0thktkbkes`(`email`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE,
  INDEX `FK5rwmryny6jthaaxkogownknqp`(`dept_id`) USING BTREE,
  INDEX `FKfftoc2abhot8f2wu6cl9a5iky`(`job_id`) USING BTREE,
  INDEX `FKpq2dhypk2qgt68nauh2by22jb`(`avatar_id`) USING BTREE,
  CONSTRAINT `FK5rwmryny6jthaaxkogownknqp` FOREIGN KEY (`dept_id`) REFERENCES `dept` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKfftoc2abhot8f2wu6cl9a5iky` FOREIGN KEY (`job_id`) REFERENCES `job` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKpq2dhypk2qgt68nauh2by22jb` FOREIGN KEY (`avatar_id`) REFERENCES `user_avatar` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统用户' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of adminUser
-- ----------------------------
INSERT INTO `adminUser` VALUES (1, 1, '<EMAIL>', 1, '$2a$10$fP.426qKaTmix50Oln8L.uav55gELhAd0Eg66Av4oG86u8km7D/Ky', 'admin', 2, '18888888888', 11, '2018-08-23 09:11:56', '2019-05-18 17:34:21', '管理员', '男', '2020-06-27 12:05:56', 0);
INSERT INTO `adminUser` VALUES (3, NULL, 'test@yshopnet', 1, '$2a$10$HhxyGZy.ulf3RvAwaHUGb.k.2i9PBpv4YbLMJWp8pES7pPhTyRCF.', 'test', 2, '17777777777', 12, '2018-12-27 20:05:26', '2019-04-01 09:15:24', '测试', '男', NULL, 0);
INSERT INTO `adminUser` VALUES (4, NULL, '<EMAIL>', 1, '$2a$10$IjehtV8MiXb8ni.Qz0wBteE7FjVn49cEcsSj2.ZBUqqHjnC3umSh.', 'test2', 2, '15136175247', 11, '2020-02-15 20:39:16', NULL, 'test2', '男', NULL, 0);
INSERT INTO `adminUser` VALUES (5, NULL, '<EMAIL>', 0, '$2a$10$f/VH35NBOBszycV9KEA1HenQ0qVjazDm8LacQU9PO.A4UizFxLMuq', 'qqqqq', 11, '15136175249', 8, '2020-02-17 11:12:01', NULL, 'eeeeee', '男', NULL, 0);
INSERT INTO `adminUser` VALUES (6, NULL, '<EMAIL>', 0, '$2a$10$3Vlo24eOmHHW7.3vAjqPSusfIINNo4JiujzxgqsaoWLx/d5de/jEm', '7777', 8, '15136175246', 8, '2020-05-18 19:43:30', NULL, '777', '男', NULL, 0);

-- ----------------------------
-- Table structure for user_avatar
-- ----------------------------
DROP TABLE IF EXISTS `user_avatar`;
CREATE TABLE `user_avatar`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `real_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '真实文件名',
  `path` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '路径',
  `size` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '大小',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '系统用户头像' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of user_avatar
-- ----------------------------
INSERT INTO `user_avatar` VALUES (1, '20200627120555818410.png', 'D:\\yshop\\avatar\\20200627120555818410.png', '12.93KB   ', '2020-06-27 12:05:56');

-- ----------------------------
-- Table structure for users_roles
-- ----------------------------
DROP TABLE IF EXISTS `users_roles`;
CREATE TABLE `users_roles`  (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE,
  INDEX `FKq4eq273l04bpu4efj0jd0jb98`(`role_id`) USING BTREE,
  CONSTRAINT `FKgd3iendaoyh04b95ykqise6qh` FOREIGN KEY (`user_id`) REFERENCES `adminUser` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKt4v0rrweyk393bdgt107vdx0x` FOREIGN KEY (`role_id`) REFERENCES `role` (`id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户角色关联' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of users_roles
-- ----------------------------
INSERT INTO `users_roles` VALUES (1, 1);
INSERT INTO `users_roles` VALUES (3, 2);
INSERT INTO `users_roles` VALUES (4, 2);
INSERT INTO `users_roles` VALUES (5, 2);
INSERT INTO `users_roles` VALUES (6, 2);

-- ----------------------------
-- Table structure for verification_code
-- ----------------------------
DROP TABLE IF EXISTS `verification_code`;
CREATE TABLE `verification_code`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '验证码',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '创建日期',
  `status` bit(1) NULL DEFAULT NULL COMMENT '状态：1有效、0过期',
  `type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '验证码类型：email或者短信',
  `value` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '接收邮箱或者手机号码',
  `scenes` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '业务名称：如重置邮箱、重置密码等',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '验证码' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of verification_code
-- ----------------------------

-- ----------------------------
-- Table structure for visits
-- ----------------------------
DROP TABLE IF EXISTS `visits`;
CREATE TABLE `visits`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `create_time` datetime(0) NULL DEFAULT NULL,
  `date` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ip_counts` bigint(20) NULL DEFAULT NULL,
  `pv_counts` bigint(20) NULL DEFAULT NULL,
  `week_day` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `UK_11aksgq87euk9bcyeesfs4vtp`(`date`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1266934639347150881 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '访客记录' ROW_FORMAT = COMPACT;

-- ----------------------------
-- Records of visits
-- ----------------------------
INSERT INTO `visits` VALUES (1265104717297319937, '2020-05-26 10:17:24', '2020-05-26', 1, 1, 'Tue');
INSERT INTO `visits` VALUES (1265104717297319938, '2020-05-27 16:32:32', '2020-05-27', 1, 2, 'Wed');
INSERT INTO `visits` VALUES (1265104717297319939, '2020-05-29 14:18:55', '2020-05-29', 1, 1, 'Fri');
INSERT INTO `visits` VALUES (1266934639347150849, '2020-05-31 11:28:51', '2020-05-31', 1, 1, 'Sun');
INSERT INTO `visits` VALUES (1266934639347150850, '2020-06-01 15:37:07', '2020-06-01', 1, 1, 'Mon');
INSERT INTO `visits` VALUES (1266934639347150851, '2020-06-09 11:52:36', '2020-06-09', 1, 2, 'Tue');
INSERT INTO `visits` VALUES (1266934639347150852, '2020-06-12 16:23:50', '2020-06-12', 1, 2, 'Fri');
INSERT INTO `visits` VALUES (1266934639347150853, '2020-06-13 16:49:12', '2020-06-13', 1, 1, 'Sat');
INSERT INTO `visits` VALUES (1266934639347150854, '2020-06-25 16:12:58', '2020-06-25', 1, 8, 'Thu');
INSERT INTO `visits` VALUES (1266934639347150855, '2020-06-26 10:15:43', '2020-06-26', 2, 7, 'Fri');
INSERT INTO `visits` VALUES (1266934639347150856, '2020-06-27 11:38:14', '2020-06-27', 1, 5, 'Sat');
INSERT INTO `visits` VALUES (1266934639347150857, '2020-06-28 15:59:08', '2020-06-28', 1, 6, 'Sun');
INSERT INTO `visits` VALUES (1266934639347150858, '2020-06-29 09:19:53', '2020-06-29', 1, 4, 'Mon');
INSERT INTO `visits` VALUES (1266934639347150859, '2020-06-30 09:55:25', '2020-06-30', 1, 5, 'Tue');
INSERT INTO `visits` VALUES (1266934639347150860, '2020-07-01 10:02:51', '2020-07-01', 1, 9, 'Wed');
INSERT INTO `visits` VALUES (1266934639347150861, '2020-07-02 10:55:09', '2020-07-02', 1, 7, 'Thu');
INSERT INTO `visits` VALUES (1266934639347150862, '2020-07-03 10:40:46', '2020-07-03', 1, 8, 'Fri');
INSERT INTO `visits` VALUES (1266934639347150863, '2020-07-04 10:03:30', '2020-07-04', 1, 4, 'Sat');
INSERT INTO `visits` VALUES (1266934639347150864, '2020-07-05 09:56:06', '2020-07-05', 1, 4, 'Sun');
INSERT INTO `visits` VALUES (1266934639347150865, '2020-07-06 14:35:25', '2020-07-06', 1, 2, 'Mon');
INSERT INTO `visits` VALUES (1266934639347150866, '2020-09-01 00:00:00', '2020-09-01', 4, 4, 'Tue');
INSERT INTO `visits` VALUES (1266934639347150867, '2020-09-02 00:00:00', '2020-09-02', 1, 4, 'Wed');
INSERT INTO `visits` VALUES (1266934639347150868, '2020-09-03 00:00:00', '2020-09-03', 1, 2, 'Thu');
INSERT INTO `visits` VALUES (1266934639347150869, '2020-09-04 00:00:00', '2020-09-04', 1, 3, 'Fri');
INSERT INTO `visits` VALUES (1266934639347150870, '2020-09-05 00:00:00', '2020-09-05', 5, 2, 'Sat');
INSERT INTO `visits` VALUES (1266934639347150871, '2020-09-06 00:00:00', '2020-09-06', 4, 2, 'Sun');
INSERT INTO `visits` VALUES (1266934639347150872, '2020-09-07 00:00:00', '2020-09-07', 5, 3, 'Mon');
INSERT INTO `visits` VALUES (1266934639347150873, '2020-09-08 00:00:00', '2020-09-08', 7, 15, 'Tue');
INSERT INTO `visits` VALUES (1266934639347150874, '2020-09-09 00:00:00', '2020-09-09', 5, 3, 'Wed');
INSERT INTO `visits` VALUES (1266934639347150875, '2020-09-10 00:00:00', '2020-09-10', 2, 4, 'Thu');
INSERT INTO `visits` VALUES (1266934639347150876, '2020-09-11 00:00:00', '2020-09-11', 12, 5, 'Fri');
INSERT INTO `visits` VALUES (1266934639347150877, '2020-09-12 00:00:00', '2020-09-12', 3, 4, 'Sat');
INSERT INTO `visits` VALUES (1266934639347150878, '2020-09-13 00:00:00', '2020-09-13', 10, 4, 'Sun');
INSERT INTO `visits` VALUES (1266934639347150879, '2020-09-14 00:00:00', '2020-09-14', 1, 1, 'Mon');
INSERT INTO `visits` VALUES (1266934639347150880, '2020-09-15 00:00:00', '2020-09-15', 1, 1, 'Tue');

-- ----------------------------
-- Table structure for yx_app_version
-- ----------------------------
DROP TABLE IF EXISTS `yx_app_version`;
CREATE TABLE `yx_app_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `version_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本code',
  `version_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本名称',
  `version_info` varchar(600) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '版本描述',
  `ios_url` varchar(10000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'ios store应用商店链接',
  `android_url` varchar(10000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '安卓下载链接',
  `force_update` tinyint(4) NULL DEFAULT NULL COMMENT '是否强制升级',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'app版本' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_app_version
-- ----------------------------

-- ----------------------------
-- Table structure for yx_article
-- ----------------------------
DROP TABLE IF EXISTS `yx_article`;
CREATE TABLE `yx_article`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '文章管理ID',
  `cid` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '1' COMMENT '分类id',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文章标题',
  `author` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文章作者',
  `image_input` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '文章图片',
  `synopsis` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文章简介',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `share_title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文章分享标题',
  `share_synopsis` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '文章分享简介',
  `visit` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '浏览次数',
  `sort` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '原文链接',
  `status` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '状态',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  `hide` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否隐藏',
  `admin_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '管理员id',
  `mer_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '商户id',
  `product_id` int(10) NULL DEFAULT 0 COMMENT '产品关联id',
  `is_hot` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否热门(小程序)',
  `is_banner` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否轮播图(小程序)',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文章管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_article
-- ----------------------------
INSERT INTO `yx_article` VALUES (2, '', 'yshop2版本上线了', 'yshop', 'http://127.0.0.1:8000/file/pic/20200527164526793230.png', 'yshop1.3版本上线了', '<h3 data-v-01881cfc=\"\" style=\"text-align: center;\"><p>yshop1.3版本上线了</p></h3>', '', '', '8', NULL, '', NULL, '2019-08-22 12:26:00', '2020-06-25 19:12:53', 0, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `yx_article` VALUES (3, '', '44', '444', 'http://127.0.0.1:8000/file/pic/20200527164543489004.png', '4444', '<p>55555</p>', '', '', '', 0, '', NULL, '2020-06-25 19:12:35', NULL, 1, 0, 0, 0, 0, 0, 0);
INSERT INTO `yx_article` VALUES (4, '', 'yshop3.0', 'yshop', 'https://image.dayouqiantu.cn/news.png', 'yshop3.0', '<p><br/></p><p><img src=\"https://image.dayouqiantu.cn/add.png\"/></p><p>yshop3.0</p>', '', '', '3', 0, '', NULL, '2020-07-02 12:15:13', '2020-07-03 17:58:19', 0, 0, 0, 0, 0, 0, 0);

-- ----------------------------
-- Table structure for yx_express
-- ----------------------------
DROP TABLE IF EXISTS `yx_express`;
CREATE TABLE `yx_express`  (
  `id` mediumint(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '快递公司id',
  `code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递公司简称',
  `name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '快递公司全称',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(255) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_show`(`is_show`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 433 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '快递公司表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_express
-- ----------------------------
INSERT INTO `yx_express` VALUES (426, 'STO', '申通快递', 0, 0, NULL, NULL, 0);
INSERT INTO `yx_express` VALUES (427, 'SF', '顺丰速运', 0, 0, NULL, NULL, 0);
INSERT INTO `yx_express` VALUES (428, 'ZTO', '中通快递', 0, 0, NULL, NULL, 0);
INSERT INTO `yx_express` VALUES (429, 'YTO', '圆通速递', 0, 0, NULL, '2020-09-06 09:59:36', 0);
INSERT INTO `yx_express` VALUES (430, '555', '555555', 0, 0, NULL, NULL, 1);
INSERT INTO `yx_express` VALUES (431, '555', '5555', 0, 0, NULL, NULL, 1);
INSERT INTO `yx_express` VALUES (432, '555', '555', 0, 0, '2020-06-26 12:29:48', NULL, 1);

-- ----------------------------
-- Table structure for yx_material
-- ----------------------------
DROP TABLE IF EXISTS `yx_material`;
CREATE TABLE `yx_material`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'PK',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `create_id` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者ID',
  `type` char(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '类型1、图片；2、视频',
  `group_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL COMMENT '分组ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '素材名',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT '' COMMENT '素材链接',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '素材库' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_material
-- ----------------------------
INSERT INTO `yx_material` VALUES ('05bf470c271f73fe2f572cad60b356f4', '2020-09-03 17:13:52', 'admin', '1', NULL, '1.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171351713145.jpg', NULL, 0);
INSERT INTO `yx_material` VALUES ('1888a13fdeca0d85dcc6bef413c531b9', '2020-06-26 17:22:21', 'admin', '1', NULL, 'list_16.png', 'http://127.0.0.1:8000/file/pic/20200626172220229003.png', NULL, 1);
INSERT INTO `yx_material` VALUES ('353760137dad5526444c01074f9490e0', '2020-09-03 17:18:08', 'admin', '1', NULL, '3.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', NULL, 0);
INSERT INTO `yx_material` VALUES ('3e2aa47438e059b06eea6327ae2260ec', '2020-06-26 18:25:07', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'news.png', 'https://image.dayouqiantu.cn/news.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('612f4f924840739f0ebdfbdce21d878b', '2020-06-26 18:25:07', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'sign.png', 'https://image.dayouqiantu.cn/sign.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('658cff7a98834617e46adfd105db0a3c', '2020-05-27 16:45:44', 'admin', '1', NULL, 'list_18.png', 'http://127.0.0.1:8000/file/pic/20200527164543489004.png', NULL, 1);
INSERT INTO `yx_material` VALUES ('6d2c5511b87631ffa6c5eecb9e4d736f', '2020-06-26 18:25:23', 'admin', '1', '8cc5996f6c4b54fcf5c0f2845a5d9afa', '5ca04fa9c08ef.jpg', 'https://image.dayouqiantu.cn/5ca04fa9c08ef.jpg', NULL, 0);
INSERT INTO `yx_material` VALUES ('6d4adf87cbb42bac277edf212dcbf2a7', '2020-06-26 18:20:58', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'collect.png', 'https://image.dayouqiantu.cn/collect.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('700a8cade3e6ff35a5e0d12b55849735', '2020-09-03 17:18:08', 'admin', '1', NULL, '7.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg', NULL, 0);
INSERT INTO `yx_material` VALUES ('7492896114a45879b88c08f02ef88a3e', '2020-06-26 18:25:07', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'pink.png', 'https://image.dayouqiantu.cn/pink.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('77b45342d9861a25323f284b155c4c9d', '2020-09-03 17:18:08', 'admin', '1', NULL, '6.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807641535.jpg', NULL, 0);
INSERT INTO `yx_material` VALUES ('7cc18f371ebcfaeb63d95f734251453b', '2020-06-26 18:25:24', 'admin', '1', '8cc5996f6c4b54fcf5c0f2845a5d9afa', '5ca0786c5d2c1.jpg', 'https://image.dayouqiantu.cn/5ca0786c5d2c1.jpg', NULL, 1);
INSERT INTO `yx_material` VALUES ('80116a9fea6f899d4530e82bc4d9b97b', '2020-09-03 17:12:09', 'admin', '1', NULL, 'avatar_default.png', 'https://consoleapi.xinxintuan.co/file/pic/20200903171208889668.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('808cbc928e818b2c0d12f5919042cdc2', '2020-06-26 11:08:09', 'admin', '1', NULL, 'list_34.png', 'http://127.0.0.1:8000/file/pic/20200626110808157592.png', NULL, 1);
INSERT INTO `yx_material` VALUES ('8a599a4721469bfcd1c313241c88bee0', '2020-06-26 18:20:58', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'buy.png', 'https://image.dayouqiantu.cn/buy.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('8e19af4d16b717574d863e6316cf15e4', '2020-06-26 18:24:51', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'add.png', 'https://image.dayouqiantu.cn/add.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('9ddc310b8b6e224cd1ee2f27f1d19017', '2020-06-26 18:25:07', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'sekill.png', 'https://image.dayouqiantu.cn/sekill.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('a0fc29a301a72712802ef38efe011fc4', '2020-09-11 09:39:13', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'avatar_default.png', 'https://consoleapi.xinxintuan.co/file/pic/20200911093912577832.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('a961d6c0f255083294c27bed47330ad9', '2020-05-27 16:45:27', 'admin', '1', '83ee56004ff88fd1ecdde5a43469ef2e', 'list_13.png', 'http://127.0.0.1:8000/file/pic/20200527164526793230.png', NULL, 1);
INSERT INTO `yx_material` VALUES ('b36ab3797cd310a210f5d3dec2d05349', '2020-09-03 17:18:08', 'admin', '1', NULL, '5.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807625149.jpg', NULL, 0);
INSERT INTO `yx_material` VALUES ('bd6c9dc5a3373fe377b4aeb0579ed1bb', '2020-06-26 17:35:22', 'admin', '1', NULL, 'list_18.png', 'http://127.0.0.1:8009/api/api/file/pic/20200626173520849370.png', NULL, 1);
INSERT INTO `yx_material` VALUES ('c246328a65ecb0170b67334e8ef4683e', '2020-06-26 18:20:58', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'goods.png', 'https://image.dayouqiantu.cn/goods.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('c48c27553f32321e5b5e6cdc2b934484', '2020-09-15 07:41:40', 'admin', '1', NULL, '1600126884(1).jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200915074139926178.jpg', NULL, 0);
INSERT INTO `yx_material` VALUES ('c83bb0d55e101f6ab525acb1f524b10b', '2020-06-26 17:39:44', 'admin', '1', NULL, 'list_34.png', 'http://127.0.0.1:8000/file/pic/20200626173943611335.png', NULL, 1);
INSERT INTO `yx_material` VALUES ('d63a722c0a0e55e73126ff9411ae1224', '2020-06-26 18:20:58', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'coupon.png', 'https://image.dayouqiantu.cn/coupon.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('d98b9f8ad2daa9e9a7567b3ae31b81d7', '2020-06-26 18:24:51', 'admin', '1', 'd9b1b9d69f5b72cda63e26dc9f087623', 'bargin.png', 'https://image.dayouqiantu.cn/bargin.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('eb8aa8d0846009f5b685e9f07a05c836', '2020-09-03 17:10:04', 'admin', '1', NULL, 'avatar_default.png', 'https://consoleapi.xinxintuan.co/file/pic/20200903171003417769.png', NULL, 0);
INSERT INTO `yx_material` VALUES ('f04dcbf4b61ab35d0f08f85a27ee92ff', '2020-06-26 18:25:24', 'admin', '1', '8cc5996f6c4b54fcf5c0f2845a5d9afa', '5ca081af6183f.jpg', 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', NULL, 0);
INSERT INTO `yx_material` VALUES ('f0e8dddf88c587f638e5a7859d8cc7a3', '2020-09-03 17:18:08', 'admin', '1', NULL, '4.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807568739.jpg', NULL, 0);

-- ----------------------------
-- Table structure for yx_material_group
-- ----------------------------
DROP TABLE IF EXISTS `yx_material_group`;
CREATE TABLE `yx_material_group`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT 'PK',
  `create_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
  `create_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建者ID',
  `name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '分组名',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '素材分组' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_material_group
-- ----------------------------
INSERT INTO `yx_material_group` VALUES ('83ee56004ff88fd1ecdde5a43469ef2e', '2020-05-27 16:45:08', NULL, 'test', '2020-06-26 18:20:00', 0);
INSERT INTO `yx_material_group` VALUES ('8cc5996f6c4b54fcf5c0f2845a5d9afa', '2020-06-26 11:07:26', NULL, '商品', '2020-06-26 18:20:14', 0);
INSERT INTO `yx_material_group` VALUES ('d9b1b9d69f5b72cda63e26dc9f087623', '2020-06-26 18:20:19', NULL, 'icon', NULL, 0);
INSERT INTO `yx_material_group` VALUES ('fe2e32c3ce27983c298ab344c6e1c4fa', '2020-05-27 16:45:13', NULL, 'bb', NULL, 1);

-- ----------------------------
-- Table structure for yx_shipping_templates
-- ----------------------------
DROP TABLE IF EXISTS `yx_shipping_templates`;
CREATE TABLE `yx_shipping_templates`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模板名称',
  `type` tinyint(1) NULL DEFAULT NULL COMMENT '计费方式',
  `region_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '地域以及费用',
  `appoint` tinyint(1) NULL DEFAULT NULL COMMENT '指定包邮开关',
  `appoint_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '指定包邮内容',
  `create_time` datetime(0) NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  `sort` int(11) NULL DEFAULT NULL COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 37 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '运费模板表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_shipping_templates
-- ----------------------------
INSERT INTO `yx_shipping_templates` VALUES (32, '9999', 1, '[{\"_continue\":\"1\",\"continue_price\":\"0\",\"first\":\"1\",\"price\":\"0\",\"region\":[{\"city_id\":\"0\",\"name\":\"默认全国\"}],\"regionName\":\"默认全国\"}]', 0, '[]', '2020-07-01 14:03:59', NULL, 1, 0);
INSERT INTO `yx_shipping_templates` VALUES (33, '8888', 1, '[{\"_continue\":\"1\",\"continue_price\":\"0\",\"first\":\"1\",\"price\":\"0\",\"region\":[{\"city_id\":\"0\",\"name\":\"默认全国\"}],\"regionName\":\"默认全国\"}]', 0, '[]', '2020-07-01 14:08:19', NULL, 1, 0);
INSERT INTO `yx_shipping_templates` VALUES (34, '默认全国运费模板', 1, '[{\"_continue\":\"1\",\"continue_price\":\"2\",\"first\":\"1\",\"price\":\"1\",\"region\":[{\"city_id\":\"0\",\"name\":\"默认全国\"}],\"regionName\":\"默认全国\"}]', 0, '[]', '2020-07-01 15:20:44', '2020-07-05 17:06:47', 0, 0);
INSERT INTO `yx_shipping_templates` VALUES (35, '包邮湖北省，河南省特例', 1, '[{\"_continue\":\"1\",\"continue_price\":\"1\",\"first\":\"1\",\"price\":\"1\",\"region\":[{\"city_id\":\"0\",\"name\":\"默认全国\"}],\"regionName\":\"默认全国\"},{\"_continue\":\"1\",\"continue_price\":\"5\",\"first\":\"1\",\"price\":\"2\",\"region\":[{\"children\":[{\"city_id\":\"367396\"},{\"city_id\":\"370304\"},{\"city_id\":\"372971\"},{\"city_id\":\"376365\"},{\"city_id\":\"379245\"},{\"city_id\":\"382878\"},{\"city_id\":\"383890\"},{\"city_id\":\"387707\"},{\"city_id\":\"389719\"},{\"city_id\":\"392737\"},{\"city_id\":\"395254\"},{\"city_id\":\"396611\"},{\"city_id\":\"398169\"},{\"city_id\":\"403311\"},{\"city_id\":\"408287\"},{\"city_id\":\"411925\"},{\"city_id\":\"417174\"},{\"city_id\":\"420258\"}],\"city_id\":\"367395\",\"name\":\"河南省\"}],\"regionName\":\"河南省\"}]', 1, '[{\"a_num\":\"2\",\"a_price\":\"100\",\"place\":[{\"children\":[{\"city_id\":\"420825\"},{\"city_id\":\"424449\"},{\"city_id\":\"425508\"},{\"city_id\":\"427710\"},{\"city_id\":\"429501\"},{\"city_id\":\"432415\"},{\"city_id\":\"432829\"},{\"city_id\":\"434548\"},{\"city_id\":\"437779\"},{\"city_id\":\"440639\"},{\"city_id\":\"445040\"},{\"city_id\":\"446212\"},{\"city_id\":\"447268\"},{\"city_id\":\"449865\"}],\"city_id\":\"420824\",\"name\":\"湖北省\"}],\"placeName\":\"湖北省\"}]', '2020-07-01 15:26:38', '2020-07-05 17:08:39', 0, 0);
INSERT INTO `yx_shipping_templates` VALUES (36, '8888', 1, '[{\"_continue\":\"1\",\"continue_price\":\"1\",\"first\":\"1\",\"price\":\"1\",\"region\":[{\"city_id\":\"0\",\"name\":\"默认全国\"}],\"regionName\":\"默认全国\"}]', 1, '[]', '2020-07-01 16:04:28', '2020-07-01 16:07:31', 1, 0);

-- ----------------------------
-- Table structure for yx_shipping_templates_free
-- ----------------------------
DROP TABLE IF EXISTS `yx_shipping_templates_free`;
CREATE TABLE `yx_shipping_templates_free`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省ID',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市ID',
  `number` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '包邮件数',
  `price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '包邮金额',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '计费方式',
  `uniqid` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分组唯一值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 198 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_shipping_templates_free
-- ----------------------------
INSERT INTO `yx_shipping_templates_free` VALUES (158, 13267, 36, 13268, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (159, 13267, 36, 18128, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (160, 13267, 36, 24371, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (161, 13267, 36, 26779, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (162, 13267, 36, 32688, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (163, 13267, 36, 38160, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (164, 13267, 36, 44570, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (165, 13267, 36, 49190, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (166, 13267, 36, 52018, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (167, 13267, 36, 58247, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (168, 13267, 36, 61804, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (169, 13267, 36, 66813, 1.00, 1.00, 1, 'ddd9604e6fb54edab5ced2e32c41db61');
INSERT INTO `yx_shipping_templates_free` VALUES (184, 420824, 35, 420825, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (185, 420824, 35, 424449, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (186, 420824, 35, 425508, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (187, 420824, 35, 427710, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (188, 420824, 35, 429501, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (189, 420824, 35, 432415, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (190, 420824, 35, 432829, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (191, 420824, 35, 434548, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (192, 420824, 35, 437779, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (193, 420824, 35, 440639, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (194, 420824, 35, 445040, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (195, 420824, 35, 446212, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (196, 420824, 35, 447268, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');
INSERT INTO `yx_shipping_templates_free` VALUES (197, 420824, 35, 449865, 2.00, 100.00, 1, '5a3c9a0dbe084d568b6f70e19e28c695');

-- ----------------------------
-- Table structure for yx_shipping_templates_region
-- ----------------------------
DROP TABLE IF EXISTS `yx_shipping_templates_region`;
CREATE TABLE `yx_shipping_templates_region`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `province_id` int(11) NOT NULL DEFAULT 0 COMMENT '省ID',
  `temp_id` int(11) NOT NULL DEFAULT 0 COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT 0 COMMENT '城市ID',
  `first` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '首件',
  `first_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '首件运费',
  `continues` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '续件',
  `continue_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '续件运费',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '计费方式',
  `uniqid` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '分组唯一值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 250 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_shipping_templates_region
-- ----------------------------
INSERT INTO `yx_shipping_templates_region` VALUES (175, 0, 32, 0, 1.00, 0.00, 1.00, 0.00, 1, 'da4f15807a2e47b880d8db454a350a5e');
INSERT INTO `yx_shipping_templates_region` VALUES (176, 0, 33, 0, 1.00, 0.00, 1.00, 0.00, 1, '6ddd27e504e3451aad294cfb9e105fa2');
INSERT INTO `yx_shipping_templates_region` VALUES (210, 0, 36, 0, 1.00, 1.00, 1.00, 1.00, 1, '14e3da43097248c29ed0d41eded5d9c0');
INSERT INTO `yx_shipping_templates_region` VALUES (211, 0, 34, 0, 1.00, 1.00, 1.00, 2.00, 1, '2c8ea986f1a149329a72951c7670e9b4');
INSERT INTO `yx_shipping_templates_region` VALUES (231, 0, 35, 0, 1.00, 1.00, 1.00, 1.00, 1, 'cc8ca17c6a7145adbf78498b2bdc07fe');
INSERT INTO `yx_shipping_templates_region` VALUES (232, 367395, 35, 367396, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (233, 367395, 35, 370304, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (234, 367395, 35, 372971, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (235, 367395, 35, 376365, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (236, 367395, 35, 379245, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (237, 367395, 35, 382878, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (238, 367395, 35, 383890, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (239, 367395, 35, 387707, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (240, 367395, 35, 389719, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (241, 367395, 35, 392737, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (242, 367395, 35, 395254, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (243, 367395, 35, 396611, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (244, 367395, 35, 398169, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (245, 367395, 35, 403311, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (246, 367395, 35, 408287, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (247, 367395, 35, 411925, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (248, 367395, 35, 417174, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');
INSERT INTO `yx_shipping_templates_region` VALUES (249, 367395, 35, 420258, 1.00, 2.00, 1.00, 5.00, 1, 'f883508219224a6d8a10ccbf99c0e9ba');

-- ----------------------------
-- Table structure for yx_store_cart
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_cart`;
CREATE TABLE `yx_store_cart`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '购物车表ID',
  `uid` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'product' COMMENT '类型',
  `product_id` bigint(20) UNSIGNED NOT NULL COMMENT '商品ID',
  `product_attr_unique` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商品属性',
  `cart_num` smallint(5) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品数量',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_pay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0 = 未购买 1 = 已购买',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `is_new` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为立即购买',
  `combination_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '拼团id',
  `seckill_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '秒杀产品ID',
  `bargain_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '砍价id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`uid`) USING BTREE,
  INDEX `goods_id`(`product_id`) USING BTREE,
  INDEX `uid`(`uid`, `is_pay`) USING BTREE,
  INDEX `uid_2`(`uid`, `is_del`) USING BTREE,
  INDEX `uid_3`(`uid`, `is_new`) USING BTREE,
  INDEX `type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 150 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '购物车表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_cart
-- ----------------------------
INSERT INTO `yx_store_cart` VALUES (86, 40, 'product', 6, '91d12632e2c047d7b34dab48cfc5a34e', 1, '2020-09-12 14:54:08', '2020-09-12 14:54:45', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (87, 43, 'product', 7, '789984b858bb489bb6313107e11fdc15', 1, '2020-09-12 17:04:12', '2020-09-12 17:06:04', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (88, 43, 'product', 7, '789984b858bb489bb6313107e11fdc15', 1, '2020-09-12 17:06:54', '2020-09-12 17:07:00', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (89, 43, 'product', 7, '789984b858bb489bb6313107e11fdc15', 1, '2020-09-12 17:07:53', '2020-09-12 17:07:57', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (90, 43, 'product', 8, '2f7ee7305cfe43f782ea28c0235256db', 1, '2020-09-12 17:27:03', '2020-09-12 17:27:24', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (91, 43, 'product', 8, '183fcbaffb3849c48de2e87a226209b9', 1, '2020-09-12 17:48:03', '2020-09-12 17:48:14', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (92, 43, 'product', 7, '789984b858bb489bb6313107e11fdc15', 1, '2020-09-12 17:54:50', NULL, 0, 0, 0, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (93, 43, 'product', 8, '67138eee0bed408abe7d32b0ab2060b0', 1, '2020-09-12 17:56:08', NULL, 0, 0, 0, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (94, 43, 'product', 7, '789984b858bb489bb6313107e11fdc15', 1, '2020-09-12 18:10:27', NULL, 0, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (95, 43, 'product', 8, '183fcbaffb3849c48de2e87a226209b9', 1, '2020-09-12 18:10:44', '2020-09-12 18:11:05', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (96, 43, 'product', 8, 'f25af180071742458622febf7dde528b', 1, '2020-09-12 18:18:27', '2020-09-12 18:18:37', 1, 0, 1, 10, 0, 0);
INSERT INTO `yx_store_cart` VALUES (97, 42, 'product', 3, 'b951f8e2ed4641f08be1bce99b716a6d', 1, '2020-09-12 19:27:31', NULL, 0, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (98, 42, 'product', 8, '013f89ec740f478d8144f052909c0807', 1, '2020-09-12 19:42:36', NULL, 0, 0, 1, 10, 0, 0);
INSERT INTO `yx_store_cart` VALUES (99, 40, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-12 23:18:58', '2020-09-13 15:01:09', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (100, 53, 'product', 5, '4dd9ea6fb9f1414aabf18b713f0f6832', 1, '2020-09-13 02:29:15', '2020-09-13 02:29:37', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (101, 53, 'product', 8, '013f89ec740f478d8144f052909c0807', 1, '2020-09-13 03:54:24', '2020-09-13 03:54:28', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (102, 42, 'product', 8, '8112da382da7420f961203372f7c1d31', 1, '2020-09-13 06:59:31', NULL, 0, 0, 1, 0, 7, 0);
INSERT INTO `yx_store_cart` VALUES (103, 44, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-13 10:15:35', '2020-09-13 10:18:02', 1, 0, 1, 0, 6, 0);
INSERT INTO `yx_store_cart` VALUES (104, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 10:41:44', '2020-09-13 10:41:49', 1, 0, 1, 5, 0, 0);
INSERT INTO `yx_store_cart` VALUES (105, 53, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-13 10:42:37', '2020-09-13 10:42:42', 1, 0, 1, 0, 6, 0);
INSERT INTO `yx_store_cart` VALUES (106, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 10:58:02', '2020-09-13 11:04:18', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (107, 42, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:04:15', '2020-09-14 10:59:22', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (108, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:05:22', '2020-09-13 11:05:26', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (109, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:06:59', '2020-09-13 11:07:03', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (110, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:07:24', '2020-09-13 11:07:29', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (111, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:08:49', '2020-09-13 11:08:53', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (112, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:09:01', '2020-09-13 11:09:05', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (113, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:11:33', '2020-09-13 11:11:38', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (114, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:18:03', '2020-09-13 11:18:06', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (115, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:42:10', '2020-09-13 11:42:13', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (116, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:43:03', '2020-09-13 11:43:09', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (117, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:46:23', '2020-09-13 11:46:28', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (118, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:56:22', '2020-09-13 11:56:24', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (119, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 11:57:35', '2020-09-13 11:57:38', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (120, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 12:00:39', '2020-09-13 12:00:42', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (121, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 20:00:13', '2020-09-13 20:00:16', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (122, 53, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 20:01:24', '2020-09-13 20:01:49', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (123, 42, 'product', 2, '04e9d586cef8464aaee0a45439f12520', 1, '2020-09-13 21:32:46', '2020-09-13 21:33:38', 1, 0, 1, 16, 0, 0);
INSERT INTO `yx_store_cart` VALUES (124, 42, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 21:33:49', '2020-09-13 21:33:51', 1, 0, 1, 5, 0, 0);
INSERT INTO `yx_store_cart` VALUES (125, 54, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-13 22:04:08', '2020-09-13 22:05:10', 1, 0, 1, 5, 0, 0);
INSERT INTO `yx_store_cart` VALUES (126, 50, 'product', 3, 'b951f8e2ed4641f08be1bce99b716a6d', 1, '2020-09-13 22:04:30', '2020-09-13 22:04:52', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (127, 50, 'product', 4, '6a1919d9178b48f3a75fe4640e51693e', 1, '2020-09-13 22:12:38', '2020-09-13 22:12:44', 1, 0, 1, 7, 0, 0);
INSERT INTO `yx_store_cart` VALUES (128, 49, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-13 23:59:44', '2020-09-14 00:01:07', 1, 0, 1, 6, 0, 0);
INSERT INTO `yx_store_cart` VALUES (129, 49, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-14 00:02:41', '2020-09-14 00:02:42', 1, 0, 1, 6, 0, 0);
INSERT INTO `yx_store_cart` VALUES (130, 49, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-14 00:02:48', '2020-09-14 00:02:50', 1, 0, 1, 6, 0, 0);
INSERT INTO `yx_store_cart` VALUES (131, 59, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-14 00:03:19', '2020-09-14 00:04:08', 1, 0, 1, 6, 0, 0);
INSERT INTO `yx_store_cart` VALUES (132, 49, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-14 00:03:36', '2020-09-14 00:03:39', 1, 0, 1, 6, 0, 0);
INSERT INTO `yx_store_cart` VALUES (133, 55, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-14 00:18:43', NULL, 0, 0, 1, 6, 0, 0);
INSERT INTO `yx_store_cart` VALUES (134, 42, 'product', 3, '43cdbd1043474b4e97a1cffffa18071c', 1, '2020-09-14 08:40:57', '2020-09-14 08:41:02', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (135, 42, 'product', 3, '43cdbd1043474b4e97a1cffffa18071c', 1, '2020-09-14 08:41:11', '2020-09-14 08:41:17', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (136, 42, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-14 09:56:15', NULL, 0, 0, 1, 5, 0, 0);
INSERT INTO `yx_store_cart` VALUES (137, 61, 'product', 2, '04e9d586cef8464aaee0a45439f12520', 1, '2020-09-14 10:01:46', '2020-09-14 10:02:55', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (138, 61, 'product', 2, '04e9d586cef8464aaee0a45439f12520', 1, '2020-09-14 10:08:05', '2020-09-14 10:08:07', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (139, 40, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-14 10:13:05', '2020-09-14 10:13:17', 1, 0, 1, 5, 0, 0);
INSERT INTO `yx_store_cart` VALUES (140, 42, 'product', 1, 'd4ce8cd4bda84d479c11348e060afb8e', 1, '2020-09-14 10:18:05', '2020-09-14 10:18:12', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (141, 42, 'product', 1, 'd4ce8cd4bda84d479c11348e060afb8e', 1, '2020-09-14 10:20:20', '2020-09-14 10:20:24', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (142, 42, 'product', 1, 'd4ce8cd4bda84d479c11348e060afb8e', 1, '2020-09-14 10:22:06', '2020-09-14 10:22:12', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (143, 42, 'product', 4, '6a1919d9178b48f3a75fe4640e51693e', 1, '2020-09-14 10:50:38', '2020-09-14 10:50:44', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (144, 42, 'product', 3, '43cdbd1043474b4e97a1cffffa18071c', 1, '2020-09-14 11:00:30', '2020-09-14 11:00:36', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (145, 71, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-14 12:52:02', '2020-09-14 12:53:06', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (146, 71, 'product', 5, '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1, '2020-09-14 13:16:45', '2020-09-14 13:16:46', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (147, 76, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-14 22:20:51', NULL, 0, 0, 0, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (148, 76, 'product', 6, '90babee9cea24645870c6027fc420d42', 1, '2020-09-14 22:20:55', '2020-09-14 22:21:11', 1, 0, 1, 0, 0, 0);
INSERT INTO `yx_store_cart` VALUES (149, 54, 'product', 2, '04e9d586cef8464aaee0a45439f12520', 1, '2020-09-14 22:34:54', '2020-09-14 22:34:59', 1, 0, 1, 0, 0, 0);

-- ----------------------------
-- Table structure for yx_store_category
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_category`;
CREATE TABLE `yx_store_category`  (
  `id` mediumint(11) NOT NULL AUTO_INCREMENT COMMENT '商品分类表ID',
  `pid` mediumint(11) NOT NULL COMMENT '父id',
  `cate_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类名称',
  `sort` mediumint(11) NULL DEFAULT NULL COMMENT '排序',
  `pic` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '图标',
  `is_show` tinyint(1) NULL DEFAULT 1 COMMENT '是否推荐',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '删除状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `is_base`(`is_show`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `add_time`(`create_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_category
-- ----------------------------
INSERT INTO `yx_store_category` VALUES (1, 0, '图文广告', 1, '', 1, '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_store_category` VALUES (2, 1, '写真', 1, 'http://localhost:8000/file/pic/资讯-20200311043727918.png', 1, '2020-06-25 18:54:35', NULL, 1);
INSERT INTO `yx_store_category` VALUES (3, 1, '平面设计', 1, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', 1, '2020-06-25 18:54:35', '2020-09-09 21:00:17', 0);
INSERT INTO `yx_store_category` VALUES (5, 1, '名片', 1, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg', 1, '2020-06-25 18:54:35', '2020-09-09 21:00:32', 0);
INSERT INTO `yx_store_category` VALUES (6, 0, '666', 1, '', 1, '2020-06-25 18:54:35', NULL, 1);
INSERT INTO `yx_store_category` VALUES (7, 0, '99995', 1, '', 1, '2020-06-25 18:54:35', NULL, 1);
INSERT INTO `yx_store_category` VALUES (8, 7, '8888', 1, 'http://localhost:8000/file/pic/钻石-20200328094531898.jpg', 1, '2020-06-25 18:54:35', NULL, 1);
INSERT INTO `yx_store_category` VALUES (9, 0, '3333', 1, '', 1, '2020-06-25 17:11:45', NULL, 1);


-- ----------------------------
-- Table structure for yx_store_coupon
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_coupon`;
CREATE TABLE `yx_store_coupon`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '优惠券表ID',
  `title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '优惠券名称',
  `integral` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '兑换消耗积分值',
  `coupon_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '兑换的优惠券面值',
  `use_min_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最低消费多少金额可用优惠券',
  `coupon_time` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券有效期限（单位：天）',
  `sort` int(11) UNSIGNED NOT NULL DEFAULT 1 COMMENT '排序',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态（0：关闭，1：开启）',
  `product_id` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品ids',
  `type` tinyint(255) NULL DEFAULT 0 COMMENT '优惠券类型 0-通用 1-商品券',
  `create_time` datetime(0) NOT NULL COMMENT '兑换项目添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `state`(`status`) USING BTREE,
  INDEX `add_time`(`create_time`) USING BTREE,
  INDEX `coupon_time`(`coupon_time`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '优惠券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_coupon
-- ----------------------------
INSERT INTO `yx_store_coupon` VALUES (6, '全场通用', 0, 1.00, 10.00, 100, 0, 1, '', 0, '2020-09-12 17:57:02', NULL, 0);
INSERT INTO `yx_store_coupon` VALUES (7, '多规格商品券', 0, 1.00, 5.00, 100, 0, 1, '8', 1, '2020-09-12 17:57:46', NULL, 0);

-- ----------------------------
-- Table structure for yx_store_coupon_issue
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_coupon_issue`;
CREATE TABLE `yx_store_coupon_issue`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `cname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '',
  `cid` int(10) NULL DEFAULT NULL COMMENT '优惠券ID',
  `ctype` tinyint(1) NULL DEFAULT NULL COMMENT '优惠券类型 0-通用 1-商品券',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT '优惠券领取开启时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '优惠券领取结束时间',
  `total_count` int(10) NULL DEFAULT NULL COMMENT '优惠券领取数量',
  `remain_count` int(10) NULL DEFAULT NULL COMMENT '优惠券剩余领取数量',
  `is_permanent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否无限张数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1 正常 0 未开启 -1 已无效',
  `is_del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '优惠券添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `cid`(`cid`) USING BTREE,
  INDEX `start_time`(`start_time`, `end_time`) USING BTREE,
  INDEX `remain_count`(`remain_count`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '优惠券前台领取表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_coupon_issue
-- ----------------------------
INSERT INTO `yx_store_coupon_issue` VALUES (6, '多规格商品券', 7, 1, '2020-09-12 18:07:10', '2020-09-30 00:00:00', 100, 99, 0, 1, 0, '2020-09-12 18:07:19', NULL);
INSERT INTO `yx_store_coupon_issue` VALUES (7, '全场通用', 6, 0, '2020-09-12 18:07:25', '2020-09-29 00:00:00', 100, 100, 0, 1, 0, '2020-09-12 18:07:34', NULL);

-- ----------------------------
-- Table structure for yx_store_coupon_issue_user
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_coupon_issue_user`;
CREATE TABLE `yx_store_coupon_issue_user`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NULL DEFAULT NULL COMMENT '领取优惠券用户ID',
  `issue_coupon_id` int(10) NULL DEFAULT NULL COMMENT '优惠券前台领取ID',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '领取时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid`, `issue_coupon_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '优惠券前台用户领取记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_coupon_issue_user
-- ----------------------------

-- ----------------------------
-- Table structure for yx_store_coupon_user
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_coupon_user`;
CREATE TABLE `yx_store_coupon_user`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '优惠券发放记录id',
  `cid` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '兑换的项目id',
  `uid` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券所属用户',
  `coupon_title` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '优惠券名称',
  `coupon_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠券的面值',
  `use_min_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '最低消费多少金额可用优惠券',
  `create_time` datetime(0) NOT NULL COMMENT '优惠券创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `end_time` datetime(0) NOT NULL COMMENT '优惠券结束时间',
  `use_time` datetime(0) NULL DEFAULT NULL COMMENT '使用时间',
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'send' COMMENT '获取方式',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态（0：未使用，1：已使用, 2:已过期）',
  `is_fail` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否有效',
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `cid`(`cid`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `add_time`(`create_time`) USING BTREE,
  INDEX `end_time`(`end_time`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `is_fail`(`is_fail`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '优惠券发放记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_coupon_user
-- ----------------------------

-- ----------------------------
-- Table structure for yx_store_customer
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_customer`;
CREATE TABLE `yx_store_customer`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `nick_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `open_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'openId',
  `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '修改时间',
  `is_del` tinyint(1) NULL DEFAULT 0,
  `is_enable` tinyint(1) NULL DEFAULT 1 COMMENT '是否启用',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 88 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单通知用户表 接收订单通知的用户' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_customer
-- ----------------------------

-- ----------------------------
-- Table structure for yx_store_order
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_order`;
CREATE TABLE `yx_store_order`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单号',
  `extend_order_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '额外订单号',
  `uid` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `real_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户姓名',
  `user_phone` varchar(18) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户电话',
  `user_address` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '详细地址',
  `cart_id` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '[]' COMMENT '购物车id',
  `freight_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '运费金额',
  `total_num` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单商品总数',
  `total_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '订单总价',
  `total_postage` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '邮费',
  `pay_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际支付金额',
  `pay_postage` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付邮费',
  `deduction_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '抵扣金额',
  `coupon_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券id',
  `coupon_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠券金额',
  `paid` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付状态',
  `pay_time` datetime(0) NULL DEFAULT NULL COMMENT '支付时间',
  `pay_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '支付方式',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态（-1 : 申请退款 -2 : 退货成功 0：待发货；1：待收货；2：已收货；3：已完成；-1：已退款）',
  `refund_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 未退款 1 申请中 2 已退款',
  `refund_suspend_status` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '申请退款时：中断时的订单状态',
  `refund_reason_wap_img` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退款图片',
  `refund_reason_wap_explain` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '退款用户说明',
  `refund_reason_time` datetime(0) NULL DEFAULT NULL COMMENT '退款时间',
  `refund_reason_wap` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '前台退款原因',
  `refund_reason` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '不退款的理由',
  `refund_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `delivery_sn` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '快递公司编号',
  `delivery_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递名称/送货人姓名',
  `delivery_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '发货类型',
  `delivery_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '快递单号/手机号',
  `gain_integral` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '消费赚取积分',
  `use_integral` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '使用积分',
  `pay_integral` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际支付积分',
  `back_integral` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '给用户退了多少积分',
  `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '备注',
  `is_del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  `unique` char(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '唯一id(md5加密)类似id',
  `remark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '管理员备注',
  `mer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户ID',
  `is_mer_check` tinyint(3) UNSIGNED NOT NULL DEFAULT 0,
  `combination_id` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '拼团产品id0一般产品',
  `pink_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '拼团id 0没有拼团',
  `cost` decimal(8, 2) UNSIGNED NOT NULL COMMENT '成本价',
  `seckill_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '秒杀产品ID',
  `bargain_id` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '砍价id',
  `verify_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '核销码',
  `store_id` int(11) NOT NULL DEFAULT 0 COMMENT '门店id',
  `shipping_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '配送方式 1=快递 ，2=门店自提',
  `is_channel` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '支付渠道(0微信公众号1微信小程序)',
  `is_remind` tinyint(1) UNSIGNED NULL DEFAULT 0,
  `is_system_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id_2`(`order_id`, `uid`) USING BTREE,
  UNIQUE INDEX `unique`(`unique`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `add_time`(`create_time`) USING BTREE,
  INDEX `pay_price`(`pay_price`) USING BTREE,
  INDEX `paid`(`paid`) USING BTREE,
  INDEX `pay_time`(`pay_time`) USING BTREE,
  INDEX `pay_type`(`pay_type`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `coupon_id`(`coupon_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_order
-- ----------------------------

-- ----------------------------
-- Table structure for yx_store_order_cart_info
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_order_cart_info`;
CREATE TABLE `yx_store_order_cart_info`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `oid` bigint(20) UNSIGNED NOT NULL COMMENT '订单id',
  `order_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `cart_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '购物车id',
  `product_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `cart_info` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '购买东西的详细信息',
  `unique` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '唯一id',
  `is_after_sales` tinyint(1) DEFAULT '0' COMMENT '是否能售后0不能1能',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `oid`(`oid`, `unique`) USING BTREE,
  INDEX `cart_id`(`cart_id`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单购物详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_order_cart_info
-- ----------------------------

-- ----------------------------
-- Table structure for yx_store_order_status
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_order_status`;
CREATE TABLE `yx_store_order_status`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `oid` bigint(20) UNSIGNED NOT NULL COMMENT '订单id',
  `change_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作类型',
  `change_message` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '操作备注',
  `change_time` datetime(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `oid`(`oid`) USING BTREE,
  INDEX `change_type`(`change_type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '订单操作记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_order_status
-- ----------------------------

-- ----------------------------
-- Table structure for yx_store_pink
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_pink`;
CREATE TABLE `yx_store_pink`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '订单id 生成',
  `order_id_key` int(10) UNSIGNED NOT NULL COMMENT '订单id  数据库',
  `total_num` int(10) UNSIGNED NOT NULL COMMENT '购买商品个数',
  `total_price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '购买总金额',
  `cid` bigint(20) UNSIGNED NOT NULL COMMENT '拼团产品id',
  `pid` bigint(20) UNSIGNED NOT NULL COMMENT '产品id',
  `people` int(10) UNSIGNED NOT NULL COMMENT '拼团总人数',
  `price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '拼团产品单价',
  `create_time` datetime(0) NOT NULL COMMENT '开始时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `stop_time` datetime(0) NULL DEFAULT NULL,
  `k_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '团长id 0为团长',
  `is_tpl` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否发送模板消息0未发送1已发送',
  `is_refund` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否退款 0未退款 1已退款',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态1进行中2已完成3未完成',
  `is_del` tinyint(1) NULL DEFAULT 0 COMMENT '是否删除1删除 0否',
  `unique_id` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '库存唯一值',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '拼团表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_pink
-- ----------------------------

-- ----------------------------
-- Table structure for yx_store_product
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_product`;
CREATE TABLE `yx_store_product`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品id',
  `mer_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '商户Id(0为总后台管理员创建,不为0的时候是商户后台创建)',
  `image` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品图片',
  `rec_image` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品推荐图片',
  `slider_image` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '轮播图',
  `banner_img` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT 'Banner图片',
  `store_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品名称',
  `store_info` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品简介',
  `keyword` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '关键字',
  `bar_code` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '产品条码（一维码）',
  `cate_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '分类id',
  `cate1st_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '一级分类id',
  `price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商品价格',
  `vip_price` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '会员价格',
  `ot_price` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '市场价',
  `postage` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '邮费',
  `unit_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '单位名',
  `sort` smallint(11) NULL DEFAULT 0 COMMENT '排序',
  `shelf_sort` smallint(11) NULL DEFAULT 0 COMMENT '展架排序',
  `sales` mediumint(11) UNSIGNED NULL DEFAULT 0 COMMENT '销量',
  `stock` mediumint(11) UNSIGNED NULL DEFAULT 0 COMMENT '库存',
  `is_show` tinyint(1) NULL DEFAULT 1 COMMENT '上架状态（0：未上架，1：上架）',
  `is_hide` tinyint(1) NULL DEFAULT 0 COMMENT '隐藏（0：显示，1：隐藏）',
  `is_suspend` tinyint(1) NULL DEFAULT 0 COMMENT '停售（0：在售，1：停售）',
  `is_shelf` tinyint(1) NULL DEFAULT 0 COMMENT '在展架（0：不在，1：在展架）',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '是否热卖',
  `is_benefit` tinyint(1) NULL DEFAULT 0 COMMENT '是否优惠',
  `is_best` tinyint(1) NULL DEFAULT 0 COMMENT '是否精品',
  `is_new` tinyint(1) NULL DEFAULT 0 COMMENT '是否新品',
  `description` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '产品描述',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_postage` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否包邮',
  `is_del` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否删除',
  `mer_use` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '商户是否代理 0不可代理1可代理',
  `give_integral` decimal(8, 2) UNSIGNED NULL DEFAULT NULL COMMENT '获得积分',
  `cost` decimal(8, 2) UNSIGNED NULL DEFAULT NULL COMMENT '成本价',
  `is_seckill` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '秒杀状态 0 未开启 1已开启',
  `is_bargain` tinyint(1) UNSIGNED NULL DEFAULT NULL COMMENT '砍价状态 0未开启 1开启',
  `is_good` tinyint(1) NULL DEFAULT 0 COMMENT '是否优品推荐',
  `ficti` mediumint(11) NULL DEFAULT 100 COMMENT '虚拟销量',
  `browse` int(11) NULL DEFAULT 0 COMMENT '浏览量',
  `code_path` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '产品二维码地址(用户小程序海报)',
  `temp_id` int(10) NULL DEFAULT NULL COMMENT '运费模板ID',
  `spec_type` tinyint(1) NULL DEFAULT 0 COMMENT '规格 0单 1多',
  `integral` int(11) NULL DEFAULT 0 COMMENT '需要多少积分兑换 只在开启积分兑换时生效',
  `limit_buy` int(11) NULL DEFAULT 0 COMMENT '限购数量',
  `depot_id` bigint(20) DEFAULT NULL COMMENT '主发货仓库ID',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `is_hot`(`is_hot`) USING BTREE,
  INDEX `is_benefit`(`is_benefit`) USING BTREE,
  INDEX `is_best`(`is_best`) USING BTREE,
  INDEX `is_new`(`is_new`) USING BTREE,
  INDEX `toggle_on_sale, is_del`(`is_del`) USING BTREE,
  INDEX `price`(`price`) USING BTREE,
  INDEX `is_show`(`is_show`) USING BTREE,
  INDEX `sort`(`sort`) USING BTREE,
  INDEX `sales`(`sales`) USING BTREE,
  INDEX `add_time`(`create_time`) USING BTREE,
  INDEX `is_postage`(`is_postage`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品表' ROW_FORMAT = DYNAMIC;

ALTER TABLE yx_store_product add column `pre_publish_status` tinyint(1) NOT NULL DEFAULT 0;
ALTER TABLE yx_store_product add column `pre_publish_time` datetime(0) NULL DEFAULT NULL;

-- ----------------------------
-- Records of yx_store_product
-- ----------------------------
-- INSERT INTO `yx_store_product` VALUES (1, 0, 'https://image.dayouqiantu.cn/5ca04fa9c08ef.jpg', 'https://image.dayouqiantu.cn/5ca081af6183f.jpg,https://image.dayouqiantu.cn/5ca04fa9c08ef.jpg', '照片打印', '照片打印', '照片打印', '', '5', 2.00, 0.00, 1.00, 0.00, '张', 0, 8, 992, 1, 0, 0, 0, 0, '<p><br/></p><p><img src=\"https://image.dayouqiantu.cn/5ca04fa9c08ef.jpg\"/></p>', '2020-09-04 17:41:30', '2020-09-05 20:57:49', 0, 0, 0, 0.00, 3.00, 0, NULL, 0, 0, 3, '', 0, 34, 0, 0,0);
-- INSERT INTO `yx_store_product` VALUES (2, 0, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg,https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg,https://consoleapi.xinxintuan.co/file/pic/20200903171807641535.jpg', 'X型展架', 'X型展架', 'X型展架', '', '3', 10.00, 0.00, 12.00, 0.00, '张', 0, 20, 3310, 1, 1, 1, 0, 0, '<p><br/></p><p><img src=\"https://consoleapi.xinxintuan.co/file/pic/20200903171807641535.jpg\"/></p>', '2020-09-04 17:55:16', '2020-09-05 20:57:41', 0, 0, 0, 0.00, 11.00, 0, NULL, 0, 69, 25, '', 0, 34, 1, 0,0);
-- INSERT INTO `yx_store_product` VALUES (3, 0, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg', '彩色复印-量大有优惠', '彩色复印', '彩色复印', '', '3', 2.00, 0.00, 1.00, 0.00, '张', 0, 8, 9996, 1, 1, 1, 0, 0, '<p>1111</p>', '2020-09-07 13:14:50', '2020-09-13 22:18:46', 0, 0, 0, 0.00, 1.00, 0, NULL, 0, 0, 43, '', 0, 34, 0, 0,0);
-- INSERT INTO `yx_store_product` VALUES (4, 0, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807625149.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807625149.jpg', '户外写真背胶', '户外写真背胶', '户外写真背胶', '', '3', 2.00, 0.00, 1.00, 0.00, '米', 0, 24, 87, 1, 1, 1, 0, 0, '<p>户外写真背胶</p>', '2020-09-07 13:16:02', NULL, 0, 0, 0, 0.00, 1.00, 0, NULL, 0, 0, 32, '', 0, 34, 0, 0,0);
-- INSERT INTO `yx_store_product` VALUES (5, 0, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807568739.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807568739.jpg', '广告版定制', '广告版定制', '广告版定制', '', '3', 2.00, 0.00, 11.00, 0.00, '米', 0, 16, 2, 1, 1, 1, 1, 1, '<p>广告版定制</p>', '2020-09-07 13:17:49', '2020-09-12 00:14:32', 0, 0, 0, 0.00, 1.00, 0, NULL, 0, 0, 26, '', 0, 34, 0, 0,0);
-- INSERT INTO `yx_store_product` VALUES (6, 0, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807641535.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807641535.jpg', '印刷各种印刷', '印刷各种印刷', '印刷各种印刷', '', '3', 11.00, 0.00, 33.00, 0.00, '张', 1, 35, 9989, 1, 1, 1, 0, 0, '<p>印刷各种印刷</p>', '2020-09-07 13:18:45', '2020-12-20 20:58:32', 0, 0, 0, 1.00, 22.00, 0, NULL, 0, 1, 139, '', 0, 34, 0, 0,0);
-- INSERT INTO `yx_store_product` VALUES (7, 0, 'https://consoleapi.xinxintuan.co/file/pic/20200903171208889668.png', 'https://image.dayouqiantu.cn/5ca04fa9c08ef.jpg,https://image.dayouqiantu.cn/5ca04fa9c08ef.jpg,https://image.dayouqiantu.cn/5ca081af6183f.jpg', '专属测试商品', '', '专属测试商品', '', '3', 100.00, 0.00, 120.00, 0.00, '张', 0, 3, 7, 1, 0, 0, 0, 0, '<p><br/></p><p><img src=\"https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg\"/></p>', '2020-09-12 16:11:05', '2020-09-12 17:03:58', 0, 0, 0, 10.00, 110.00, 0, NULL, 0, 9, 9, '', 0, 34, 0, 0,0);

-- ----------------------------
-- Table structure for yx_store_product_attr
-- 一个商品SPU包含多个属性，组合形成多个SKU
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_product_attr`;
CREATE TABLE `yx_store_product_attr`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `product_id` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  -- 颜色
  `attr_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '属性名',
  -- 白色,红色
  `attr_values` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '属性值',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `store_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品属性表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_product_attr
-- ----------------------------
-- INSERT INTO `yx_store_product_attr` VALUES (32, 1, '规格', '默认');
-- INSERT INTO `yx_store_product_attr` VALUES (55, 4, '规格', '默认');
-- INSERT INTO `yx_store_product_attr` VALUES (57, 2, '纸张', 'A4,A3');
-- INSERT INTO `yx_store_product_attr` VALUES (58, 2, '颜色', '白色,红色');
-- INSERT INTO `yx_store_product_attr` VALUES (59, 0, '规格', '默认');
-- INSERT INTO `yx_store_product_attr` VALUES (62, 7, '规格', '默认');
-- INSERT INTO `yx_store_product_attr` VALUES (69, 5, '规格', '默认');
-- INSERT INTO `yx_store_product_attr` VALUES (70, 3, '规格', '默认');
-- INSERT INTO `yx_store_product_attr` VALUES (71, 6, '规格', '默认');
-- INSERT INTO `yx_store_product_attr` VALUES (76, 8, '纸张', 'A4,A3,A5');
-- INSERT INTO `yx_store_product_attr` VALUES (77, 8, '颜色', '白色,红色');

-- ----------------------------
-- Table structure for yx_store_product_attr_result
-- 每一次调整商品SKU后的结果
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_product_attr_result`;
CREATE TABLE `yx_store_product_attr_result`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `product_id` bigint(20) UNSIGNED NOT NULL COMMENT '商品ID',
  `result` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品属性参数',
  `change_time` datetime(0) NOT NULL COMMENT '上次修改时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `product_id`(`product_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 60 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品属性详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for yx_store_product_attr_value
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_product_attr_value`;
CREATE TABLE `yx_store_product_attr_value`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `product_id` bigint(20) UNSIGNED NOT NULL COMMENT '商品ID',
  `sku` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '商品属性索引值 (attr_value|attr_value[|....])',
  `stock` int(10) UNSIGNED NOT NULL COMMENT '属性对应的库存',
  `sales` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '销量',
  `price` decimal(8, 2) UNSIGNED NOT NULL COMMENT '属性金额',
  `image` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
  `unique` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '唯一值',
  `cost` decimal(8, 2) UNSIGNED NOT NULL COMMENT '成本价',
  `bar_code` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '商品条码',
  `ot_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '原价',
  `weight` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '重量',
  `volume` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '体积',
  `brokerage` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '一级返佣',
  `brokerage_two` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '二级返佣',
  `integral` int(10) UNSIGNED ZEROFILL NULL DEFAULT 0000000000 COMMENT '需要多少积分兑换',
  `limit_buy` int(11) NULL DEFAULT 0 COMMENT '限购数量',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `unique`(`unique`, `sku`) USING BTREE,
  INDEX `store_id`(`product_id`, `sku`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 126 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品属性值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_product_attr_value
-- ----------------------------
-- INSERT INTO `yx_store_product_attr_value` VALUES (44, 1, '默认', 993, 6, 2.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', 'd4ce8cd4bda84d479c11348e060afb8e', 3.00, '00005', 1.00, 0.00, 0.00, 0.00, 0.00, 0.10, 100, 0.20, 97, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (77, 4, '默认', 91, 2, 0.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', '6a1919d9178b48f3a75fe4640e51693e', 0.00, '', 0.00, 0.00, 0.00, 0.00, 0.00, 22.00, 17, 0.00, 0, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (79, 2, 'A4,白色', 987, 4, 10.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', '04e9d586cef8464aaee0a45439f12520', 11.00, '00001', 12.00, 0.00, 0.00, 0.00, 0.00, 11.09, 99, 1.00, 100, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (80, 2, 'A4,红色', 661, 0, 11.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', '3d1ffa92e6064c36b614d3046c268968', 11.00, '00002', 12.00, 0.00, 0.00, 0.00, 0.00, 22.88, 99, 2.00, 100, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (81, 2, 'A3,白色', 995, 0, 12.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', '344f3a0e9d644845ae483c8b9a84a112', 11.00, '00003', 12.00, 0.00, 0.00, 0.00, 0.00, 33.89, 99, 3.00, 100, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (82, 2, 'A3,红色', 665, 0, 13.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', '4a9ca8587855412ea7be9ba53f829052', 11.00, '00004', 12.00, 0.00, 0.00, 0.00, 0.00, 45.66, 100, 4.00, 100, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (83, 0, '默认', 10, 0, 100.00, 'https://consoleapi.xinxintuan.co/file/pic/20200911093912577832.png', 'bbdc071ff67c4892839eac9f62e7eb18', 110.00, '', 120.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0, 0.00, 0, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (86, 7, '默认', 7, 3, 100.00, 'https://consoleapi.xinxintuan.co/file/pic/20200911093912577832.png', '789984b858bb489bb6313107e11fdc15', 110.00, '', 120.00, 0.00, 0.00, 0.00, 0.00, 0.00, 0, 0.00, 0, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (105, 5, '默认', 3, 8, 2.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807641535.jpg', '040fb7ab2a8b4ff3b6c9d1c78ea3e889', 1.00, '1231321', 11.00, 1.00, 1.00, 0.00, 0.00, 29.00, 5, 11.99, 8, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (106, 3, '默认', 9996, 3, 2.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg', '43cdbd1043474b4e97a1cffffa18071c', 1.00, '', 1.00, 0.00, 0.00, 0.00, 0.00, 119.99, 10, 0.00, 0, NULL);
-- INSERT INTO `yx_store_product_attr_value` VALUES (107, 6, '默认', 9989, 0, 11.00, 'https://consoleapi.xinxintuan.co/file/pic/20200903171807641535.jpg', '4dcb457a13924be7b246f72f03ea7931', 22.00, '00002', 33.00, 1.00, 1.00, 0.00, 0.00, 19.90, 99, 22.98, 2000, 0000000000);
-- INSERT INTO `yx_store_product_attr_value` VALUES (120, 8, 'A4,白色', 95, 0, 9.00, 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', '9306b671348a41d2966392578a31e00b', 1.00, '', 12.00, 0.00, 0.00, 1.00, 1.00, 1.00, 9, 1.00, 10, 0000000100);
-- INSERT INTO `yx_store_product_attr_value` VALUES (121, 8, 'A4,红色', 99, 0, 8.00, 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', 'e3fc8496d4a0484a9946d84b03a78eee', 1.00, '', 9.00, 0.00, 0.00, 1.00, 1.00, 2.00, 10, 1.00, 10, 0000000200);
-- INSERT INTO `yx_store_product_attr_value` VALUES (122, 8, 'A3,白色', 88, 0, 7.00, 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', 'a05ff262758548edbef5fda9e6dc26de', 1.00, '', 9.00, 0.00, 0.00, 1.00, 1.00, 0.50, 10, 0.10, 10, 0000000300);
-- INSERT INTO `yx_store_product_attr_value` VALUES (123, 8, 'A3,红色', 88, 0, 6.00, 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', 'dc866a081e2b4a72b2ee9468ad7ba171', 1.00, '', 9.00, 0.00, 0.00, 1.00, 1.00, 1.00, 9, 0.10, 9, 0000000400);
-- INSERT INTO `yx_store_product_attr_value` VALUES (124, 8, 'A5,白色', 65, 0, 5.00, 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', '094a42db903648acb275c868af2bd6ba', 1.00, '', 9.00, 0.00, 0.00, 1.00, 1.00, 2.00, 9, 0.10, 9, 0000000500);
-- INSERT INTO `yx_store_product_attr_value` VALUES (125, 8, 'A5,红色', 66, 0, 4.00, 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', 'c35190bdfb5248ba9a219481adef9b1b', 1.00, '', 9.00, 0.00, 0.00, 1.00, 1.00, 3.00, 9, 0.10, 9, 0000069090);

-- ----------------------------
-- Table structure for yx_store_product_relation
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_product_relation`;
CREATE TABLE `yx_store_product_relation`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) UNSIGNED NOT NULL COMMENT '用户ID',
  `product_id` bigint(20) UNSIGNED NOT NULL COMMENT '商品ID',
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型(收藏(collect）、点赞(like))',
  `category` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '某种类型的商品(普通商品、秒杀商品)',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid`, `product_id`, `type`, `category`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `category`(`category`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品点赞和收藏表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_product_relation
-- ----------------------------

-- ----------------------------
-- Table structure for yx_store_product_reply
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_product_reply`;
CREATE TABLE `yx_store_product_reply`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `uid` bigint(20) NOT NULL COMMENT '用户ID',
  `oid` bigint(20) NOT NULL COMMENT '订单ID',
  `unique` char(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '唯一id',
  `product_id` bigint(20) NOT NULL COMMENT '产品id',
  `reply_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'product' COMMENT '某种商品类型(普通商品、秒杀商品）',
  `product_score` tinyint(1) NOT NULL COMMENT '商品分数',
  `service_score` tinyint(1) NOT NULL COMMENT '服务分数',
  `comment` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '评论内容',
  `pics` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '评论图片',
  `create_time` datetime(0) NOT NULL COMMENT '评论时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `merchant_reply_content` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '管理员回复内容',
  `merchant_reply_time` datetime(0) NULL DEFAULT NULL COMMENT '管理员回复时间',
  `is_del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0未删除1已删除',
  `is_reply` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0未回复1已回复',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `add_time`(`create_time`) USING BTREE,
  INDEX `parent_id`(`reply_type`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `product_score`(`product_score`) USING BTREE,
  INDEX `service_score`(`service_score`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_product_reply
-- ----------------------------
-- INSERT INTO `yx_store_product_reply` VALUES (3, 43, 67, 'c1907f2efeff48168c119ee28ddcac42', 8, 'product', 5, 5, '棒棒哒', '', '2020-09-12 17:45:11', NULL, NULL, NULL, 0, 0);

-- ----------------------------
-- Table structure for yx_store_product_rule
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_product_rule`;
CREATE TABLE `yx_store_product_rule`  (
  `id` bigint(10) NOT NULL AUTO_INCREMENT,
  `rule_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '规格名称',
  `rule_value` json NOT NULL COMMENT '规格值',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商品规则值(规格)表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_product_rule
-- ----------------------------
-- INSERT INTO `yx_store_product_rule` VALUES (10, '图文', '[{\"value\": \"纸张\", \"detail\": [\"A4\", \"A3\"]}, {\"value\": \"颜色\", \"detail\": [\"白色\", \"红色\"]}]', '2020-06-29 13:46:52', '2020-06-29 15:57:28', 0);
-- INSERT INTO `yx_store_product_rule` VALUES (24, '手机', '[{\"value\": \"颜色\", \"detail\": [\"黑色\", \"白色\"]}, {\"value\": \"尺寸\", \"detail\": [\"5寸\", \"7寸\"]}]', '2020-06-29 15:55:51', '2020-07-04 19:50:14', 0);

-- ----------------------------
-- Table structure for yx_store_visit
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_visit`;
CREATE TABLE `yx_store_visit`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `product_id` bigint(20) NULL DEFAULT NULL COMMENT '产品ID',
  `product_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '产品类型',
  `cate_id` int(11) NULL DEFAULT NULL COMMENT '产品分类ID',
  `type` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '产品类型',
  `uid` bigint(20) NULL DEFAULT NULL COMMENT '用户ID',
  `count` int(11) NULL DEFAULT NULL COMMENT '访问次数',
  `content` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '备注描述',
  `add_time` int(11) NULL DEFAULT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '产品浏览分析表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_store_visit
-- ----------------------------

-- ----------------------------
-- Table structure for yx_system_attachment
-- ----------------------------
DROP TABLE IF EXISTS `yx_system_attachment`;
CREATE TABLE `yx_system_attachment`  (
  `att_id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '附件名称',
  `att_dir` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '附件路径',
  `satt_dir` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '压缩图片路径',
  `att_size` char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '附件大小',
  `att_type` char(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '附件类型',
  `pid` int(10) NOT NULL DEFAULT 0 COMMENT '分类ID0编辑器,1产品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图',
  `image_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '图片上传类型 1本地 2七牛云 3OSS 4COS ',
  `module_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '图片上传模块类型 1 后台上传 2 用户生成',
  `uid` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '用户id',
  `invite_code` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '邀请码',
  `create_time` datetime(0) NULL DEFAULT NULL,
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`att_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 121 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '附件管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_system_attachment
-- ----------------------------
INSERT INTO `yx_system_attachment` VALUES (113, '12_55_wechat_bargain_share_wap.jpg', '/home/<USER>/file/qrcode/12_55_wechat_bargain_share_wap.jpg', 'qrcode/12_55_wechat_bargain_share_wap.jpg', '3897220', 'image/jpeg', 0, 1, 1, 0, '', '2020-09-14 13:38:52', NULL, 0);
INSERT INTO `yx_system_attachment` VALUES (114, '12_55_wechat_bargain_user_spread.jpg', '/home/<USER>/file/qrcode/12_55_wechat_bargain_user_spread.jpg', 'qrcode/12_55_wechat_bargain_user_spread.jpg', '43231', 'image/jpeg', 0, 1, 1, 0, '', '2020-09-14 13:38:53', NULL, 0);
INSERT INTO `yx_system_attachment` VALUES (115, '4_59_uniappH5_product_detail_wap.jpg', '/home/<USER>/file/qrcode/4_59_uniappH5_product_detail_wap.jpg', 'qrcode/4_59_uniappH5_product_detail_wap.jpg', '3904929', 'image/jpeg', 0, 1, 1, 0, '', '2020-09-14 13:43:20', NULL, 0);
INSERT INTO `yx_system_attachment` VALUES (116, '4_59_uniappH5_product_user_spread.jpg', '/home/<USER>/file/qrcode/4_59_uniappH5_product_user_spread.jpg', 'qrcode/4_59_uniappH5_product_user_spread.jpg', '113247', 'image/jpeg', 0, 1, 1, 0, '', '2020-09-14 13:43:20', NULL, 0);
INSERT INTO `yx_system_attachment` VALUES (117, '4_75_uniappH5_product_detail_wap.jpg', '/home/<USER>/file/qrcode/4_75_uniappH5_product_detail_wap.jpg', 'qrcode/4_75_uniappH5_product_detail_wap.jpg', '4025717', 'image/jpeg', 0, 1, 1, 0, '', '2020-09-14 17:25:11', NULL, 0);
INSERT INTO `yx_system_attachment` VALUES (118, '4_75_uniappH5_product_user_spread.jpg', '/home/<USER>/file/qrcode/4_75_uniappH5_product_user_spread.jpg', 'qrcode/4_75_uniappH5_product_user_spread.jpg', '113146', 'image/jpeg', 0, 1, 1, 0, '', '2020-09-14 17:25:11', NULL, 0);
INSERT INTO `yx_system_attachment` VALUES (119, '915891458048_yshop.jpg', '/home/<USER>/file/qrcode/915891458048_yshop.jpg', 'qrcode/915891458048_yshop.jpg', '4148444', 'image/jpeg', 0, 1, 1, 0, '', '2020-09-14 22:21:17', NULL, 0);
INSERT INTO `yx_system_attachment` VALUES (120, '915891458048_yshop.jpg', '/home/<USER>/file/qrcode/915891458048_yshop.jpg', 'qrcode/915891458048_yshop.jpg', '4148444', 'image/jpeg', 0, 1, 1, 0, '', '2020-09-14 22:21:17', NULL, 0);


-- ----------------------------
-- Table structure for yx_system_config
-- ----------------------------
DROP TABLE IF EXISTS `yx_system_config`;
CREATE TABLE `yx_system_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `menu_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '字段名称',
  `value` varchar(5000) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '默认值',
  `sort` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否隐藏',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 227 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_system_config
-- ----------------------------
INSERT INTO `yx_system_config` VALUES (162, 'wechat_share_img', '', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (165, 'wechat_avatar', '', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (172, 'wechat_share_synopsis', 'springboot2前后端分离电商系统', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (174, 'api', 'http://你的H5端域名/api/wechat/serve', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (175, 'wechat_share_title', 'yshop电商系统', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (177, 'store_postage', '1', 0, 0);
INSERT INTO `yx_system_config` VALUES (179, 'integral_ratio', '1', 0, 0);
INSERT INTO `yx_system_config` VALUES (180, 'store_brokerage_ratio', '70', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (182, 'user_extract_min_price', '1', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (183, 'store_brokerage_two', '30', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (184, 'site_url', 'http://127.0.0.1:8080', 0, 0);
INSERT INTO `yx_system_config` VALUES (185, 'api_url', 'http://127.0.0.1:8008', 0, 0);
INSERT INTO `yx_system_config` VALUES (186, 'order_cancel_job_time', '20', 0, 0);
INSERT INTO `yx_system_config` VALUES (187, 'wechat_appid', 'wxc061dee8806ff712', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (188, 'wechat_appsecret', '1111', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (189, 'wechat_encodingaeskey', 'yYuBUkC8BXImCXyu7O6hkzLj4TC5nxsWPfL4CQAZPNY', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (190, 'wechat_token', 'yshop', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (191, 'wxpay_mchId', '1111', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (192, 'wxpay_appId', '111111', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (193, 'wxpay_mchKey', '1111', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (194, 'wxapp_appId', '121221', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (195, 'wxapp_secret', '121212', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (196, 'wxpay_keyPath', 'http://localhost:8000/file/pic/list_30-20200110053337209.png', 0, 0);
INSERT INTO `yx_system_config` VALUES (198, 'store_brokerage_open', '1', 0, 0);
INSERT INTO `yx_system_config` VALUES (199, 'integral_full', '0', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (200, 'integral_max', '0', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (201, 'store_user_min_recharge', '1', 0, 0);
INSERT INTO `yx_system_config` VALUES (203, 'store_self_mention', '1', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (204, 'tengxun_map_key', '11111111111', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (205, 'wechat_id', '', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (206, 'wechat_type', '', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (207, 'wechat_encode', '', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (208, 'wechat_sourceid', '', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (209, 'wechat_name', '', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (210, 'wechat_qrcode', '', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (211, 'wx_native_app_appId', 'wx7c84ede33062d1e4', NULL, NULL);
INSERT INTO `yx_system_config` VALUES (212, 'file_store_mode', '1', 0, 0);
INSERT INTO `yx_system_config` VALUES (213, 'sms_access_key', 'yshopyshop', 0, 0);
INSERT INTO `yx_system_config` VALUES (214, 'sms_enable', '2', 0, 0);
INSERT INTO `yx_system_config` VALUES (215, 'sms_access_secret', 'yshop', 0, 0);
INSERT INTO `yx_system_config` VALUES (216, 'sms_region', 'cn-hangzhou', 0, 0);
INSERT INTO `yx_system_config` VALUES (217, 'sms_templateId', '111111111', 0, 0);
INSERT INTO `yx_system_config` VALUES (218, 'sms_sign', 'yshop', 0, 0);
INSERT INTO `yx_system_config` VALUES (219, 'exp_enable', '1', 0, 0);
INSERT INTO `yx_system_config` VALUES (220, 'exp_appId', '1607734', 0, 0);
INSERT INTO `yx_system_config` VALUES (221, 'exp_appKey', '33333333333333', 0, 0);
INSERT INTO `yx_system_config` VALUES (222, 'admin_api_url', 'http://127.0.0.1:8001', 0, 0);
INSERT INTO `yx_system_config` VALUES (223, 'imageArr', '[\"\"]', 0, 0);
INSERT INTO `yx_system_config` VALUES (224, 'wechat_ma_encodingaeskey', '', 0, 0);
INSERT INTO `yx_system_config` VALUES (225, 'wxapi', 'http://你的H5api端域名/api/wxapp/serve', 0, 0);
INSERT INTO `yx_system_config` VALUES (226, 'wechat_ma_token', '', 0, 0);

-- ----------------------------
-- Table structure for yx_system_group_data
-- ----------------------------
DROP TABLE IF EXISTS `yx_system_group_data`;
CREATE TABLE `yx_system_group_data`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '组合数据详情ID',
  `group_name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '对应的数据名称',
  `value` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '数据组对应的数据值（json数据）',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加数据时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `sort` int(11) NULL DEFAULT 0 COMMENT '数据排序',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态（1：开启；2：关闭；）',
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 227 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '组合数据详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_system_group_data
-- ----------------------------
INSERT INTO `yx_system_group_data` VALUES (177, 'yshop_home_banner', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5c9f05aee5059.jpg\"],\"name\":\"banner2\",\"id\":177,\"pic\":\"https://image.dayouqiantu.cn/5c9f05aee5059.jpg\",\"sort\":1,\"url\":\"wwww\",\"status\":0}', '2020-06-25 18:54:35', '2020-09-15 07:54:44', 1, 0, 0);
INSERT INTO `yx_system_group_data` VALUES (180, 'yshop_home_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/all.png\"],\"uniapp_url\":\"/pages/shop/GoodsList/index\",\"name\":\"全部商品\",\"id\":180,\"pic\":\"https://image.dayouqiantu.cn/all.png\",\"sort\":9,\"url\":\"/goods_list\",\"wxapp_url\":\"/pages/shop/GoodsClass/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 9, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (182, 'yshop_home_roll_news', '{\"uniapp_url\":\"/pages/shop/news/NewsList/index\",\"id\":182,\"pic\":\"https://i.loli.net/2019/10/18/DqOUgNf7wjuFpPT.png\",\"sort\":2,\"title\":\"分销、拼团、商户功能上线啦！\",\"url\":\"/news_list\",\"info\":\"yshop3.0\",\"wxapp_url\":\"/pages/shop/news/NewsList/main\",\"status\":1}', '2020-06-25 18:54:35', '2020-06-27 15:47:38', 2, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (183, 'yshop_hot_search', '{\"id\":183,\"title\":\"照片\"}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (184, 'yshop_hot_search', '{\"id\":184,\"title\":\"springboot\"}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (187, 'yshop_home_roll_news', '{\"uniapp_url\":\"/pages/shop/news/NewsList/index\",\"id\":187,\"sort\":1,\"url\":\"/news_list\",\"info\":\"yshop基于springboot2+Mybatisplus+jwt商城系统\",\"wxapp_url\":\"/pages/shop/news/NewsList/main\",\"status\":1}', '2020-06-25 18:54:35', '2020-06-27 15:47:22', 4, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (188, 'yshop_hot_search', '{\"id\":188,\"title\":\"打印\"}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (189, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5dec896eeb25a.png\"],\"uniapp_url\":\"/pages/adminUser/UserVip/index\",\"name\":\"会员中心\",\"id\":189,\"pic\":\"https://image.dayouqiantu.cn/5dec896eeb25a.png\",\"sort\":9,\"url\":\"/adminUser/vip\",\"wxapp_url\":\"/pages/adminUser/UserVip/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 9, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (190, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5db428984d64d.png\"],\"uniapp_url\":\"/pages/adminUser/coupon/UserCoupon/index\",\"name\":\"优惠券\",\"id\":190,\"pic\":\"https://image.dayouqiantu.cn/5db428984d64d.png\",\"sort\":8,\"url\":\"/adminUser/get_coupon\",\"wxapp_url\":\"/pages/adminUser/coupon/UserCoupon/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 8, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (191, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5db428a8d3ab0.png\"],\"uniapp_url\":\"/pages/shop/GoodsCollection/index\",\"name\":\"收藏商品\",\"id\":191,\"pic\":\"https://image.dayouqiantu.cn/5db428a8d3ab0.png\",\"sort\":7,\"url\":\"/collection\",\"wxapp_url\":\"/pages/shop/GoodsCollection/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 7, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (192, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5db428bd61b73.png\"],\"uniapp_url\":\"/pages/adminUser/address/AddressManagement/index\",\"name\":\"地址管理\",\"id\":192,\"pic\":\"https://image.dayouqiantu.cn/5db428bd61b73.png\",\"sort\":6,\"url\":\"/adminUser/add_manage\",\"wxapp_url\":\"/pages/adminUser/address/AddressManagement/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 6, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (193, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5db428e28dd48.png\"],\"uniapp_url\":\"/pages/adminUser/promotion/UserPromotion/index\",\"name\":\"我的推广\",\"id\":193,\"pic\":\"https://image.dayouqiantu.cn/5db428e28dd48.png\",\"sort\":5,\"url\":\"/adminUser/user_promotion\",\"wxapp_url\":\"/pages/adminUser/promotion/UserPromotion/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 5, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (194, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5db42a4208c55.png\"],\"uniapp_url\":\"/pages/adminUser/UserAccount/index\",\"name\":\"我的余额\",\"id\":194,\"pic\":\"https://image.dayouqiantu.cn/5db42a4208c55.png\",\"sort\":4,\"url\":\"/adminUser/account\",\"wxapp_url\":\"/pages/adminUser/UserAccount/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 4, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (195, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5db428f410462.png\"],\"uniapp_url\":\"/pages/adminUser/signIn/Integral/index\",\"name\":\"我的积分\",\"id\":195,\"pic\":\"https://image.dayouqiantu.cn/5db428f410462.png\",\"sort\":3,\"url\":\"/adminUser/integral\",\"wxapp_url\":\"/pages/adminUser/signIn/Integral/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 3, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (196, 'yshop_home_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/xw.png\"],\"uniapp_url\":\"/pages/shop/news/NewsList/index\",\"name\":\"图文资讯\",\"id\":196,\"pic\":\"https://image.dayouqiantu.cn/xw.png\",\"sort\":8,\"url\":\"/news_list\",\"wxapp_url\":\"/pages/shop/news/NewsList/main\",\"status\":1}', '2020-06-25 18:54:35', '2020-06-26 18:26:24', 8, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (197, 'yshop_home_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/colle.png\"],\"uniapp_url\":\"/pages/shop/GoodsCollection/index\",\"name\":\"我的收藏\",\"id\":197,\"pic\":\"https://image.dayouqiantu.cn/colle.png\",\"sort\":7,\"url\":\"/collection\",\"wxapp_url\":\"/pages/shop/GoodsCollection/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 7, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (199, 'yshop_home_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/cou.png\"],\"uniapp_url\":\"/pages/adminUser/coupon/GetCoupon/index\",\"name\":\"优惠券\",\"id\":199,\"pic\":\"https://image.dayouqiantu.cn/cou.png\",\"sort\":6,\"url\":\"/adminUser/get_coupon\",\"wxapp_url\":\"/pages/adminUser/coupon/GetCoupon/main\",\"status\":1}', '2020-06-25 18:54:35', '2020-06-26 18:26:36', 6, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (200, 'yshop_home_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/62ac09d2914d36c65b9b59d2147d809a.png\"],\"uniapp_url\":\"/pages/activity/GoodsGroup/index\",\"name\":\"拼团专区\",\"id\":200,\"pic\":\"https://image.dayouqiantu.cn/62ac09d2914d36c65b9b59d2147d809a.png\",\"sort\":5,\"url\":\"/activity/group\",\"wxapp_url\":\"/pages/activity/GoodsGroup/main\",\"status\":1}', '2020-06-25 18:54:35', '2020-06-26 18:26:45', 5, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (201, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5ddb7a37d58d9.png\"],\"uniapp_url\":\"/pages/orderAdmin/OrderIndex/index\",\"name\":\"商户管理\",\"id\":201,\"pic\":\"https://image.dayouqiantu.cn/5ddb7a37d58d9.png\",\"sort\":2,\"url\":\"/customer/index\",\"wxapp_url\":\"/pages/orderAdmin/OrderIndex/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 2, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (202, 'yshop_sign_day_num', '{\"sign_num\":\"10\",\"id\":205,\"day\":\"第一天\"}', '2020-06-25 18:54:35', NULL, 9, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (203, 'yshop_sign_day_num', '{\"sign_num\":\"20\",\"id\":\"\",\"day\":\"第二天\"}', '2020-06-25 18:54:35', NULL, 8, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (204, 'yshop_sign_day_num', '{\"sign_num\":\"30\",\"id\":\"\",\"day\":\"第三天\"}', '2020-06-25 18:54:35', NULL, 7, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (205, 'yshop_sign_day_num', '{\"addTime\":\"\",\"sign_num\":\"40\",\"id\":\"\",\"sort\":\"\",\"value\":\"\",\"day\":\"第四天\",\"status\":\"\"}', '2020-06-25 18:54:35', NULL, 6, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (206, 'yshop_sign_day_num', '{\"addTime\":\"\",\"sign_num\":\"50\",\"id\":\"\",\"sort\":\"\",\"value\":\"\",\"day\":\"第五天\",\"status\":\"\"}', '2020-06-25 18:54:35', NULL, 5, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (207, 'yshop_sign_day_num', '{\"addTime\":\"\",\"sign_num\":\"60\",\"id\":\"\",\"sort\":\"\",\"value\":\"\",\"day\":\"第六天\",\"status\":\"\"}', '2020-06-25 18:54:35', NULL, 4, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (208, 'yshop_sign_day_num', '{\"addTime\":\"\",\"sign_num\":\"100\",\"id\":\"\",\"sort\":\"\",\"value\":\"\",\"day\":\"奖励\",\"status\":\"\"}', '2020-06-25 18:54:35', NULL, 3, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (209, 'yshop_home_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/29ea4acebbf99e7eaf6f85af2b6d79ae.png\"],\"uniapp_url\":\"/pages/adminUser/signIn/Sign/index\",\"name\":\"积分签到\",\"id\":209,\"pic\":\"https://image.dayouqiantu.cn/29ea4acebbf99e7eaf6f85af2b6d79ae.png\",\"sort\":4,\"url\":\"/adminUser/sign\",\"wxapp_url\":\"/pages/adminUser/signIn/Sign/main\",\"status\":1}', '2020-06-25 18:54:35', '2020-06-26 18:26:56', 4, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (210, 'yshop_seckill_time', '{\"continued\":2,\"id\":\"\",\"time\":5}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (211, 'yshop_seckill_time', '{\"addTime\":\"\",\"continued\":\"3\",\"id\":\"\",\"sort\":\"\",\"time\":\"7\",\"value\":\"\",\"status\":\"\"}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (212, 'yshop_seckill_time', '{\"addTime\":\"\",\"continued\":\"2\",\"id\":\"\",\"sort\":\"\",\"time\":\"10\",\"value\":\"\",\"status\":\"\"}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (213, 'yshop_seckill_time', '{\"addTime\":\"\",\"continued\":\"3\",\"id\":\"\",\"sort\":\"\",\"time\":\"12\",\"value\":\"\",\"status\":\"\"}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (214, 'yshop_seckill_time', '{\"continued\":\"10\",\"id\":214,\"time\":\"15\"}', '2020-06-25 18:54:35', '2020-09-05 17:16:06', 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (215, 'yshop_seckill_time', '{\"continued\":\"2\",\"id\":223,\"time\":\"19\"}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (216, 'yshop_home_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/b0344c148141b50d68db9722708ea49e.png\"],\"uniapp_url\":\"/pages/activity/GoodsSeckill/index\",\"name\":\"秒杀专区\",\"id\":216,\"pic\":\"https://image.dayouqiantu.cn/b0344c148141b50d68db9722708ea49e.png\",\"sort\":3,\"url\":\"/activity/goods_seckill\",\"wxapp_url\":\"/pages/activity/GoodsSeckill/main\",\"status\":1}', '2020-06-25 18:54:35', '2020-06-26 18:27:06', 3, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (217, 'yshop_home_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/barService.png\"],\"uniapp_url\":\"/pages/activity/GoodsBargain/index\",\"name\":\"砍价专区\",\"id\":217,\"pic\":\"https://image.dayouqiantu.cn/barService.png\",\"sort\":2,\"url\":\"/activity/bargain\",\"wxapp_url\":\"/pages/activity/GoodsBargain/main\",\"status\":1}', '2020-06-25 18:54:35', '2020-06-26 18:27:16', 2, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (218, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5dfd7b748e053.png\"],\"uniapp_url\":\"/pages/activity/BargainRecord/index\",\"name\":\"砍价记录\",\"id\":218,\"pic\":\"https://image.dayouqiantu.cn/5dfd7b748e053.png\",\"sort\":1,\"url\":\"/activity/bargain/record\",\"wxapp_url\":\"/pages/activity/BargainRecord/main\",\"status\":1}', '2020-06-25 18:54:35', NULL, 1, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (219, 'yshop_home_banner', '{\"name\":\"222\",\"id\":\"\",\"pic\":\"https://image.dayouqiantu.cn/5c9f117f624ee.jpg\",\"sort\":\"\",\"url\":\"/\",\"status\":\"\"}', '2020-06-25 18:54:35', NULL, 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (221, 'yshop_my_menus', '{\"imageArr\":[\"https://image.dayouqiantu.cn/5e60da498cfdd.png\"],\"uniapp_url\":\"/pages/orderAdmin/OrderCancellation/index\",\"name\":\"订单核销\",\"id\":221,\"pic\":\"https://image.dayouqiantu.cn/5e60da498cfdd.png\",\"sort\":0,\"url\":\"/order/order_cancellation\",\"wxapp_url\":\"\",\"status\":1}', '2020-06-25 18:54:35', '2020-09-15 15:46:36', 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (222, 'yshop_recharge_price_ways', '{\"give_price\":\"10\",\"price\":\"100\",\"id\":\"\",\"sort\":0,\"status\":1}', '2020-06-25 18:54:35', NULL, 1, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (223, 'yshop_recharge_price_ways', '{\"give_price\":\"1000\",\"price\":1,\"id\":223,\"sort\":0,\"status\":1}', '2020-06-25 18:54:35', '2020-09-12 18:34:21', 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (224, 'yshop_home_banner', '{\"imageArr\":[\"https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg\"],\"uniapp_url\":\"3\",\"name\":\"3\",\"id\":224,\"pic\":\"https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg\",\"sort\":0,\"url\":\"3\",\"wxapp_url\":\"\",\"status\":1}', '2020-09-03 17:13:59', '2020-09-15 07:54:11', 0, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (225, 'yshop_my_menus', '{\"imageArr\":[\"https://consoleapi.xinxintuan.co/file/pic/20200911093912577832.png\"],\"uniapp_url\":\"/pages/shop/GoodsFoot/index\",\"name\":\"我的足迹\",\"id\":225,\"pic\":\"https://consoleapi.xinxintuan.co/file/pic/20200911093912577832.png\",\"sort\":10,\"url\":\"\",\"wxapp_url\":\"\",\"status\":1}', '2020-09-11 09:39:21', '2020-09-11 09:40:49', 10, 1, 0);
INSERT INTO `yx_system_group_data` VALUES (226, 'yshop_home_banner', '{\"imageArr\":[\"https://consoleapi.xinxintuan.co/file/pic/20200915074139926178.jpg\"],\"uniapp_url\":\"\",\"name\":\"sdaas\",\"id\":\"\",\"pic\":\"https://consoleapi.xinxintuan.co/file/pic/20200915074139926178.jpg\",\"sort\":0,\"url\":\"\",\"wxapp_url\":\"\",\"status\":1}', '2020-09-15 07:41:46', NULL, 0, 1, 1);

-- ----------------------------
-- Table structure for yx_system_store
-- ----------------------------
DROP TABLE IF EXISTS `yx_system_store`;
CREATE TABLE `yx_system_store`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '门店名称',
  `introduction` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '简介',
  `phone` char(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '手机号码',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '省市区',
  `detailed_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '门店logo',
  `latitude` char(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `longitude` char(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `valid_time` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '核销有效日期',
  `day_time` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '每日营业开关时间',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除',
  `day_time_end` datetime(0) NULL DEFAULT NULL,
  `day_time_start` datetime(0) NULL DEFAULT NULL,
  `valid_time_end` datetime(0) NULL DEFAULT NULL,
  `valid_time_start` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `phone`(`phone`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店自提' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_system_store
-- ----------------------------
INSERT INTO `yx_system_store` VALUES (3, 'yshop店铺', 'springboot商城', '15136175234', '河南省漯河市', '', 'https://image.dayouqiantu.cn/noMeal_tt.png', '33.54528', '113.9202', '2020-03-09 - 2020-04-30', '11:33:49 - 19:33:49', '2020-06-25 18:54:35', NULL, 0, 0, '2020-03-04 19:33:49', '2020-03-04 11:33:49', '2020-04-30 00:00:00', '2020-03-09 00:00:00');
INSERT INTO `yx_system_store` VALUES (4, '信阳门店', '信阳门店', '15136275234', '河南省信阳市', '', 'https://image.dayouqiantu.cn/noMeal_tt.png', '32.11683', '114.05857', '2020-03-21 - 2020-03-31', '17:11:13 - 20:11:13', '2020-06-25 18:54:35', NULL, 1, 0, '2020-03-21 20:11:13', '2020-03-21 17:11:13', '2020-03-31 00:00:00', '2020-03-21 00:00:00');
INSERT INTO `yx_system_store` VALUES (5, '郑州门店', '郑州门店', '15136175246', '河南省郑州', '', 'https://image.dayouqiantu.cn/noMeal_tt.png', '34.72468', '113.6401', '2020-03-21 - 2020-03-31', '17:11:13 - 20:11:15', '2020-06-25 18:54:35', '2020-06-26 10:51:21', 1, 1, '2020-03-21 20:11:15', '2020-03-21 17:11:13', '2020-03-31 00:00:00', '2020-03-21 00:00:00');

-- ----------------------------
-- Table structure for yx_system_store_staff
-- ----------------------------
DROP TABLE IF EXISTS `yx_system_store_staff`;
CREATE TABLE `yx_system_store_staff`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) UNSIGNED NOT NULL COMMENT '微信用户id',
  `nickname` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `avatar` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '店员头像',
  `store_id` int(11) NOT NULL COMMENT '门店id',
  `store_name` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `staff_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '店员名称',
  `phone` char(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `verify_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '核销开关',
  `status` tinyint(2) NULL DEFAULT 1 COMMENT '状态',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '门店店员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_system_store_staff
-- ----------------------------
INSERT INTO `yx_system_store_staff` VALUES (2, 12, '会敲代码的喵2', 'https://image.dayouqiantu.cn/5dc2c7f3a104c.png', 4, '信阳门店', 'tttt', '15136175423', 1, 1, '2020-06-25 18:54:35', NULL, 1);
INSERT INTO `yx_system_store_staff` VALUES (3, 11, '会敲代码的喵88', 'https://image.dayouqiantu.cn/5dc2c7f3a104c.png', 5, '郑州门店', 'tttt', '16136175234', 1, 1, '2020-06-25 18:54:35', NULL, 1);
INSERT INTO `yx_system_store_staff` VALUES (4, 19, '15136175528', 'https://image.dayouqiantu.cn/5dc2c7f3a104c.png', 5, '郑州门店', '111', '15136175246', 1, 1, '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_store_staff` VALUES (5, 20, '15136171112', 'https://image.dayouqiantu.cn/5e79f6cfd33b6.png', 4, '信阳门店', '444', '44444', 1, 1, NULL, '2020-06-26 10:56:09', 0);
INSERT INTO `yx_system_store_staff` VALUES (6, 21, '15136175234', 'https://image.dayouqiantu.cn/5e79f6cfd33b6.png', 4, '信阳门店', '5555', '555555', 1, 1, '2020-06-26 10:53:46', NULL, 0);

-- ----------------------------
-- Table structure for yx_system_user_level
-- ----------------------------
DROP TABLE IF EXISTS `yx_system_user_level`;
CREATE TABLE `yx_system_user_level`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mer_id` int(11) NOT NULL DEFAULT 0 COMMENT '商户id',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员名称',
  `money` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '购买金额',
  `valid_date` int(11) NOT NULL DEFAULT 0 COMMENT '有效时间',
  `is_forever` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为永久会员',
  `is_pay` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否购买,1=购买,0=不购买',
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示 1=显示,0=隐藏',
  `grade` int(11) NOT NULL DEFAULT 0 COMMENT '会员等级',
  `discount` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '享受折扣',
  `image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员卡背景',
  `icon` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员图标',
  `explain` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '说明',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除.1=删除,0=未删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '设置用户等级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_system_user_level
-- ----------------------------
INSERT INTO `yx_system_user_level` VALUES (1, 0, '普通会员', 20.00, 0, 1, 0, 1, 1, 99.00, 'http://pic.dayouqiantu.cn/5c9ccca8cd632.jpg', 'http://pic.dayouqiantu.cn/5c9ccca8bc1e0.png', '普通会员', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_level` VALUES (2, 0, '青铜会员', 0.00, 0, 1, 0, 1, 2, 98.00, 'http://pic.dayouqiantu.cn/5c9ccca904016.jpg', 'http://pic.dayouqiantu.cn/5c9ccca8f0a30.png', '青铜会员', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_level` VALUES (3, 0, '黄铜会员', 0.00, 0, 1, 0, 1, 3, 95.00, 'http://pic.dayouqiantu.cn/5c9ccca8b27f1.jpg', 'http://pic.dayouqiantu.cn/5c9ccca8e9365.png', '黄铜会员', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_level` VALUES (4, 0, '白银会员', 0.00, 0, 1, 0, 1, 4, 94.00, 'http://pic.dayouqiantu.cn/5c9ccca8d6ae1.jpg', 'http://pic.dayouqiantu.cn/5c9ccca8a27f0.png', '白银会员', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_level` VALUES (5, 0, '黄金会员', 0.00, 0, 1, 0, 1, 5, 90.00, 'http://pic.dayouqiantu.cn/5c9ccca8b27f1.jpg', 'http://pic.dayouqiantu.cn/5c9ccca8aa5b9.png', '黄金会员', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_level` VALUES (6, 0, '钻石会员', 0.00, 0, 1, 0, 1, 6, 88.00, 'http://localhost:8000/file/pic/钻石-20200328094531898.jpg', 'http://pic.dayouqiantu.cn/5c9ccca90d2d3.png', '钻石会员', '2020-06-25 18:54:35', NULL, 1);

-- ----------------------------
-- Table structure for yx_system_user_task
-- ----------------------------
DROP TABLE IF EXISTS `yx_system_user_task`;
CREATE TABLE `yx_system_user_task`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `real_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '配置原名',
  `task_type` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '任务类型',
  `number` int(11) NOT NULL DEFAULT 0 COMMENT '限定数',
  `level_id` int(11) NOT NULL DEFAULT 0 COMMENT '等级id',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示',
  `is_must` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否务必达成任务,1务必达成,0=满足其一',
  `illustrate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '任务说明',
  `create_time` datetime(0) NOT NULL COMMENT '新增时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '等级任务设置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_system_user_task
-- ----------------------------
INSERT INTO `yx_system_user_task` VALUES (1, '满足积分20分', '积分数', 'SatisfactionIntegral', 20, 1, 0, 1, 1, '', '2020-06-25 18:54:35', '2020-06-25 18:54:35', 0);
INSERT INTO `yx_system_user_task` VALUES (2, '消费满100元', '消费金额', 'ConsumptionAmount', 100, 1, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (3, '满足积分200分', '积分数', 'SatisfactionIntegral', 200, 2, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (4, '累计签到20天', '累计签到', 'CumulativeAttendance', 20, 2, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (5, '满足积分500分', '积分数', 'SatisfactionIntegral', 500, 3, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (6, '累计签到30天', '累计签到', 'CumulativeAttendance', 30, 3, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (7, '满足积分1000分', '积分数', 'SatisfactionIntegral', 1000, 4, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (8, '累计签到10天', '累计签到', 'CumulativeAttendance', 10, 4, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (9, '满足积分1200分', '积分数', 'SatisfactionIntegral', 1200, 5, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (10, '累计签到60天', '累计签到', 'CumulativeAttendance', 60, 5, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (11, '消费满10000元', '消费次数', 'ConsumptionAmount', 10000, 5, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (12, '满足积分2000分', '积分数', 'SatisfactionIntegral', 2000, 6, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (13, '消费满10000元', '消费次数', 'ConsumptionAmount', 10000, 6, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (14, '累计签到100天', '累计签到', 'CumulativeAttendance', 100, 6, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (15, '消费满1000元', '消费金额', 'ConsumptionAmount', 1000, 4, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (16, '累计签到2天', '累计签到', 'CumulativeAttendance', 2, 1, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (17, '消费满100元', '消费次数', 'ConsumptionAmount', 100, 2, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);
INSERT INTO `yx_system_user_task` VALUES (18, '消费满1000元', '消费金额', 'ConsumptionAmount', 1000, 3, 0, 1, 1, '', '2020-06-25 18:54:35', NULL, 0);

-- ----------------------------
-- Table structure for yx_user
-- ----------------------------
DROP TABLE IF EXISTS `yx_user`;
CREATE TABLE `yx_user`  (
  `uid` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户id',
  `username` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户账户(跟accout一样)',
  `password` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户密码（跟pwd）',
  `real_name` varchar(25) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '真实姓名',
  `birthday` int(11) NULL DEFAULT 0 COMMENT '生日',
  `card_id` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '身份证号码',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '用户备注',
  `partner_id` int(11) NULL DEFAULT 0 COMMENT '合伙人id',
  `group_id` int(11) NULL DEFAULT 0 COMMENT '用户分组id',
  `nickname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户昵称',
  `avatar` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户头像',
  `phone` char(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '手机号码',
  `add_ip` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '添加ip',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '最后一次登录时间',
  `last_ip` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '最后一次登录ip',
  `now_money` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '用户余额',
  `brokerage_price` decimal(8, 2) NOT NULL DEFAULT 0.00 COMMENT '佣金金额',
  `integral` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '用户剩余积分',
  `sign_num` int(11) NOT NULL DEFAULT 0 COMMENT '连续签到天数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '1为正常，0为禁止',
  `level` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '等级',
  `spread_uid` bigint(20) UNSIGNED NULL DEFAULT 0 COMMENT '推广元id',
  `spread_time` datetime(0) NULL DEFAULT NULL COMMENT '推广员关联时间',
  `user_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '用户类型',
  `is_promoter` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为推广员',
  `pay_count` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '用户购买次数',
  `spread_count` int(11) NULL DEFAULT 0 COMMENT '下级人数',
  `addres` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详细地址',
  `adminid` int(11) UNSIGNED NULL DEFAULT 0 COMMENT '管理员编号 ',
  `login_type` varchar(36) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '用户登录类型，h5,wechat,routine',
  `wx_profile` json NULL COMMENT '微信用户json信息',
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`uid`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE,
  INDEX `spreaduid`(`spread_uid`) USING BTREE,
  INDEX `level`(`level`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `is_promoter`(`is_promoter`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user
-- ----------------------------

-- ----------------------------
-- Table structure for yx_user_address
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_address`;
CREATE TABLE `yx_user_address`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户地址id',
  `uid` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `real_name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `phone` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '收货人电话',
  `province` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '收货人所在省',
  `city` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '收货人所在市',
  `city_id` int(11) NULL DEFAULT NULL,
  `district` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '收货人所在区',
  `detail` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '收货人详细地址',
  `post_code` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '邮编',
  `longitude` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '经度',
  `latitude` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '纬度',
  `is_default` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否默认',
  `is_del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `is_default`(`is_default`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for yx_user_feedback 
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_feedback`;
CREATE TABLE `yx_user_feedback`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `uid` bigint(20) UNSIGNED NOT NULL COMMENT '用户id',
  `content` LONGTEXT CHARACTER SET utf8 COLLATE utf8_general_ci  NULL  COMMENT '意见反馈',
  `is_del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '意见反馈' ROW_FORMAT = DYNAMIC;


-- ----------------------------
-- Records of yx_user_address
-- ----------------------------

-- ----------------------------
-- Table structure for yx_user_bill
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_bill`;
CREATE TABLE `yx_user_bill`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户账单id',
  `uid` bigint(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户uid',
  `link_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '关联id',
  `pm` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = 支出 1 = 获得',
  `title` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '账单标题',
  `category` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '明细种类',
  `type` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '明细类型',
  `number` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '明细数字',
  `balance` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '剩余',
  `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '0 = 带确定 1 = 有效 -1 = 无效',
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `openid`(`uid`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `add_time`(`create_time`) USING BTREE,
  INDEX `pm`(`pm`) USING BTREE,
  INDEX `type`(`category`, `type`, `link_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户账单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user_bill
-- ----------------------------

-- ----------------------------
-- Table structure for yx_user_enter
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_enter`;
CREATE TABLE `yx_user_enter`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '商户申请ID',
  `uid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `province` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户所在省',
  `city` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户所在市',
  `district` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户所在区',
  `address` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户详细地址',
  `merchant_name` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户名称',
  `link_user` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '',
  `link_tel` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户电话',
  `charter` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '商户证书',
  `add_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '添加时间',
  `apply_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '审核时间',
  `success_time` int(11) NOT NULL COMMENT '通过时间',
  `fail_message` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '未通过原因',
  `fail_time` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '未通过时间',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '-1 审核未通过 0未审核 1审核通过',
  `is_lock` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '0 = 开启 1= 关闭',
  `is_del` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否删除',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uid`(`uid`) USING BTREE,
  INDEX `province`(`province`, `city`, `district`) USING BTREE,
  INDEX `is_lock`(`is_lock`) USING BTREE,
  INDEX `is_del`(`is_del`) USING BTREE,
  INDEX `status`(`status`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '商户申请表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user_enter
-- ----------------------------

-- ----------------------------
-- Table structure for yx_user_extract
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_extract`;
CREATE TABLE `yx_user_extract`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) UNSIGNED NULL DEFAULT NULL,
  `real_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '名称',
  `extract_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT 'bank' COMMENT 'bank = 银行卡 alipay = 支付宝wx=微信',
  `bank_code` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '0' COMMENT '银行卡',
  `bank_address` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '开户地址',
  `alipay_code` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '支付宝账号',
  `extract_price` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '提现金额',
  `mark` varchar(512) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `balance` decimal(8, 2) UNSIGNED NULL DEFAULT 0.00,
  `fail_msg` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '无效原因',
  `fail_time` datetime(0) NULL DEFAULT NULL,
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `status` tinyint(2) NULL DEFAULT 0 COMMENT '-1 未通过 0 审核中 1 已提现',
  `wechat` varchar(15) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '微信号',
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `extract_type`(`extract_type`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `add_time`(`create_time`) USING BTREE,
  INDEX `openid`(`uid`) USING BTREE,
  INDEX `fail_time`(`fail_time`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user_extract
-- ----------------------------

-- ----------------------------
-- Table structure for yx_user_group
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_group`;
CREATE TABLE `yx_user_group`  (
  `id` smallint(5) UNSIGNED NOT NULL AUTO_INCREMENT,
  `group_name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '用户分组名称',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user_group
-- ----------------------------

-- ----------------------------
-- Table structure for yx_user_level
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_level`;
CREATE TABLE `yx_user_level`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `level_id` int(11) NOT NULL DEFAULT 0 COMMENT '等级vip',
  `grade` int(11) NOT NULL DEFAULT 0 COMMENT '会员等级',
  `valid_time` int(11) NOT NULL DEFAULT 0 COMMENT '过期时间',
  `is_forever` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否永久',
  `mer_id` int(11) NOT NULL DEFAULT 0 COMMENT '商户id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '0:禁止,1:正常',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `remind` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已通知',
  `is_del` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否删除,0=未删除,1=删除',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `discount` int(11) NOT NULL DEFAULT 0 COMMENT '享受折扣',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户等级记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user_level
-- ----------------------------

-- ----------------------------
-- Table structure for yx_user_recharge
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_recharge`;
CREATE TABLE `yx_user_recharge`  (
  `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NULL DEFAULT NULL COMMENT '充值用户UID',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `order_id` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '订单号',
  `price` decimal(8, 2) NULL DEFAULT NULL COMMENT '充值金额',
  `give_price` decimal(8, 2) NULL DEFAULT 0.00 COMMENT '购买赠送金额',
  `recharge_type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '充值类型',
  `paid` tinyint(1) NULL DEFAULT NULL COMMENT '是否充值',
  `pay_time` datetime(0) NULL DEFAULT NULL COMMENT '充值支付时间',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '充值时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `refund_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '退款金额',
  `is_del` tinyint(4) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `recharge_type`(`recharge_type`) USING BTREE,
  INDEX `paid`(`paid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户充值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user_recharge
-- ----------------------------

-- ----------------------------
-- Table structure for yx_user_sign
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_sign`;
CREATE TABLE `yx_user_sign`  (
  `int` bigint(20) NOT NULL AUTO_INCREMENT,
  `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户uid',
  `title` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '签到说明',
  `number` int(11) NOT NULL DEFAULT 0 COMMENT '获得积分',
  `balance` int(11) NOT NULL DEFAULT 0 COMMENT '剩余积分',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`int`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '签到记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user_sign
-- ----------------------------
INSERT INTO `yx_user_sign` VALUES (17, 43, '签到奖励', 10, 10, '2020-09-12 17:41:55', NULL, 0);
INSERT INTO `yx_user_sign` VALUES (18, 44, '签到奖励', 10, 0, '2020-09-13 22:21:57', NULL, 0);

-- ----------------------------
-- Table structure for yx_user_task_finish
-- ----------------------------
DROP TABLE IF EXISTS `yx_user_task_finish`;
CREATE TABLE `yx_user_task_finish`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `task_id` int(11) NOT NULL DEFAULT 0 COMMENT '任务id',
  `uid` bigint(20) NOT NULL DEFAULT 0 COMMENT '用户id',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有效',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `is_del` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户任务完成记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_user_task_finish
-- ----------------------------

-- ----------------------------
-- Table structure for yx_wechat_live
-- ----------------------------
DROP TABLE IF EXISTS `yx_wechat_live`;
CREATE TABLE `yx_wechat_live`  (
  `room_id` bigint(11) NOT NULL COMMENT '直播间id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '直播间标题',
  `cover_imge` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '背景图',
  `share_imge` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分享图片',
  `live_status` int(9) NULL DEFAULT NULL COMMENT '直播间状态',
  `start_time` bigint(11) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` bigint(11) NULL DEFAULT NULL COMMENT '预计结束时间',
  `anchor_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主播昵称',
  `anchor_wechat` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主播微信号',
  `anchor_imge` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '主播头像',
  `type` tinyint(1) NULL DEFAULT NULL COMMENT '直播间类型 1：推流 0：手机直播',
  `screen_type` tinyint(1) NULL DEFAULT NULL COMMENT '横屏、竖屏 【1：横屏，0：竖屏】',
  `close_like` tinyint(1) NULL DEFAULT NULL COMMENT '是否关闭点赞 【0：开启，1：关闭】',
  `close_comment` tinyint(1) NULL DEFAULT NULL COMMENT '是否关闭评论 【0：开启，1：关闭】',
  `close_goods` tinyint(1) NULL DEFAULT NULL COMMENT '是否关闭货架 【0：开启，1：关闭】',
  `product_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商品id 多个,分割',
  `close_replay` tinyint(1) NULL DEFAULT NULL COMMENT '是否关闭回放【0：开启，1：关闭】',
  `close_share` tinyint(1) NULL DEFAULT NULL COMMENT '是否关闭分享【0：开启，1：关闭】',
  `close_kf` tinyint(1) NULL DEFAULT NULL COMMENT '是否关闭客服【0：开启，1：关闭】',
  PRIMARY KEY (`room_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信小程序直播表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_wechat_live
-- ----------------------------
INSERT INTO `yx_wechat_live` VALUES (20, '专业打印', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807584910.jpg', 107, 1599656400, 1599703200, '阳光正好', 'simpleton_boy', 'https://consoleapi.xinxintuan.co/file/pic/20200903171208889668.png', 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO `yx_wechat_live` VALUES (21, '广告板定制', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807568739.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807568739.jpg', 103, 1599730609, 1599730645, 'yshop', 'simpleton_boy', 'https://consoleapi.xinxintuan.co/file/pic/20200903171208889668.png', 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO `yx_wechat_live` VALUES (22, '户外写真背胶', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807625149.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807625149.jpg', 107, 1599832800, 1599904800, 'yshop', 'simpleton_boy', 'https://consoleapi.xinxintuan.co/file/pic/20200903171208889668.png', 0, 0, 0, 0, 0, NULL, NULL, NULL, NULL);
INSERT INTO `yx_wechat_live` VALUES (23, '彩色复印-量大有优惠', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg', 102, 1599865200, 1599926400, 'yshop', 'zimengzhiqiu', 'https://consoleapi.xinxintuan.co/file/pic/20200903171807647067.jpg', 0, 0, 0, 0, 0, '9,8', NULL, NULL, NULL);
INSERT INTO `yx_wechat_live` VALUES (26, 'X型展架', 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', 'https://image.dayouqiantu.cn/5ca081af6183f.jpg', 102, 1599872400, 1599926400, 'yshop', 'zimengzhiqiu', 'https://consoleapi.xinxintuan.co/file/pic/20200903171208889668.png', 0, 0, 0, 0, 0, '9,8,7', NULL, NULL, NULL);

-- ----------------------------
-- Table structure for yx_wechat_live_goods
-- ----------------------------
DROP TABLE IF EXISTS `yx_wechat_live_goods`;
CREATE TABLE `yx_wechat_live_goods`  (
  `goods_id` bigint(9) NOT NULL COMMENT '直播商品id',
  `product_id` bigint(9) NULL DEFAULT NULL COMMENT '关联商品id',
  `cover_imge_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品图片',
  `url` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品小程序路径',
  `price_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '价格类型 1：一口价（只需要传入price，price2不传） 2：价格区间（price字段为左边界，price2字段为右边界，price和price2必传） 3：显示折扣价（price字段为原价，price2字段为现价， price和price2必传）',
  `price` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL,
  `price2` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品名称',
  `third_party_tag` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '1, 2：表示是为api添加商品，否则是直播控制台添加的商品',
  `audit_id` bigint(20) NULL DEFAULT NULL COMMENT '审核单id',
  `audit_status` int(1) UNSIGNED ZEROFILL NULL DEFAULT NULL COMMENT '审核状态 0：未审核，1：审核中，2:审核通过，3审核失败',
  PRIMARY KEY (`goods_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信小程序直播商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_wechat_live_goods
-- ----------------------------
INSERT INTO `yx_wechat_live_goods` VALUES (7, 6, 'https://image.dayouqiantu.cn/news.png', 'pages/shop/GoodsCon/index.html?id=30', '3', '76', '11', '印刷各种印刷', '2', 447137937, 2);
INSERT INTO `yx_wechat_live_goods` VALUES (8, 5, 'https://image.dayouqiantu.cn/pink.png', 'pages/shop/GoodsCon/index.html?id=30', '3', '30', '11', '广告版定制', '2', 447137941, 2);
INSERT INTO `yx_wechat_live_goods` VALUES (9, 4, 'https://image.dayouqiantu.cn/news.png', 'pages/shop/GoodsCon/index.html?id=30', '3', '23', '1', '户外写真背胶', '2', 447137947, 2);

-- ----------------------------
-- Table structure for yx_wechat_media
-- ----------------------------
DROP TABLE IF EXISTS `yx_wechat_media`;
CREATE TABLE `yx_wechat_media`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '微信视频音频id',
  `type` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '回复类型',
  `path` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '文件路径',
  `media_id` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '微信服务器返回的id',
  `url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '地址',
  `temporary` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否永久或者临时 0永久1临时',
  `add_time` int(10) UNSIGNED NOT NULL COMMENT '添加时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `type`(`type`, `media_id`) USING BTREE,
  INDEX `type_2`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信回复表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_wechat_media
-- ----------------------------

-- ----------------------------
-- Table structure for yx_wechat_menu
-- ----------------------------
DROP TABLE IF EXISTS `yx_wechat_menu`;
CREATE TABLE `yx_wechat_menu`  (
  `key` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `result` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '缓存数据',
  `add_time` int(10) NULL DEFAULT NULL COMMENT '缓存时间',
  PRIMARY KEY (`key`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信缓存表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_wechat_menu
-- ----------------------------
INSERT INTO `yx_wechat_menu` VALUES ('wechat_menus', '[{\"subButtons\":[{\"name\":\"公众号演示\",\"type\":\"view\",\"url\":\"https://h5.dayouqiantu.cn\"}],\"name\":\"Yshop商城3\",\"type\":\"click\",\"key\":\"yshop\"},{\"subButtons\":[{\"appId\":\"wxa82b5b7fcb0ec161\",\"name\":\"小程序演示\",\"pagePath\":\"pages/index\",\"type\":\"miniprogram\",\"url\":\"pages/index\"}],\"name\":\"供货商城\",\"type\":\"click\",\"key\":\"supply\"},{\"subButtons\":[],\"name\":\"3333\",\"type\":\"click\",\"key\":\"2222\"}]', 1570435277);

-- ----------------------------
-- Table structure for yx_wechat_reply
-- ----------------------------
DROP TABLE IF EXISTS `yx_wechat_reply`;
CREATE TABLE `yx_wechat_reply`  (
  `id` mediumint(8) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '微信关键字回复id',
  `key` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '关键字',
  `type` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '回复类型',
  `data` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '回复数据',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '0=不可用  1 =可用',
  `hide` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '是否隐藏',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `key`(`key`) USING BTREE,
  INDEX `type`(`type`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `hide`(`hide`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信关键字回复表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_wechat_reply
-- ----------------------------
INSERT INTO `yx_wechat_reply` VALUES (1, 'subscribe', 'text', '{\"content\":\"22222222222444499990000\"}', 1, NULL);

-- ----------------------------
-- Table structure for yx_wechat_template
-- ----------------------------
DROP TABLE IF EXISTS `yx_wechat_template`;
CREATE TABLE `yx_wechat_template`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '模板id',
  `tempkey` char(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '模板编号',
  `name` char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '模板名',
  `content` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '回复内容',
  `tempid` char(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '模板ID',
  `create_time` datetime(0) NOT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL,
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态',
  `is_del` tinyint(1) NULL DEFAULT 0,
  `type` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型：template:模板消息 subscribe:订阅消息',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `tempkey`(`tempkey`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '微信模板' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of yx_wechat_template
-- ----------------------------
INSERT INTO `yx_wechat_template` VALUES (3, 'delivery_success', '订单发货提醒', '{{first.DATA}}\n订单编号：{{keyword1.DATA}}\n物流公司：{{keyword2.DATA}}\n物流单号：{{keyword3.DATA}}\n{{remark.DATA}}', 'NyrkeQ5TDFDq0GV0wkNA9L39GzPHfzbQqVLnbA5OTsY', '2020-06-25 18:54:35', '2020-07-06 15:52:09', 1, 1, NULL);
INSERT INTO `yx_wechat_template` VALUES (13, 'pay_success', '订单支付成功通知', '{{first.DATA}}\n订单编号：{{keyword1.DATA}}\n支付金额：{{keyword2.DATA}}\n{{remark.DATA}}', 'W5r2c2kzhbq8uxStkPAVx_sk-5aapMFCqe7b7KU5jXI', '2020-06-25 18:54:35', '2020-09-14 12:51:34', 1, 0, 'subscribe');
INSERT INTO `yx_wechat_template` VALUES (14, 'recharge_success', '帐户资金变动提醒', '{{first.DATA}}\n变动类型：{{keyword1.DATA}}\n变动时间：{{keyword2.DATA}}\n变动金额：{{keyword3.DATA}}\n{{remark.DATA}}', 'ePF4RS3ONCEuS9AuPyqZ2Th_B-HZ6E1CIpnJRt7ACwI', '2020-06-25 18:54:35', '2020-07-06 15:51:54', 1, 0, NULL);
INSERT INTO `yx_wechat_template` VALUES (15, 'refund_success', '退款进度通知', '', 'jaDVkOdbbk01WcWSxp1_liEQen44-euhj7shxjDvLIc', '2020-07-06 15:53:10', NULL, 0, 0, NULL);



-- ----------------------------
-- Table structure for yx_store_canvas
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_canvas`;
CREATE TABLE `yx_store_canvas`  (
                                    `canvas_id` bigint(11) NOT NULL AUTO_INCREMENT COMMENT '画布id',
                                    `terminal` tinyint(1) NOT NULL COMMENT '终端 1-小程序 2-H5 3-APP 4-PC',
                                    `json` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '画布json数据',
                                    `type` tinyint(1) NULL DEFAULT 1 COMMENT '类型 1-系统画布 2-自定义页面 3-商家店铺装修',
                                    `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '名称',
                                    `shop_id` bigint(20) NULL DEFAULT 0 COMMENT '店铺id，当type=3的时候，值为具体的店铺id，其它情况为0',
                                    `create_time` timestamp(0) NULL DEFAULT NULL COMMENT '创建时间',
                                    `update_time` timestamp(0) NULL DEFAULT NULL COMMENT '修改时间',
                                    `is_del` tinyint(1) NULL DEFAULT NULL COMMENT '删除标识',
                                    PRIMARY KEY (`canvas_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '画布信息表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of yx_store_canvas
-- ----------------------------
INSERT INTO `yx_store_canvas` VALUES (11, 3, '[{\"title\":\"店铺头部\",\"type\":\"header\",\"componentContent\":{\"title\":\"Yshop\"},\"index\":0},{\"title\":\"搜索商品\",\"type\":\"search\",\"componentContent\":{},\"index\":1},{\"title\":\"轮播图\",\"type\":\"banner\",\"componentContent\":{\"bannerData\":[{\"pic\":\"./static/img/banner.f96c3f5.png\",\"name\":\"0\",\"sort\":0,\"url\":\"/\",\"status\":1}]},\"index\":2},{\"title\":\"滚动新闻\",\"type\":\"noticeBar\",\"componentContent\":{\"roll\":[{\"uniapp_url\":\"/pages/shop/news/NewsList/index\",\"url\":\"/news_list\",\"info\":\"yshop基于springboot2+Mybatisplus商城系统，3.0版本重构了代码，新增了运费模板、sku单独管理、商品券等\",\"wxapp_url\":\"/pages/shop/news/NewsList/main\"}]},\"index\":3},{\"title\":\"菜单\",\"type\":\"menu\",\"componentContent\":{\"menus\":[{\"imageArr\":[\"https://image.dayouqiantu.cn/5e85bfa61251d.png\"],\"uniapp_url\":\"/pages/shop/GoodsList/index\",\"name\":\"全部商品\",\"id\":180,\"pic\":\"https://image.dayouqiantu.cn/all.png\",\"sort\":9,\"url\":\"/goods_list\",\"wxapp_url\":\"/pages/shop/GoodsClass/main\",\"status\":1},{\"imageArr\":[\"https://image.dayouqiantu.cn/news.png\"],\"uniapp_url\":\"/pages/shop/news/NewsList/index\",\"name\":\"图文资讯\",\"id\":196,\"pic\":\"https://image.dayouqiantu.cn/xw.png\",\"sort\":8,\"url\":\"/news_list\",\"wxapp_url\":\"/pages/shop/news/NewsList/main\",\"status\":1},{\"imageArr\":[\"https://image.dayouqiantu.cn/5e4e939507b5e.png\"],\"uniapp_url\":\"/pages/shop/GoodsCollection/index\",\"name\":\"我的收藏\",\"id\":197,\"pic\":\"https://image.dayouqiantu.cn/colle.png\",\"sort\":7,\"url\":\"/collection\",\"wxapp_url\":\"/pages/shop/GoodsCollection/main\",\"status\":1},{\"imageArr\":[\"https://image.dayouqiantu.cn/coupon.png\"],\"uniapp_url\":\"\",\"name\":\"优惠券\",\"id\":199,\"pic\":\"https://image.dayouqiantu.cn/cou.png\",\"sort\":6,\"url\":\"/adminUser/get_coupon\",\"wxapp_url\":\"/pages/adminUser/coupon/GetCoupon/main\",\"status\":1},{\"imageArr\":[\"https://image.dayouqiantu.cn/pink.png\"],\"uniapp_url\":\"/pages/activity/GoodsGroup/index\",\"name\":\"拼团专区\",\"id\":200,\"pic\":\"https://image.dayouqiantu.cn/62ac09d2914d36c65b9b59d2147d809a.png\",\"sort\":5,\"url\":\"/activity/group\",\"wxapp_url\":\"/pages/activity/GoodsGroup/main\",\"status\":1},{\"imageArr\":[\"https://image.dayouqiantu.cn/sign.png\"],\"uniapp_url\":\"/pages/adminUser/signIn/Sign/index\",\"name\":\"积分签到\",\"id\":209,\"pic\":\"https://image.dayouqiantu.cn/29ea4acebbf99e7eaf6f85af2b6d79ae.png\",\"sort\":4,\"url\":\"/adminUser/sign\",\"wxapp_url\":\"/pages/adminUser/signIn/Sign/main\",\"status\":1},{\"imageArr\":[\"https://image.dayouqiantu.cn/sekill.png\"],\"uniapp_url\":\"/pages/activity/GoodsSeckill/index\",\"name\":\"秒杀专区\",\"id\":216,\"pic\":\"https://image.dayouqiantu.cn/b0344c148141b50d68db9722708ea49e.png\",\"sort\":3,\"url\":\"/activity/goods_seckill\",\"wxapp_url\":\"/pages/activity/GoodsSeckill/main\",\"status\":1},{\"imageArr\":[\"https://image.dayouqiantu.cn/bargin.png\"],\"uniapp_url\":\"/pages/activity/GoodsBargain/index\",\"name\":\"砍价专区\",\"id\":217,\"pic\":\"https://image.dayouqiantu.cn/barService.png\",\"sort\":2,\"url\":\"/activity/bargain\",\"wxapp_url\":\"/pages/activity/GoodsBargain/main\",\"status\":1}]},\"index\":4},{\"title\":\"广告\",\"type\":\"adv\",\"componentContent\":{\"detail\":{\"list\":[{\"image\":\"https://wx.yixiang.co/static/images/index001.png\",\"url\":\"\",\"uniapp_url\":\"/pages/adminUser/coupon/GetCoupon/index\",\"wxapp_url\":\"/pages/adminUser/coupon/GetCoupon/index\",\"path_type\":1},{\"image\":\"https://wx.yixiang.co/static/images/index002.png\",\"url\":\"\",\"uniapp_url\":\"/pages/shop/GoodsList/index\",\"wxapp_url\":\"/pages/shop/GoodsList/index\",\"path_type\":1},{\"image\":\"https://wx.yixiang.co/static/images/index003.png\",\"url\":\"\",\"uniapp_url\":\"/pages/shop/GoodsList/index?title=\\\"积分商城\\\"&isIntegral=true\",\"wxapp_url\":\"/pages/shop/GoodsList/index?title=\\\"积分商城\\\"&isIntegral=true\",\"path_type\":1}],\"name\":\"\",\"style\":3}},\"index\":5},{\"title\":\"热门榜单\",\"type\":\"hotCommodity\",\"componentContent\":{},\"index\":6},{\"title\":\"为您推荐\",\"type\":\"promotionGood\",\"componentContent\":{},\"index\":7}]', 1, '1', 0, '2021-02-25 19:36:06', '2021-02-25 22:39:55', 0);

SET FOREIGN_KEY_CHECKS = 1;

-- leway
-- leway
-- leway
-- leway


-- ----------------------------
-- Table structure for yx_store_article
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_article`;
CREATE TABLE `yx_store_article` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `thumb` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '头图',
  `title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '标题',
  `intro` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '简介',
  `author` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '作者',
  `url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '地址',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `publish_time` datetime(0) NULL DEFAULT NULL COMMENT '发布时间',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '0=未发布  1 =已发布',
  `is_del` bit(1) DEFAULT NULL COMMENT '逻辑删除',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL,
  `is_custom` bit(1) DEFAULT 0 COMMENT '是否自定义文章',
  `is_top` bit(1) DEFAULT 0 COMMENT '是否置顶',
  `is_banner` bit(1) DEFAULT 0 COMMENT '是否banner',
  `sort` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `top_img` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '置顶图片',
  `banner_img` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '轮播图片',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='文章';


-- ----------------------------
-- Table structure for yx_store_broadcasting
-- ----------------------------
DROP TABLE IF EXISTS `yx_store_broadcasting`;
CREATE TABLE `yx_store_broadcasting` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `thumb` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '封面',
  `title` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '标题',
  `intro` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '简介',
  `author` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '作者',
  `url` varchar(256) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '地址',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `publish_time` datetime(0) NULL DEFAULT NULL COMMENT '发布时间',
  `status` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '0=未发布  1 =已发布',
  `is_del` bit(1) DEFAULT NULL COMMENT '逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='电台';


-- 存货管理
DROP TABLE IF EXISTS `inventory_record`;
CREATE TABLE inventory_record (
    inventory_id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    spu_id BIGINT(20) NOT NULL,
    sku_id BIGINT(20) NOT NULL,
    warehouse_id INT(10) NOT NULL DEFAULT 0 COMMENT '0北京；1石家庄',
    current_quantity INT NOT NULL DEFAULT 0,
    warehouse_location VARCHAR(100) DEFAULT NULL
) ENGINE=InnoDB AUTO_INCREMENT=1000000 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='存货';

ALTER TABLE inventory_record add KEY `idx_sku` (warehouse_id, spu_id, sku_id);
ALTER TABLE inventory_record add UNIQUE KEY `uk_sku` (sku_id, warehouse_id);


-- 操作记录
DROP TABLE IF EXISTS `inventory_transaction`;
CREATE TABLE inventory_transaction (
    transaction_id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    inventory_id BIGINT(20) NOT NULL,
    operator_id BIGINT(20) NOT NULL,
    batch_number VARCHAR(50),
    cost decimal(32,8) DEFAULT NULL COMMENT '总获取成本',
    transaction_type VARCHAR(50) NOT NULL,
    quantity INT NOT NULL,
    transaction_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    source_or_destination VARCHAR(255) DEFAULT NULL,
    remarks VARCHAR(255)
) ENGINE=InnoDB AUTO_INCREMENT=1000000 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='存货操作记录';

ALTER TABLE inventory_transaction add KEY `idx_inventory` (transaction_type, inventory_id);

-- 临时库存日汇总
-- 按照订单时间记录一个变化中的库存
DROP TABLE IF EXISTS `tmp_inventory_summary`;
CREATE TABLE tmp_inventory_summary (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    spu_id BIGINT(20) NOT NULL,
    sku_id varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'SKU unique',
    summary_date VARCHAR(50) NOT NULL COMMENT '20230203',
    total_stock_in INT DEFAULT 0,
    total_stock_out INT DEFAULT 0,
    total_transfers INT DEFAULT 0,
    inventory_adjustments INT DEFAULT 0,
    synced tinyint(1) DEFAULT 0 COMMENT '是否已经同步到库存'
) ENGINE=InnoDB AUTO_INCREMENT=1000000 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='临时库存日汇总';

ALTER TABLE tmp_inventory_summary add KEY `idx_sku_id` (synced, spu_id, sku_id);
ALTER TABLE tmp_inventory_summary add UNIQUE KEY `uk_sku` (sku_id, summary_date);

-- 临时库存
-- 按照订单时间记录一个变化中的库存
DROP TABLE IF EXISTS `tmp_inventory`;
CREATE TABLE tmp_inventory (
    id BIGINT(20) AUTO_INCREMENT PRIMARY KEY,
    spu_id BIGINT(20) NOT NULL,
    sku_id varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT 'SKU unique',
    summary_date VARCHAR(50) NOT NULL COMMENT '20230203',
    total_stock_in INT DEFAULT 0,
    total_stock_out INT DEFAULT 0,
    total_transfers INT DEFAULT 0,
    inventory_adjustments INT DEFAULT 0,
    synced tinyint(1) DEFAULT 0 COMMENT '是否已经同步到库存'
) ENGINE=InnoDB AUTO_INCREMENT=1000000 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='临时库存';

ALTER TABLE tmp_inventory add KEY `idx_sku_id` (synced, spu_id, sku_id);


-- ----------------------------
-- Table structure for c_business_resource
-- ----------------------------
DROP TABLE IF EXISTS `c_business_resource`;
CREATE TABLE `c_business_resource` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '图片',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '名称',
  `kind` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '分类',
  `act_type` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '操作类型',
  `url` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '地址',
  `sort` int(8) DEFAULT 0 COMMENT '排序',
  `product_id` bigint(20) DEFAULT NULL COMMENT '商品id',
  `create_time` datetime(0) NULL DEFAULT NULL COMMENT '添加时间',
  `update_time` datetime(0) NULL DEFAULT NULL COMMENT '更新时间',
  `start_time` datetime(0) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` datetime(0) NULL DEFAULT NULL COMMENT '预计结束时间',
  `enabled` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '启用',
  `is_del` bit(1) DEFAULT NULL COMMENT '逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8 COLLATE=utf8_general_ci COMMENT='运营位置';