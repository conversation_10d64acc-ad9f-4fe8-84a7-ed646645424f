server:
  port: 8001
  shutdown: graceful
  servlet:
    encoding:
      charset: UTF-8
      force-response: true
  tomcat:
    uri-encoding: UTF-8

spring:
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  lifecycle:
    timeout-per-shutdown-phase: 60s
  main:
    allow-circular-references: true
  freemarker:
    check-template-location: false
  profiles:
    active: dev
  jackson:
    time-zone: GMT+8
  quartz:
    properties:
      quartz:
        properties:
          file: classpath:quartz.properties
  data:
    redis:
      repositories:
        enabled: false
  servlet:
    multipart:
      # 10MB
      max-file-size: 10485760
      max-request-size: 10485760
  cache:
    # spring cache 缓存类型为redis  也可以是其他的实现，例如内存缓存 caffeine
    type: redis  # 这里定义默认的缓存类型
    caffeine:
      spec: initialCapacity=100,maximumSize=500,expireAfterWrite=1s
    redis:
      time-to-live: 60000  # 缓存存活时间
      key-prefix: cable    # key前缀
      use-key-prefix: true # 是否缓存空值。防止缓存穿透
      cache-null-values: true

task:
  pool:
    # 核心线程池大小
    core-pool-size: 10
    # 最大线程数
    max-pool-size: 30
    # 活跃时间
    keep-alive-seconds: 60
    # 队列容量
    queue-capacity: 50

#七牛云
qiniu:
  # 文件大小 /M
  max-size: 15

#邮箱验证码有效时间/分钟
code:
  expiration: 5

#登录图形验证码有效时间/分钟
loginCode:
  expiration: 2

#密码加密传输，前端公钥加密，后端私钥解密
rsa:
  private_key: MIIBUwIBADANBgkqhkiG9w0BAQEFAASCAT0wggE5AgEAAkEA0vfvyTdGJkdbHkB8mp0f3FE0GYP3AYPaJF7jUd1M0XxFSE2ceK3k2kw20YvQ09NJKk+OMjWQl9WitG9pB6tSCQIDAQABAkA2SimBrWC2/wvauBuYqjCFwLvYiRYqZKThUS3MZlebXJiLB+Ue/gUifAAKIg1avttUZsHBHrop4qfJCwAI0+YRAiEA+W3NK/RaXtnRqmoUUkb59zsZUBLpvZgQPfj1MhyHDz0CIQDYhsAhPJ3mgS64NbUZmGWuuNKp5coY2GIj/zYDMJp6vQIgUueLFXv/eZ1ekgz2Oi67MNCk5jeTF2BurZqNLR3MSmUCIFT3Q6uHMtsB9Eha4u7hS31tj1UWE+D+ADzp59MGnoftAiBeHT7gDMuqeJHPL4b+kC+gzV4FGTfhR9q3tTbklZkD2A==

# sm.ms 图床的 token
smms:
  token: 1oOP3ykFDI0K6ifmtvU7c8Y1eTWZSlyl

yshop:
  # 配置
  version: 3.3
  erpUrl: http://localhost:3000
  resourcePath: "resource"
  workerId: 1
  server:
    read-timeout: 300000 # 300秒
    write-timeout: 300000 # 300秒

# application.yml
# http://localhost:8001/health
management:
  endpoints:
    web:
      exposure:
        include: health

logging:
  level:
    com.leway: DEBUG
    org.springframework.web: DEBUG
    com.github.binarywang.demo.wx.mp: DEBUG
    me.chanjar.weixin: DEBUG

mybatis-plus:
  check-config-location: true
  configuration:
    map-underscore-to-camel-case: true
  global-config:
    db-config:
      id-type: auto
      logic-delete-value: 1
      logic-not-delete-value: 0
  #ERP配置
  mapper-locations: classpath:./mapper_xml/*.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

sentry:
  dsn:
  # Set traces-sample-rate to 1.0 to capture 100% of transactions for performance monitoring.
  # We recommend adjusting this value in production.
  sentry.traces-sample-rate: 1.0

#ERP配置

#租户对应的角色id 租户role
manage:
  roleId: 10
#租户允许创建的用户数
tenant:
  userNumLimit: 1000000

#租户允许试用的天数
  tryDayLimit: 3000

# 文件存储路径
file:
  path: upload/file/
  avatar: upload/avatar/
  # 文件大小 /M
  maxSize: 100
  avatarMaxSize: 5
