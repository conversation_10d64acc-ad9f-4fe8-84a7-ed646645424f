/**
* Copyright (C) 2019-2023
* All rights reserved, <NAME_EMAIL>
* 注意：
* 本软件为************开发研制，未经购买不得使用
* 一经发现盗用、分享等行为，将追究法律责任，后果自负
*/
package ${package}.service.impl;

import ${package}.domain.${className};
<#if columns??>
    <#list columns as column>
        <#if column.columnKey = 'UNI'>
            <#if column_index = 1>
import com.leway.exception.EntityExistException;
            </#if>
        </#if>
    </#list>
</#if>
import com.leway.common.service.impl.BaseServiceImpl;
import lombok.AllArgsConstructor;
import com.leway.dozer.service.IGenerator;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.leway.common.utils.QueryHelpPlus;
import com.leway.utils.ValidationUtil;
import com.leway.utils.FileUtil;
import ${package}.service.${className}Service;
import ${package}.service.dto.${className}Dto;
import ${package}.service.dto.${className}QueryCriteria;
import ${package}.service.mapper.${className}Mapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
<#if !auto && pkColumnType = 'Long'>
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
</#if>
<#if !auto && pkColumnType = 'String'>
import cn.hutool.core.util.IdUtil;
</#if>

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import com.leway.domain.PageResult;
/**
* <AUTHOR>
* @date ${date}
*/
@Service
@AllArgsConstructor
//@CacheConfig(cacheNames = "${changeClassName}#60")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class ${className}ServiceImpl extends BaseServiceImpl<${className}Mapper, ${className}> implements ${className}Service {

    private final IGenerator generator;

    @Override
    public PageResult<${className}Dto> queryAll(${className}QueryCriteria criteria, Pageable pageable) {
        getPage(pageable);
        PageInfo<${className}> page = new PageInfo<>(queryAll(criteria));
        return generator.convertPageInfo(page,${className}Dto.class);
    }


    @Override
    public List<${className}> queryAll(${className}QueryCriteria criteria){
        return baseMapper.selectList(QueryHelpPlus.getPredicate(${className}.class, criteria));
    }


    @Override
    public void download(List<${className}Dto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (${className}Dto ${changeClassName} : all) {
            Map<String,Object> map = new LinkedHashMap<>();
        <#list columns as column>
            <#if column.columnKey != 'PRI'>
            <#if column.remark != ''>
            map.put("${column.remark}", ${changeClassName}.get${column.capitalColumnName}());
            <#else>
            map.put(" ${column.changeColumnName}",  ${changeClassName}.get${column.capitalColumnName}());
            </#if>
            </#if>
        </#list>
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }
}
