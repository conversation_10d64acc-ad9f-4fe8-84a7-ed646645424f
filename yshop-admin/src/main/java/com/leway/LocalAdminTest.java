package com.leway;

import java.awt.FontFormatException;
import java.io.IOException;
import java.util.Collections;

import com.getsentry.raven.DefaultRavenFactory;
import com.getsentry.raven.Raven;
import com.getsentry.raven.dsn.Dsn;
import com.leway.modules.product.domain.Product;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.support.DefaultActiveProfilesResolver;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import com.binarywang.spring.starter.wxjava.miniapp.config.WxMaAutoConfiguration;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.services.CreateShareProductService;

/**
 * <AUTHOR>
 * @date 2019/10/1 9:20:19
 */
@EnableAsync
@EnableTransactionManagement // 事务
@MapperScan(basePackages = { "com.leway.modules.*.service.mapper", "com.leway.config",
})
// @MapperScan(basePackages = { "com.leway.modules.*.service.mapper",
// "com.leway.config",
// "com.leway.modules.erp.datasource.mappers" })
// @ActiveProfiles(profiles = "test", resolver =
// DefaultActiveProfilesResolver.class) // 设置profile，可控制连接到测试数据库
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class, WxMaAutoConfiguration.class })
public class LocalAdminTest {

    public static void main(String[] args) throws IOException {
        System.setProperty("spring.devtools.restart.enabled", "false");

        SpringApplication app = new SpringApplication(com.leway.LocalAdminTest.class);
        app.setDefaultProperties(Collections.singletonMap("spring.profiles.active", "test"));
        ConfigurableApplicationContext context = app.run(args);

        try {
            // testPoster(context);
            testSentry();
        } catch (Exception e) {
            e.printStackTrace();
        }

        context.close();
    }

    public static void testSentry() {
        int a = 1 / 0;
    }

    // public static void testPoster(ConfigurableApplicationContext context) throws
    // IOException, FontFormatException {
    // CreateShareProductService createShareProductService =
    // context.getBean(CreateShareProductService.class);
    // ProductService productService = context.getBean(ProductService.class);
    //
    // // 1. 获取商品信息
    // Long id = 622l;
    // Product storeProduct = productService.getById(id);
    //
    // createShareProductService.creatCableProductPic(storeProduct, "SC01", "分享图",
    // "/Users/<USER>/Downloads/poster.jpg", "http://baidu.com");
    // }

}
