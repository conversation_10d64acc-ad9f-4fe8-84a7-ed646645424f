/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>
 */
package com.leway.handler;

import static org.springframework.http.HttpStatus.NOT_FOUND;
import static org.springframework.http.HttpStatus.valueOf;

import java.util.Objects;

import org.apache.catalina.connector.ClientAbortException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import com.leway.exception.BadRequestException;
import com.leway.exception.EntityExistException;
import com.leway.exception.EntityNotFoundException;
import com.leway.exception.ErrorRequestException;
import com.leway.utils.ThrowableUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2018-11-23
 */
@Slf4j
@RestControllerAdvice
@SuppressWarnings("unchecked")
public class GlobalExceptionHandler {
    @Value("${sentry.dsn}")
    private String dsnUrl;

    private void sendSentry(Throwable e) {
        if (dsnUrl == null || "".equals(dsnUrl)) {
            log.error("local error when Dsn not set, {}", e.getMessage());
            return;
        }

        // Dsn dsn = new Dsn(dsnUrl);
        // Raven raven = (new DefaultRavenFactory()).createRavenInstance(dsn);
        // Throwable throwable = new Throwable(e.getMessage(), e.getCause());
        // throwable.setStackTrace(e.getStackTrace());
        // raven.sendException(throwable);
    }

    /**
     * 处理所有不可知的异常
     */
    @ExceptionHandler(Throwable.class)
    public ResponseEntity<ApiError> handleException(Throwable e) {
        sendSentry(e);
        // 打印堆栈信息
        log.error(ThrowableUtil.getStackTrace(e));
        return buildResponseEntity(ApiError.error(e.getMessage()));
    }

    /**
     * 处理 EntityExist
     */
    @ExceptionHandler(value = EntityExistException.class)
    public ResponseEntity<ApiError> entityExistException(EntityExistException e) {
        sendSentry(e);
        // 打印堆栈信息
        log.error(ThrowableUtil.getStackTrace(e));
        return buildResponseEntity(ApiError.error(e.getMessage()));
    }

    /**
     * 处理 EntityNotFound
     */
    @ExceptionHandler(value = EntityNotFoundException.class)
    public ResponseEntity<ApiError> entityNotFoundException(EntityNotFoundException e) {
        sendSentry(e);
        // 打印堆栈信息
        log.error(ThrowableUtil.getStackTrace(e));
        return buildResponseEntity(ApiError.error(NOT_FOUND.value(), e.getMessage()));
    }

    /**
     * 处理所有接口数据验证异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<ApiError> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
        sendSentry(e);
        // 打印堆栈信息
        log.error(ThrowableUtil.getStackTrace(e));
        String[] str = Objects.requireNonNull(e.getBindingResult().getAllErrors().get(0).getCodes())[1].split("\\.");
        String message = e.getBindingResult().getAllErrors().get(0).getDefaultMessage();
        String msg = "不能为空";
        if (msg.equals(message)) {
            message = str[1] + ":" + message;
        }
        return buildResponseEntity(ApiError.error(message));
    }

    /**
     * 统一返回
     */
    private ResponseEntity<ApiError> buildResponseEntity(ApiError apiError) {
        return new ResponseEntity<>(apiError, valueOf(apiError.getStatus()));
    }

    /**
     * 处理 ClientAbortException
     */
    @ExceptionHandler(ClientAbortException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<ApiError> handleClientAbortException(ClientAbortException e) {
        // 不做任何处理，直接忽略
        log.debug("ClientAbortException 被忽略: {}", e.getMessage());
        return buildResponseEntity(ApiError.error(HttpStatus.OK.value(), "ClientAbortException 被忽略: " + e.getMessage()));
    }

    /**
     * 处理 BadCredentialsException
     */
    @ExceptionHandler(BadCredentialsException.class)
    public ResponseEntity<ApiError> handleBadCredentialsException(BadCredentialsException e) {
        // 打印堆栈信息
        log.error(ThrowableUtil.getStackTrace(e));
        return buildResponseEntity(ApiError.error(HttpStatus.UNAUTHORIZED.value(), "用户名或密码错误"));
    }

    /**
     * 处理 BadRequestException
     */
    @ExceptionHandler(BadRequestException.class)
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<ApiError> handleBadRequestException(BadRequestException e) {
        // 不做任何处理，直接忽略
        log.debug("BadRequestException 被忽略: {}", e.getMessage());
        return buildResponseEntity(ApiError.error(HttpStatus.OK.value(), "BadRequestException 被忽略: " + e.getMessage()));
    }
}
