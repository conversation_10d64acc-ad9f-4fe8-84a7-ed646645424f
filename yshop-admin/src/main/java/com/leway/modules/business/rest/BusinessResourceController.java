package com.leway.modules.business.rest;

/**
* Copyright (C) 2019-2023
* All rights reserved, <NAME_EMAIL>
* 注意：
* 本软件为************开发研制，未经购买不得使用
* 一经发现盗用、分享等行为，将追究法律责任，后果自负
*/

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import java.util.Arrays;
import com.leway.dozer.service.IGenerator;
import lombok.AllArgsConstructor;
import com.leway.modules.logging.aop.log.Log;
import com.leway.modules.business.domain.BusinessResource;
import com.leway.modules.business.service.IBusinessResourceService;
import com.leway.modules.business.service.dto.BusinessResourceQueryCriteria;

import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.leway.domain.PageResult;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
@Api(tags = "后台:运营资源位置")
@RestController
@RequestMapping("/api/business_resource")
public class BusinessResourceController {

    private final IBusinessResourceService brService;
    private final IGenerator generator;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    // @PreAuthorize("@el.check('admin','business_resource:list')")
    public void download(HttpServletResponse response, BusinessResourceQueryCriteria criteria) throws IOException {
        brService.download(
                generator.convert(brService.queryAll(criteria), BusinessResource.class),
                response);
    }

    @GetMapping
    @Log("查询资源位")
    @ApiOperation("查询资源位")
    // @PreAuthorize("@el.check('admin','business_resource:list')")
    public ResponseEntity<PageResult<BusinessResource>> getBusinessResources(
            BusinessResourceQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(brService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增资源位")
    @ApiOperation("新增资源位")
    // @PreAuthorize("@el.check('admin','business_resource:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody BusinessResource resources) {
        ResponseEntity resp = new ResponseEntity<>(brService.save(resources), HttpStatus.CREATED);
        brService.evictAll();
        return resp;
    }

    @PutMapping
    @Log("修改资源位")
    @ApiOperation("修改资源位")
    // @PreAuthorize("@el.check('admin','business_resource:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody BusinessResource resources) {
        Long id = resources.getId();
        brService.forceUpdateFields(id, resources);

        brService.evictAll();
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除资源位")
    @ApiOperation("删除资源位")
    // @PreAuthorize("@el.check('admin','business_resource:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        Arrays.asList(ids).forEach(id -> {
            brService.removeById(id);
        });
        brService.evictAll();
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
