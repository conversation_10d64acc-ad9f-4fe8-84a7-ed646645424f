package com.leway.modules.feedback;

import java.util.Arrays;
import com.leway.dozer.service.IGenerator;
import com.leway.modules.user.domain.Feedback;
import com.leway.modules.user.service.FeedbackService;
import lombok.AllArgsConstructor;
import com.leway.modules.logging.aop.log.Log;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.leway.domain.PageResult;

/**
 * <AUTHOR>
 * @date 2022-11-13
 */
@AllArgsConstructor
@Api(tags = "后台:意见反馈管理")
@RestController
@RequestMapping("/api/feedback")
public class AdminFeedbackController {

    private final FeedbackService feedbackService;

    @GetMapping
    @Log("查询意见反馈")
    @ApiOperation("查询意见反馈")
    @PreAuthorize("@el.check('admin','feedback:list')")
    public ResponseEntity<PageResult<Feedback>> getFeedbacks(Feedback criteria,
            Pageable pageable) {
        return new ResponseEntity<>(feedbackService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增意见反馈")
    @ApiOperation("新增意见反馈")
    @PreAuthorize("@el.check('admin','feedback:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody Feedback resources) {
        ResponseEntity<Object> ret = new ResponseEntity<>(feedbackService.save(resources), HttpStatus.CREATED);
        return ret;
    }

    @PutMapping
    @Log("修改意见反馈")
    @ApiOperation("修改意见反馈")
    @PreAuthorize("@el.check('admin','feedback:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody Feedback resources) {
        feedbackService.updateById(resources);
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除意见反馈")
    @ApiOperation("删除意见反馈")
    @PreAuthorize("@el.check('admin','feedback:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        Arrays.asList(ids).forEach(id -> {
            feedbackService.removeById(id);
        });
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
