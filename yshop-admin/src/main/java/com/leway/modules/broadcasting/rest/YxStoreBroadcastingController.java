/**
* Copyright (C) 2019-2023
* All rights reserved, <NAME_EMAIL>
* 注意：
* 本软件为************开发研制，未经购买不得使用
* 一经发现盗用、分享等行为，将追究法律责任，后果自负
*/
package com.leway.modules.broadcasting.rest;

import java.util.Arrays;
import com.leway.dozer.service.IGenerator;
import lombok.AllArgsConstructor;
import com.leway.modules.logging.aop.log.Log;
import com.leway.modules.broadcasting.domain.YxStoreBroadcasting;
import com.leway.modules.broadcasting.service.YxStoreBroadcastingService;
import com.leway.modules.broadcasting.service.dto.YxStoreBroadcastingQueryCriteria;
import com.leway.modules.broadcasting.service.dto.YxStoreBroadcastingDto;

import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.leway.domain.PageResult;

/**
 * <AUTHOR>
 * @date 2022-11-13
 */
@Lazy
@AllArgsConstructor
@Api(tags = "后台:电台管理")
@RestController
@RequestMapping("/api/yxStoreBroadcasting")
public class YxStoreBroadcastingController {

    private final YxStoreBroadcastingService yxStoreBroadcastingService;
    private final IGenerator generator;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('admin','yxStoreBroadcasting:list')")
    public void download(HttpServletResponse response, YxStoreBroadcastingQueryCriteria criteria) throws IOException {
        yxStoreBroadcastingService.download(
                generator.convert(yxStoreBroadcastingService.queryAll(criteria), YxStoreBroadcastingDto.class),
                response);
    }

    @GetMapping
    @Log("查询broadcasting")
    @ApiOperation("查询broadcasting")
    @PreAuthorize("@el.check('admin','yxStoreBroadcasting:list')")
    public ResponseEntity<PageResult<YxStoreBroadcastingDto>> getYxStoreBroadcastings(
            YxStoreBroadcastingQueryCriteria criteria, Pageable pageable) {
        return new ResponseEntity<>(yxStoreBroadcastingService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增broadcasting")
    @ApiOperation("新增broadcasting")
    @PreAuthorize("@el.check('admin','yxStoreBroadcasting:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody YxStoreBroadcasting resources) {
        ResponseEntity resp = new ResponseEntity<>(yxStoreBroadcastingService.save(resources), HttpStatus.CREATED);
        yxStoreBroadcastingService.evictAll();
        return resp;
    }

    @PutMapping
    @Log("修改broadcasting")
    @ApiOperation("修改broadcasting")
    @PreAuthorize("@el.check('admin','yxStoreBroadcasting:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody YxStoreBroadcasting resources) {
        yxStoreBroadcastingService.updateById(resources);
        yxStoreBroadcastingService.evictAll();
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除broadcasting")
    @ApiOperation("删除broadcasting")
    @PreAuthorize("@el.check('admin','yxStoreBroadcasting:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        Arrays.asList(ids).forEach(id -> {
            yxStoreBroadcastingService.removeById(id);
        });
        yxStoreBroadcastingService.evictAll();
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
