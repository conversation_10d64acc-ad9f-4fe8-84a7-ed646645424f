/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>

 */
package com.leway.modules.quartz.service.dto;

import com.leway.annotation.Query;
import lombok.Data;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020-05-13
 */
@Data
public class QuartzJobQueryCriteria {

    @Query(type = Query.Type.INNER_LIKE)
    private String jobName;

    @Query
    private Boolean isSuccess;

    @Query
    private Boolean isPause;

    @Query(type = Query.Type.BETWEEN)
    private List<Timestamp> createTime;
}
