package com.leway.modules.quartz.task;

import org.springframework.stereotype.Component;

import com.leway.modules.logging.service.LogService;
import com.leway.modules.quartz.service.QuartzLogService;

// import lombok.extern.slf4j.Slf4j;

// @Slf4j
@Component
public class CleanLogTask {

    private final QuartzLogService quartzLogService;
    private final LogService logService;

    public CleanLogTask(QuartzLogService quartzLogService, LogService logService) {
        this.quartzLogService = quartzLogService;
        this.logService = logService;
    }
    
    public void run(){
        // 1. 定时任务日志
        quartzLogService.deleteOld();

        // 2. 后台操作日志
    }
}
