package com.leway.modules.quartz.task;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import com.leway.enums.PayStsEnum;
import com.leway.modules.order.service.StoreOrderService;
import com.leway.utils.RedisUtils;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.leway.constant.ShopConstants;
import com.leway.enums.OrderInfoEnum;
import com.leway.modules.order.domain.Order;

import lombok.extern.slf4j.Slf4j;

/**
 * 未付款订单关闭，退还库存
 */
@Slf4j
@Component
public class OrderUnPayCheckTask {
    private StoreOrderService storeOrderService;
    private RedisUtils redisUtils;

    public OrderUnPayCheckTask(StoreOrderService storeOrderService, RedisUtils redisUtils) {
        this.storeOrderService = storeOrderService;
        this.redisUtils = redisUtils;

    }

    public void run() {
        try {
            // Get the current time
            Instant now = Instant.now();

            // some minutes before
            Instant minutesBefore = now.minus(Duration.ofMinutes(ShopConstants.ORDER_OUTTIME_UN_PAY + 5));
            Date theTime = Date.from(minutesBefore);

            // 尝试获得锁
            String locked = (String) redisUtils.get(ShopConstants.LOCK_KEY_BATCH_DELIVERY);
            boolean isLocked = locked != null && locked.equals("1");
            if (isLocked) {
                // log.info("正在执行批量发货，暂时跳过关闭订单任务");
                return;
            }

            // 2天前
            Date yestoday = Date.from(now.minus(Duration.ofDays(2)));

            // 1. 查询
            // 未付款，且不是取消、不是关闭状态的订单
            List<String> orderIDs = this.storeOrderService.listObjs(new LambdaQueryWrapper<Order>()
                    .select(Order::getOrderId)
                    // 未付款
                    .eq(Order::getPaid, PayStsEnum.PAY_STATUS_0_UNPAY.getValue())
                    // 创建时间早于规定时间
                    .lt(Order::getCreateTime, theTime)
                    .gt(Order::getCreateTime, yestoday)
                    .notIn(Order::getCancelStatus, OrderInfoEnum.CANCEL_STATUS_2_CLOSED.getValue(),
                            OrderInfoEnum.CANCEL_STATUS_1_CANCELED.getValue()),
                    id -> {
                        // 进行一系列转换操作 ，最终返回我们所需要的类型V ， 这里只做简单演示。
                        return id.toString();
                    });

            // 2. 关闭订单
            for (int i = 0; i < orderIDs.size(); i++) {
                String orderID = orderIDs.get(i);
                log.info("自动关闭超时订单: {}", orderID);
                try {
                    storeOrderService.closeOrder(String.valueOf(orderID), null);
                } catch (Exception e) {
                    log.error("自动关闭订单失败: {} {}", orderID, e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error("自动关闭订单任务执行失败: {}", e.getMessage());
            e.printStackTrace();
        }
    }
}
