package com.leway.modules.quartz.task;

import java.util.Date;

import org.springframework.stereotype.Component;

import com.leway.modules.order.service.StoreOrderService;
import com.leway.utils.DateUtils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 每天3点12分同步
 * 0 12 3 * * ? *
 */
@Slf4j
@Component
public class DailyStockSummaryTask {

    private final StoreOrderService storeOrderService;

    public DailyStockSummaryTask(StoreOrderService storeOrderService) {
        this.storeOrderService = storeOrderService;
    }

    /**
     * 同步前一天的库存
     */
    public void run() {
        Date today = DateUtils.getNowDate();
        Date yestoday = DateUtils.addDays(today, -1);

        String summaryDate = DateUtil.format(yestoday, DatePattern.PURE_DATE_PATTERN);
        try {
            storeOrderService.syncTmpInventory(summaryDate);
        } catch (Exception e) {
            log.error("同步{}库存失败, {}", summaryDate, e.getMessage());
            e.printStackTrace();
        }
    }

}
