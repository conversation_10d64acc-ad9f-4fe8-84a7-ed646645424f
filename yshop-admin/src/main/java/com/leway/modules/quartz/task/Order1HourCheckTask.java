package com.leway.modules.quartz.task;

import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;

import com.leway.enums.PayStsEnum;
import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.leway.constant.ShopConstants;
import com.leway.enums.OrderInfoEnum;
import com.leway.modules.mp.service.WeiXinOrderShippingService;
import com.leway.modules.order.domain.Order;
import com.leway.modules.order.service.StoreOrderService;
import com.leway.utils.RedisUtils;

import lombok.extern.slf4j.Slf4j;

/**
 * 每4分钟检测前一个小时，冗余时间为10分钟
 * 例如：两点10分检查1-2点的订单
 * 
 * 1. 设置订单为出库中（已发货）状态
 * 2. 同步已发货订单给微信
 */
@Slf4j
@Component
public class Order1HourCheckTask {

    private StoreOrderService orderService;
    private RedisUtils redisUtils;

    public Order1HourCheckTask(
            StoreOrderService orderService, RedisUtils redisUtils) {
        this.orderService = orderService;
        this.redisUtils = redisUtils;
    }

    public void run() {
        // 尝试获得锁
        String locked = (String) redisUtils.get(ShopConstants.LOCK_KEY_BATCH_DELIVERY);
        boolean isLocked = locked != null && locked.equals("1");
        if (isLocked) {
            log.info("正在执行批量发货，暂时跳过出库任务");
            return;
        }

        // hourbefore
        // 假设3点04分检查，应该检查的是1-2点的订单
        // 以及冗余时间，再往前推4分钟
        Instant now = Instant.now();
        Instant hourBefore = now.minus(Duration.ofHours(ShopConstants.ORDER_DEFAULT_DEIVER_HOUR));

        Date theTime = Date.from(hourBefore);

        // 2天前
        Date day2Before = Date.from(now.minus(Duration.ofDays(2)));

        // 1. 查询已付款，还未标记发货的订单
        List<String> orderIDs = this.orderService.listObjs(new LambdaQueryWrapper<Order>()
                .select(Order::getOrderId)
                // 已付款
                .eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                // 待发货
                .eq(Order::getStatus, OrderInfoEnum.STATUS_NORMAL_0.getValue())
                // 未取消
                .eq(Order::getCancelStatus, OrderInfoEnum.CANCEL_STATUS_0_NORMAL.getValue())
                // 发货状态为尚未发货
                .eq(Order::getDeliveryStatus, OrderInfoEnum.DELIVER_STATUS_0_NORMAL.getValue())
                // 创建时间早于规定时间
                .gt(Order::getCreateTime, day2Before)
                .lt(Order::getCreateTime, theTime), id -> {
                    // 进行一系列转换操作 ，最终返回我们所需要的类型V ， 这里只做简单演示。
                    return id.toString();
                });

        // 2. 设置为出库中（假发货）
        orderIDs.forEach(orderID -> {
            // log.info("自动设置订单为已发货(出库中)状态: {}", orderID);
            try {
                orderService.setDeliverOfOutDepot(String.valueOf(orderID), null);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        //
        //
        // 3. 查询已发货，未同步给微信的订单
        //
        //
        List<String> waitSyncOrderIDs = this.orderService.listObjs(new LambdaQueryWrapper<Order>()
                .select(Order::getOrderId)
                // 已付款
                .eq(Order::getPaid, PayStsEnum.PAY_STATUS_1_PAID.getValue())
                // 已发货
                .eq(Order::getStatus, OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue())
                // 发货状态为真的已经发货
                .eq(Order::getDeliveryStatus, OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue())
                // 微信订单同步状态为待发货
                .in(Order::getWechatOrderStatus, WeiXinOrderShippingService.OrderState.INIT.getCode(),
                        WeiXinOrderShippingService.OrderState.WAITING_FOR_DELIVERY.getCode())
                // 创建时间早于规定时间
                .lt(Order::getCreateTime, theTime), id -> {
                    // 进行一系列转换操作 ，最终返回我们所需要的类型V ， 这里只做简单演示。
                    return id.toString();
                });

        // 4. 上传信息给微信
        waitSyncOrderIDs.forEach(orderID -> {
            // log.info("自动同步物流发货信息给微信: {}", orderID);
            orderService.uploadDeliveryInfo2Wechat(orderID);
        });
    }
}
