/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>

 */
package com.leway.modules.quartz.service.impl;

import com.leway.common.service.impl.BaseServiceImpl;
import com.leway.common.utils.QueryHelpPlus;
import com.leway.dozer.service.IGenerator;
import com.leway.modules.quartz.domain.QuartzLog;
import com.leway.modules.quartz.service.QuartzLogService;
import com.leway.modules.quartz.service.dto.QuartzLogDto;
import com.leway.modules.quartz.service.dto.QuartzLogQueryCriteria;
import com.leway.modules.quartz.service.mapper.QuartzLogMapper;
import com.leway.utils.DateUtils;
import com.leway.utils.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageInfo;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Date;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

// 默认不使用缓存
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;

/**
 * <AUTHOR>
 * @date 2020-05-13
 */
@Slf4j
@Service
@AllArgsConstructor
@CacheConfig(cacheNames = "quartzLog#60")
@Transactional(propagation = Propagation.SUPPORTS, readOnly = true, rollbackFor = Exception.class)
public class QuartzLogServiceImpl extends BaseServiceImpl<QuartzLogMapper, QuartzLog> implements QuartzLogService {

    private final IGenerator generator;

    @Override
    @Cacheable
    public Map<String, Object> queryAll(QuartzLogQueryCriteria criteria, Pageable pageable) {
        getPageByStartPage0(pageable);
        PageInfo<QuartzLog> page = new PageInfo<>(queryAll(criteria));
        Map<String, Object> map = new LinkedHashMap<>(2);
        map.put("content", generator.convert(page.getList(), QuartzLogDto.class));
        map.put("totalElements", page.getTotal());
        return map;
    }

    @Override
    @Cacheable
    public List<QuartzLog> queryAll(QuartzLogQueryCriteria criteria) {
        return baseMapper.selectList(QueryHelpPlus.getPredicate(QuartzLog.class, criteria));
    }

    /**
     * 导出数据
     *
     * @param all      待导出的数据
     * @param response /
     * @throws IOException /
     */
    @Override
    public void download(List<QuartzLogDto> all, HttpServletResponse response) throws IOException {
        List<Map<String, Object>> list = new ArrayList<>();
        for (QuartzLogDto quartzLog : all) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put(" baenName", quartzLog.getBaenName());
            map.put(" createTime", quartzLog.getCreateTime());
            map.put(" cronExpression", quartzLog.getCronExpression());
            map.put(" exceptionDetail", quartzLog.getExceptionDetail());
            map.put(" isSuccess", quartzLog.getIsSuccess());
            map.put(" jobName", quartzLog.getJobName());
            map.put(" methodName", quartzLog.getMethodName());
            map.put(" params", quartzLog.getParams());
            map.put(" time", quartzLog.getTime());
            map.put("任务名称", quartzLog.getBaenName());
            map.put("Bean名称 ", quartzLog.getCreateTime());
            map.put("cron表达式", quartzLog.getCronExpression());
            map.put("异常详细 ", quartzLog.getExceptionDetail());
            map.put("状态", quartzLog.getIsSuccess());
            map.put("任务名称", quartzLog.getJobName());
            map.put("方法名称", quartzLog.getMethodName());
            map.put("参数", quartzLog.getParams());
            map.put("耗时（毫秒）", quartzLog.getTime());
            list.add(map);
        }
        FileUtil.downloadExcel(list, response);
    }

    /**
     * 清理旧的日志
     */
    @Override
    @CacheEvict(allEntries = true)
    public void deleteOld() {
        // 删除前一个月的日志
        List<Timestamp> timeBetween = new ArrayList<>();
        Date start = DateUtils.getDateBeforeDay(30);
        Date end = DateUtils.getDateBeforeDay(7);
        timeBetween.add(new Timestamp(start.getTime()));
        timeBetween.add(new Timestamp(end.getTime()));
        // QuartzLogQueryCriteria criteria = new QuartzLogQueryCriteria();
        // criteria.setCreateTime(timeBetween);
        // List<QuartzLog> logs =
        // baseMapper.selectList(QueryHelpPlus.getPredicate(QuartzLog.class, criteria));
        LambdaQueryWrapper<QuartzLog> q = new LambdaQueryWrapper<QuartzLog>().between(QuartzLog::getCreateTime, start,
                end);
        int n = baseMapper.delete(q);
        log.info("删除{}条日志", n);
    }

}
