package com.leway.modules.quartz.task;

import org.springframework.stereotype.Component;

import com.leway.modules.order.service.StoreOrderService;

import lombok.extern.slf4j.Slf4j;

/*
 * 每日向微信结算：自动确认收货的机制
 * <AUTHOR>
 * 1. 用户未确认收货，但是已经超过了10天，系统自动确认收货
 * 2. 将订单完成状态的订单，自动结算到商家账户 
 * 在Quartz调度器中，你使用cron表达式来安排一个作业（Job）。

Quartz的cron表达式与Unix/Linux的crontab非常类似，但它包含了一个额外的字段，用于指定秒。Quartz的cron表达式包含七个位置时间字段，时间从左到右的顺序如下：

```
秒 分 时 日 月 周 年(可选)
```

如果你想要每天下午1点12分执行任务，Quartz表达式会是：

```
0 12 1 * * ? *
```

这里的字段解释如下：

- `0` 表示秒钟的开始时刻。
- `12` 表示分钟 —— 任务会在每小时的第12分启动。
- `13` 表示小时 —— 24小时制的下午1点。
- `*` 表示日子，每天。
- `*` 表示月份，每个月。
- `?` 表示周天，使用`?`表示对该字段不设置具体值。在日和周天字段中，你需要至少在一个位置上设置一个问号。
- `*` 表示年份，每年都执行，这个字段是可选的。

需要注意的是，在日和周天这两个位置，通常会有一个设置为“?”，因为这两个字段冲突的时候会导致不明确的情况。例如，如果你在“日”位置上设置了一个特定的日子，在“周天”位置上也设置了一个特定的星期几，那么就不清楚具体执行的优先级了。因此通常将不使用的那一个设置为“?”。

你可以把这个cron表达式用在Quartz的相关配置或API调用中，以安排你的作业。
//  * 3. 同步已完成订单到ERP零售出库中 
 */
@Slf4j
@Component
public class DailySettleTask {
    private final StoreOrderService storeOrderService;

    public DailySettleTask(StoreOrderService storeOrderService) {
        this.storeOrderService = storeOrderService;
    }

    public void run() {
        // 1. 对微信发货中的订单进行处理->确认收货
        try {
            storeOrderService.checkWechatDeliveryInfo2me();
        } catch (Exception e) {
            log.error("每日批量自动确认收货执行失败", e.getMessage());
            e.printStackTrace();
        }

        // 2. 对微信确认收货的订单进行处理->交易完成
        try {
            storeOrderService.syncFinishOrder2Wechat();
        } catch (Exception e) {
            log.error("每日批量标记交易完成失败", e.getMessage());
            e.printStackTrace();
        }
    }

}
