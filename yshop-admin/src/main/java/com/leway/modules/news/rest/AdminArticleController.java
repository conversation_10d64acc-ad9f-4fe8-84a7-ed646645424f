/**
* Copyright (C) 2019-2023
* All rights reserved, <NAME_EMAIL>
* 注意：
* 本软件为************开发研制，未经购买不得使用
* 一经发现盗用、分享等行为，将追究法律责任，后果自负
*/
package com.leway.modules.news.rest;

import java.util.Arrays;
import com.leway.dozer.service.IGenerator;
import lombok.AllArgsConstructor;
import com.leway.modules.logging.aop.log.Log;
import com.leway.modules.news.domain.YxStoreArticle;
import com.leway.modules.news.service.YxStoreArticleService;
import com.leway.modules.news.service.dto.YxStoreArticleQueryCriteria;
import com.leway.modules.news.service.dto.YxStoreArticleDto;

import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.*;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import com.leway.domain.PageResult;

/**
 * <AUTHOR>
 * @date 2022-11-13
 */
@Lazy
@AllArgsConstructor
@Api(tags = "后台:新闻管理")
@RestController
@RequestMapping("/api/yxStoreArticle")
public class AdminArticleController {

    private final YxStoreArticleService yxStoreArticleService;
    private final IGenerator generator;

    @Log("导出数据")
    @ApiOperation("导出数据")
    @GetMapping(value = "/download")
    @PreAuthorize("@el.check('admin','yxStoreArticle:list')")
    public void download(HttpServletResponse response, YxStoreArticleQueryCriteria criteria) throws IOException {
        yxStoreArticleService.download(
                generator.convert(yxStoreArticleService.queryAll(criteria), YxStoreArticleDto.class), response);
    }

    @GetMapping
    @Log("查询新闻")
    @ApiOperation("查询新闻")
    @PreAuthorize("@el.check('admin','yxStoreArticle:list')")
    public ResponseEntity<PageResult<YxStoreArticleDto>> getYxStoreArticles(YxStoreArticleQueryCriteria criteria,
            Pageable pageable) {
        return new ResponseEntity<>(yxStoreArticleService.queryAll(criteria, pageable), HttpStatus.OK);
    }

    @PostMapping
    @Log("新增新闻")
    @ApiOperation("新增新闻")
    @PreAuthorize("@el.check('admin','yxStoreArticle:add')")
    public ResponseEntity<Object> create(@Validated @RequestBody YxStoreArticle resources) {
        ResponseEntity<Object> ret = new ResponseEntity<>(yxStoreArticleService.save(resources), HttpStatus.CREATED);
        yxStoreArticleService.evictAll();
        return ret;
    }

    @PutMapping
    @Log("修改新闻")
    @ApiOperation("修改新闻")
    @PreAuthorize("@el.check('admin','yxStoreArticle:edit')")
    public ResponseEntity<Object> update(@Validated @RequestBody YxStoreArticle resources) {
        yxStoreArticleService.updateById(resources);
        yxStoreArticleService.evictAll();
        return new ResponseEntity<>(HttpStatus.NO_CONTENT);
    }

    @Log("删除新闻")
    @ApiOperation("删除新闻")
    @PreAuthorize("@el.check('admin','yxStoreArticle:del')")
    @DeleteMapping
    public ResponseEntity<Object> deleteAll(@RequestBody Long[] ids) {
        Arrays.asList(ids).forEach(id -> {
            yxStoreArticleService.removeById(id);
        });
        yxStoreArticleService.evictAll();
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
