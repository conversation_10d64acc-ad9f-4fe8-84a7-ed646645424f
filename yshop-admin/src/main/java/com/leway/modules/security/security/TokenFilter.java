/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>
 */
package com.leway.modules.security.security;

import com.leway.modules.security.config.SecurityProperties;
import com.leway.modules.security.service.AdminOnlineUserService;
import com.leway.modules.user.vo.OnlineUser;
import com.leway.utils.SpringContextHolder;
import com.leway.utils.StringUtils;
import io.jsonwebtoken.ExpiredJwtException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.web.filter.GenericFilterBean;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * <AUTHOR>
 */
@Slf4j
public class TokenFilter extends GenericFilterBean {

    private final TokenUtil tokenUtil;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    TokenFilter(TokenUtil tokenUtil) {
        this.tokenUtil = tokenUtil;
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;

        String requestRri = httpServletRequest.getRequestURI();

        // 验证 token 是否存在
        // OnlineUser onlineUser = null;
        String authToken = "";
        try {
            // SecurityProperties properties = SpringContextHolder.getBean(SecurityProperties.class);
            // OnlineUserService onlineUserService = SpringContextHolder.getBean(OnlineUserService.class);
            authToken = tokenUtil.getToken(httpServletRequest);
            if (authToken == null) {
                // log.info("authToken 为空: {}", requestRri);
                filterChain.doFilter(httpServletRequest, servletResponse);
                return;
            }
            // 从在线用户记录(redis)中读取用户是否存在
//            onlineUser = onlineUserService.getOne(properties.getOnlineKey() + authToken);
        } catch (ExpiredJwtException e) {
            log.error(e.getMessage());
        }

        String username = StringUtils.isNotBlank(authToken) ? tokenUtil.getUsernameFromToken(authToken) : null;
        if (/*onlineUser != null && */username != null && SecurityContextHolder.getContext().getAuthentication() == null
                && tokenUtil.validateToken(authToken)) {
            UserDetails userDetails = tokenUtil.getUserDetails(authToken);
            if (userDetails == null) {
                log.info("获取用户信息失败：{}\tname: {}", authToken, username);
                tokenUtil.removeToken(authToken);
            } else {
                UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(
                        userDetails, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(httpServletRequest));
                SecurityContextHolder.getContext().setAuthentication(authentication);
                // log.info("set Authentication to security context for '{}', uri: {}",
                // authentication.getName(), requestRri);
            }
        } else {
            // 集成登录请求时不删除token
            if (!StringUtils.equals("/user/auth", requestRri) &&
                    !StringUtils.equals("/function/findMenuByPNumber", requestRri) &&
                    !StringUtils.equals("/error", requestRri)) {
                tokenUtil.removeToken(authToken);
            }
            if (StringUtils.equals("/function/findMenuByPNumber", requestRri)) {
                log.info("findMenuByPNumber:{}", requestRri);
            }
            log.debug("no valid JWT token found, uri: {}", requestRri);
        }
        filterChain.doFilter(httpServletRequest, servletResponse);
    }
}
