/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>
 */
package com.leway.modules.security.rest;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import com.leway.annotation.AnonymousAccess;
import com.leway.api.YshopException;
import com.leway.exception.BadRequestException;
import com.leway.modules.logging.aop.log.Log;
import com.leway.modules.security.config.SecurityProperties;
import com.leway.modules.security.security.TokenUtil;
import com.leway.modules.security.security.vo.AuthUser;
import com.leway.modules.security.security.vo.JwtUser;
import com.leway.modules.security.service.AdminOnlineUserService;
import com.leway.modules.system.domain.AdminUser;
import com.leway.utils.RedisUtils;
import com.leway.utils.SecurityUtils;
import com.leway.utils.StringUtils;

//import com.leway.modules.erp.datasource.entities.ERPRole;
//import com.leway.modules.erp.service.user.ERPUserService;
//import com.leway.modules.erp.utils.Tools;
import com.wf.captcha.ArithmeticCaptcha;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018-11-23
 *       授权、根据token获取用户详细信息
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@Api(tags = "系统：系统授权接口")
public class AdminAuthController {

    @Value("${loginCode.expiration}")
    private Long expiration;
    @Value("${rsa.private_key}")
    private String privateKey;
    @Value("${single.login}")
    private Boolean singleLogin;
    private final SecurityProperties properties;
    private final RedisUtils redisUtils;
    private final UserDetailsService userDetailsService;
    private final AdminOnlineUserService onlineUserService;
    private final TokenUtil tokenUtil;
    private final AuthenticationManagerBuilder authenticationManagerBuilder;

//    private final ERPUserService userService;

    public AdminAuthController(SecurityProperties properties, RedisUtils redisUtils, UserDetailsService userDetailsService,
            AdminOnlineUserService onlineUserService, TokenUtil tokenUtil,
            AuthenticationManagerBuilder authenticationManagerBuilder) {
        this.properties = properties;
        this.redisUtils = redisUtils;
        this.userDetailsService = userDetailsService;
        this.onlineUserService = onlineUserService;
        this.tokenUtil = tokenUtil;
        this.authenticationManagerBuilder = authenticationManagerBuilder;
//        this.userService = userService;
    }

    @Log("用户登录")
    @ApiOperation("登录授权")
    @AnonymousAccess
    @PostMapping(value = "/login")
    public ResponseEntity<Object> login(@Validated @RequestBody AuthUser authUser, HttpServletRequest request) {
        // 密码解密
        RSA rsa = new RSA(privateKey, null);
        byte[] pwdBytes = null;
        try {
            pwdBytes = rsa.decrypt(authUser.getPassword(), KeyType.PrivateKey);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("密码解密失败: msg: {}\tpwd: {}", e.getMessage(), authUser.getPassword());
            throw new YshopException("用户名或密码错误");
        }
        String password = new String(pwdBytes);
        // 查询验证码
        String code = (String) redisUtils.get(authUser.getUuid());
        // 清除验证码
        redisUtils.del(authUser.getUuid());
        if (StringUtils.isBlank(code)) {
            throw new YshopException("验证码不存在或已过期");
        }
        if (StringUtils.isBlank(authUser.getCode()) || !authUser.getCode().equalsIgnoreCase(code)) {
            throw new YshopException("验证码错误");
        }
        UsernamePasswordAuthenticationToken authenticationToken = new UsernamePasswordAuthenticationToken(
                authUser.getUsername(), password);

        Authentication authentication;
        try {
            authentication = authenticationManagerBuilder.getObject().authenticate(authenticationToken);
        } catch (InternalAuthenticationServiceException e) {
            log.error("认证失败: {}", e.getMessage());
            throw new YshopException("用户名或密码错误");
        }
        SecurityContextHolder.getContext().setAuthentication(authentication);
        // 生成令牌
        final UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        String token = tokenUtil.generateToken(userDetails);

        final JwtUser jwtUser = (JwtUser) authentication.getPrincipal();
        // 保存在线信息
        onlineUserService.save(jwtUser, token, request);

        // 生成ERP系统必要的session信息
        // try {
        //     AdminUser adminUser = userService.getUserByLoginName(jwtUser.getUsername());
        //     redisUtils.storageObjectBySession(token, "userId", adminUser.getId());
        //     redisUtils.storageObjectBySession(token, "userNumLimit", 999); // 用户限制数

        //     ERPRole role = userService.getRoleTypeByUserId(adminUser.getId());
        //     if (role == null) {
        //         throw new RuntimeException("用户无对应角色");
        //     }
        //     String roleType = role.getType(); // 角色类型
        //     redisUtils.storageObjectBySession(token, "roleType", roleType);
        //     redisUtils.storageObjectBySession(token, "clientIp", Tools.getLocalIp(request));
        // } catch (Exception e) {
        //     e.printStackTrace();
        //     log.error("生成ERP系统必要的session信息失败: {}", e.getMessage());
        // }

        // 返回 token 与 用户信息
        Map<String, Object> authInfo = new HashMap<String, Object>(2) {
            {
                put("token", properties.getTokenStartWith() + token);
                put("user", jwtUser);
            }
        };
        if (singleLogin) {
            // 踢掉之前已经登录的token
            onlineUserService.checkLoginOnUser(authUser.getUsername(), token);
        }
        return ResponseEntity.ok(authInfo);
    }

    @ApiOperation("获取用户信息")
    @GetMapping(value = "/info")
    public ResponseEntity<Object> getUserInfo() {
        JwtUser jwtUser = (JwtUser) userDetailsService.loadUserByUsername(SecurityUtils.getUsername());
        return ResponseEntity.ok(jwtUser);
    }

    @AnonymousAccess
    @ApiOperation("获取验证码")
    @GetMapping(value = "/code")
    public ResponseEntity<Object> getCode() {
        // 算术类型 https://gitee.com/whvse/EasyCaptcha
        ArithmeticCaptcha captcha = new ArithmeticCaptcha(111, 36);
        // 几位数运算，默认是两位
        captcha.setLen(2);
        // 获取运算的结果
        String result = "";
        try {
            result = new Double(Double.parseDouble(captcha.text())).intValue() + "";
        } catch (Exception e) {
            result = captcha.text();
        }
        String uuid = properties.getCodeKey() + IdUtil.simpleUUID();
        // 保存
        redisUtils.set(uuid, result, expiration, TimeUnit.MINUTES);
        // 验证码信息
        Map<String, Object> imgResult = new HashMap<String, Object>(2) {
            {
                put("img", captcha.toBase64());
                put("uuid", uuid);
            }
        };
        return ResponseEntity.ok(imgResult);
    }

    @ApiOperation("退出登录")
    @AnonymousAccess
    @DeleteMapping(value = "/logout")
    public ResponseEntity<Object> logout(HttpServletRequest request) {
        onlineUserService.logout(tokenUtil.getToken(request));
        return new ResponseEntity<>(HttpStatus.OK);
    }
}
