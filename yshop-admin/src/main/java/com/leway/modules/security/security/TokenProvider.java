package com.leway.modules.security.security; /**
                                              * Copyright (C) 2019-2023
                                              * All rights reserved, <NAME_EMAIL>
                                              * 
                                              *//*
                                                 * 
                                                 * package com.leway.modules.security.security;
                                                 * 
                                                 * import com.leway.modules.security.config.SecurityProperties;
                                                 * import io.jsonwebtoken.Claims;
                                                 * import io.jsonwebtoken.ExpiredJwtException;
                                                 * import io.jsonwebtoken.Jwts;
                                                 * import io.jsonwebtoken.MalformedJwtException;
                                                 * import io.jsonwebtoken.SignatureAlgorithm;
                                                 * import io.jsonwebtoken.UnsupportedJwtException;
                                                 * import io.jsonwebtoken.io.Decoders;
                                                 * import io.jsonwebtoken.security.Keys;
                                                 * import lombok.extern.slf4j.Slf4j;
                                                 * import org.springframework.beans.factory.InitializingBean;
                                                 * import org.springframework.security.authentication.
                                                 * UsernamePasswordAuthenticationToken;
                                                 * import org.springframework.security.core.Authentication;
                                                 * import org.springframework.security.core.GrantedAuthority;
                                                 * import
                                                 * org.springframework.security.core.authority.SimpleGrantedAuthority;
                                                 * import org.springframework.security.core.userdetails.User;
                                                 * import org.springframework.stereotype.Component;
                                                 * 
                                                 * import javax.servlet.http.HttpServletRequest;
                                                 * import java.security.Key;
                                                 * import java.util.Arrays;
                                                 * import java.util.Collection;
                                                 * import java.util.Date;
                                                 * import java.util.stream.Collectors;
                                                 * 
                                                 */

/**
 * <AUTHOR>
 *//*
    * 
    * @Slf4j
    * 
    * @Component
    * public class TokenProvider implements InitializingBean {
    * 
    * private final SecurityProperties properties;
    * private static final String AUTHORITIES_KEY = "auth";
    * private Key key;
    * 
    * public TokenProvider(SecurityProperties properties) {
    * this.properties = properties;
    * }
    * 
    * 
    * @Override
    * public void afterPropertiesSet() {
    * byte[] keyBytes = Decoders.BASE64.decode(properties.getBase64Secret());
    * this.key = Keys.hmacShaKeyFor(keyBytes);
    * }
    * 
    * public String createToken(Authentication authentication) {
    * String authorities = authentication.getAuthorities().stream()
    * .map(GrantedAuthority::getAuthority)
    * .collect(Collectors.joining(","));
    * 
    * long now = (new Date()).getTime();
    * Date validity = new Date(now + properties.getTokenValidityInSeconds());
    * 
    * return Jwts.builder()
    * .setSubject(authentication.getName())
    * .claim(AUTHORITIES_KEY, authorities)
    * .signWith(key, SignatureAlgorithm.HS512)
    * .setExpiration(validity)
    * .compact();
    * }
    * 
    * Authentication getAuthentication(String token) {
    * Claims claims = Jwts.parser()
    * .setSigningKey(key)
    * .parseClaimsJws(token)
    * .getBody();
    * 
    * Collection<? extends GrantedAuthority> authorities =
    * Arrays.stream(claims.get(AUTHORITIES_KEY).toString().split(","))
    * .map(SimpleGrantedAuthority::new)
    * .collect(Collectors.toList());
    * 
    * User principal = new User(claims.getSubject(), "", authorities);
    * 
    * return new UsernamePasswordAuthenticationToken(principal, token,
    * authorities);
    * }
    * 
    * boolean validateToken(String authToken) {
    * try {
    * Jwts.parser().setSigningKey(key).parseClaimsJws(authToken);
    * return true;
    * } catch (io.jsonwebtoken.security.SecurityException | MalformedJwtException
    * e) {
    * log.info("Invalid JWT signature.");
    * e.printStackTrace();
    * } catch (ExpiredJwtException e) {
    * log.info("Expired JWT token.");
    * e.printStackTrace();
    * } catch (UnsupportedJwtException e) {
    * log.info("Unsupported JWT token.");
    * e.printStackTrace();
    * } catch (IllegalArgumentException e) {
    * log.info("JWT token compact of handler are invalid.");
    * e.printStackTrace();
    * }
    * return false;
    * }
    * 
    * public String getToken(HttpServletRequest request){
    * final String requestHeader = request.getHeader(properties.getHeader());
    * if (requestHeader != null &&
    * requestHeader.startsWith(properties.getTokenStartWith())) {
    * return requestHeader.substring(7);
    * }
    * return null;
    * }
    * }
    */
