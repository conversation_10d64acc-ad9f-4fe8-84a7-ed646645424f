
package com.leway.modules.security.security;

import com.leway.modules.security.config.SecurityProperties;
import com.leway.modules.security.security.vo.JwtUser;
import com.leway.utils.RedisUtils;
import com.leway.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.extern.slf4j.Slf4j;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Base64;
import java.util.Date;

/**
 * Token 工具类
 *
 * <AUTHOR>
 */

@Slf4j
@Component
public class TokenUtil {
    @Autowired
    private SecurityProperties properties;

    /**
     * 权限缓存前缀
     */
    private static final String REDIS_PREFIX_AUTH = "auth:";

    /**
     * 用户信息缓存前缀
     */
    private static final String REDIS_PREFIX_USER = "user-details:";

    /**
     * redis repository
     */
    @Autowired
    private RedisUtils redisUtils;

    /**
     * 获取用户名
     *
     * @param token Token
     * @return String
     */

    public String getUsernameFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.getSubject() : null;
    }

    /**
     * 获取过期时间
     *
     * @param token Token
     * @return Date
     */

    public Date getExpiredFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims != null ? claims.getExpiration() : null;
    }

    /**
     * 获得 Claims
     *
     * @param token Token
     * @return Claims
     */

    private Claims getClaimsFromToken(String token) {
        Claims claims;
        try {
            claims = Jwts.parser()
                    .setSigningKey(properties.getSecret())
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.warn("getClaimsFromToken exception", e);
            claims = null;
        }
        return claims;
    }

    /**
     * 计算过期时间
     *
     * @return Date
     */
    private Date generateExpired() {
        return new Date(System.currentTimeMillis() + properties.getTokenValidityInSeconds() * 1000);
    }

    /**
     * 判断 Token 是否过期
     *
     * @param token Token
     * @return Boolean
     */

    private Boolean isTokenExpired(String token) {
        Date expirationDate = getExpiredFromToken(token);
        return expirationDate.before(new Date());
    }

    /**
     * Admin 登录后台使用
     * 生成 Token
     * token算法来自于 配置文件的secret，userDetails中的username
     * key= redis_prefix + username + token
     *
     * @param userDetails 用户信息
     * @return String
     */
    public String generateToken(UserDetails userDetails) {
        String secret = properties.getSecret();
        String token = Jwts.builder()
                .setSubject(userDetails.getUsername())
                .setExpiration(generateExpired())
                .signWith(SignatureAlgorithm.HS512, secret)
                .compact();

        String key = REDIS_PREFIX_AUTH + userDetails.getUsername() + ":" + token;
        redisUtils.set(key, token, properties.getTokenValidityInSeconds() / 1000);
        putUserDetails(userDetails);
        return token;
    }

    /**
     * 验证 Token
     *
     * @param token Token
     * @return Boolean
     */
    public Boolean validateToken(String token) {
        final String username = getUsernameFromToken(token);
        String key = REDIS_PREFIX_AUTH + username + ":" + token;
        Object data = redisUtils.get(key);
        String redisToken = data == null ? null : data.toString();
        return StringUtils.isNotEmpty(token) && !isTokenExpired(token) && token.equals(redisToken);
    }

    /**
     * 移除 Token
     *
     * @param token Token
     */

    public void removeToken(String token) {
        final String username = getUsernameFromToken(token);
        String key = REDIS_PREFIX_AUTH + username + ":" + token;
        redisUtils.del(key);
        delUserDetails(username);
    }

    /**
     * 获得用户信息 Json 字符串
     *
     * @param token Token
     * @return String
     */
    protected String getUserDetailsString(String token) {
        final String username = getUsernameFromToken(token);
        String key = REDIS_PREFIX_USER + username;
        Object data = redisUtils.get(key);
        // log.info("getUserDetailsString获得用户信息. key:{}\tdata:{}", key, data);
        return data == null ? null : data.toString();
    }

    /**
     * 获得用户信息
     *
     * @param token Token
     * @return UserDetails
     */

    public UserDetails getUserDetails(String token) {
        String userDetailsString = getUserDetailsString(token);
        if (userDetailsString != null) {
            return new Gson().fromJson(userDetailsString, JwtUser.class);
        }
        return null;
    }

    /**
     * 存储用户信息userDetails json序列化后到redis中
     *
     * @param userDetails 用户信息
     */

    private void putUserDetails(UserDetails userDetails) {
        String key = REDIS_PREFIX_USER + userDetails.getUsername();
        boolean succ = redisUtils.set(key, new Gson().toJson(userDetails),
                properties.getTokenValidityInSeconds() / 1000);
        log.info("putUserDetails存储用户登录信息. key:{}\tsucc:{}", key, succ);
    }

    /**
     * 删除用户信息
     *
     * @param username 用户名
     */

    private void delUserDetails(String username) {
        String key = REDIS_PREFIX_USER + username;
        redisUtils.del(key);
        log.info("delUserDetails删除用户登录信息. key:{}", key);
    }

    public String getToken(HttpServletRequest request) {
        /**
         * 调试
         */
        final String HeaderOfAuthorization = request.getHeader(properties.getHeader());
        // log.info("getToken HeaderOfAuthorization: {}", HeaderOfAuthorization);
        if (HeaderOfAuthorization != null && HeaderOfAuthorization.startsWith(properties.getTokenStartWith())) {
            return HeaderOfAuthorization.substring(7);
        }
        return null;
    }

    public static void main(String[] args) {
        String key = Base64.getEncoder().encodeToString("123".getBytes());

        Claims claims;
//                = Jwts.parser().setSigningKey(key)
//                .parseClaimsJws(
//                        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX25hbWUiOiJhZG1pbiIsInNjb3BlIjpbImFwcCIsIndyaXRlIl0sInVpbiI6MSwiZXhwIjoxNTc1MDE1ODgzLCJhdXRob3JpdGllcyI6WyJST0xFX1VTRVIiXSwianRpIjoiYjdiYjQ1NTQtNTQ4OS00YTg5LWI3NjQtNzNjODI0YzljNGMyIiwiY2xpZW50X2lkIjoibHZoYWliYW8ifQ.x7QZxRAR1wuX_YNLi6EzRJ1iaKr1rIEUgjtYF0oSx5k")
//                .getBody();
//        System.out.println(JSON.toJSONString(claims));

        key = Base64.getEncoder().encodeToString("123".getBytes());
        String token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOjk4MjQsInNjb3BlIjo4LCJ1TmFtZSI6IjE5OTUxNjcwNTI2IiwiZXhwIjoxNzE1MDUzNjU2LCJpYXQiOjE3MTQ5ODE2NTZ9.MP_yIQNVoZvkVZtyQ58rYcw4ZJ_r-Ukms0LivGFZN3g";
        claims = Jwts.parser().setSigningKey(key)
                .parseClaimsJws(token)
                .getBody();
        System.out.println(JSON.toJSONString(claims));
    }
}
