/**
 * Copyright (C) 2019-2023
 * All rights reserved, <NAME_EMAIL>
 * 注意：
 * 本软件为************开发研制，未经购买不得使用
 * 一经发现盗用、分享等行为，将追究法律责任，后果自负
 */
package com.leway;

import java.io.IOException;
import java.util.Arrays;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ServletWebServerFactory;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.bind.annotation.RestController;

import com.binarywang.spring.starter.wxjava.miniapp.config.WxMaAutoConfiguration;
import com.leway.modules.erp.utils.ComputerInfo;
import com.leway.utils.SpringContextHolder;
import com.leway.utils.StringUtils;

import io.sentry.Sentry;
import io.sentry.SentryClient;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2018/11/15 9:20:19
 */
@Slf4j
@EnableAsync
@RestController
@SpringBootApplication(exclude = { WxMaAutoConfiguration.class })
@EnableTransactionManagement // 支持事务
// @MapperScan(basePackages = { "com.leway.modules.*.service.mapper",
// "com.leway.config",
// "com.leway.modules.erp.datasource.mappers" })
// 排除ERP
@MapperScan(basePackages = { "com.leway.modules.*.service.mapper", "com.leway.config" })
@ComponentScan(excludeFilters = @ComponentScan.Filter(type = FilterType.ASPECTJ, pattern = "com.leway.modules.erp..*"))
public class AdminAPIRun {
    private static SentryClient sentry;

    public static void main(String[] args) throws IOException {

        ConfigurableApplicationContext context = SpringApplication.run(AdminAPIRun.class, args);
        System.out.println(
                "\n" +
                        "             ___.   .__             __                        .__          \n" +
                        "  ____ _____ \\_ |__ |  |   ____   _/  |_  ____   _____ ______ |  |   ____  \n" +
                        "_/ ___\\\\__  \\ | __ \\|  | _/ __ \\  \\   __\\/ __ \\ /     \\\\____ \\|  | _/ __ \\ \n" +
                        "\\  \\___ / __ \\| \\_\\ \\  |_\\  ___/   |  | \\  ___/|  Y Y  \\  |_> >  |_\\  ___/ \n" +
                        " \\___  >____  /___  /____/\\___  >  |__|  \\___  >__|_|  /   __/|____/\\___  >\n" +
                        "     \\/     \\/    \\/          \\/             \\/      \\/|__|             \\/ " +

                        "\n后台API服务启动成功");
        Environment environment = context.getBean(Environment.class);
        String[] activeProfiles = environment.getActiveProfiles();

        // 判断 dev profile 是否激活
        boolean isDevProfileActive = Arrays.asList(activeProfiles).contains("dev")
                || Arrays.asList(activeProfiles).contains("test");

        if (!isDevProfileActive) {
            String dsn = environment.getProperty("sentry.dsn");
            log.info("sentry dsn:{}", dsn);
            // You can also manually provide the DSN to the ``init`` method.
            // String dsn =
            // "https://6cd7cd9c0903413c8dcfc2e5c3df0dbd:<EMAIL>/7";
            if (StringUtils.isNotEmpty(dsn)) {
                sentry = Sentry.init(dsn);
                log.info("sentry inited.");
            }
        }

        System.out.println("启动成功，后端服务API文档地址：http://" + ComputerInfo.getIpAddr() + ":"
                + environment.getProperty("server.port") + "/doc.html");

        // http://127.0.0.1:8001/druid/login.html
        System.out.println("启动成功，后端服务监控地址：http://" + ComputerInfo.getIpAddr() + ":"
                + environment.getProperty("server.port") + "/druid/login.html");
    }

    @Bean
    public SpringContextHolder springContextHolder() {
        return new SpringContextHolder();
    }

    @Bean
    public ServletWebServerFactory webServerFactory() {
        TomcatServletWebServerFactory fa = new TomcatServletWebServerFactory();
        fa.addConnectorCustomizers(connector -> connector.setProperty("relaxedQueryChars", "[]{}"));
        return fa;
    }

}

// @Api(tags = "ERP-登录集成接口")
// @Slf4j
// @Controller
// @RequestMapping("/")
// class ERPIntegrationController {
// @Value("${yshop.erpUrl}")
// private String erpUrl;
//
// private final TokenUtil tokenUtil;
//
// ERPIntegrationController(TokenUtil tokenUtil) {
// this.tokenUtil = tokenUtil;
// }
//
// /**
// * 访问首页提示
// *
// * @return /
// */
// @GetMapping()
// @AnonymousAccess
// public ResponseEntity<Object> index() {
// return new ResponseEntity<>("OK", HttpStatus.OK);
// }
//
// /*
// * 后台系统此时知道用户token，直接通过参数进行传入
// */
// @AnonymousAccess
// @GetMapping("/erp")
// public String doNothing(@RequestParam("erp_path") String path,
// @RequestParam("shop_token") String token) {
// String rawToken = token;
// int index = rawToken.indexOf(" ");
// if (index > 0) {
// rawToken = rawToken.substring(index + 1);
// }
// UserDetails userDetail = tokenUtil.getUserDetails(rawToken);
// String loginName = "-";
// if (userDetail != null) {
// loginName = userDetail.getUsername();
// }
//
// // HttpServletRequest req = ERPIntegrationController.getRequest();
// // String token = req.getHeader("Authorization");
// if (!StringUtils.startsWith(path, "/")) {
// path = "/" + path;
// }
//
// String url = erpUrl + path + "?shop_token=" + encode(token);
// String redirectStr = "redirect:" + url;
// log.debug("ERP集成跳转: {}\t用户: {}", redirectStr, loginName);
// return redirectStr;
// }
//
// public static HttpServletRequest getRequest() {
// ServletRequestAttributes ra = (ServletRequestAttributes)
// RequestContextHolder.getRequestAttributes();
// return ra.getRequest();
// }
//
// public static String encode(String url) {
// try {
//
// String encodeURL = URLEncoder.encode(url, "UTF-8");
//
// return encodeURL;
//
// } catch (UnsupportedEncodingException e) {
// return "Issue while encoding" + e.getMessage();
//
// }
//
// }
//
// }
