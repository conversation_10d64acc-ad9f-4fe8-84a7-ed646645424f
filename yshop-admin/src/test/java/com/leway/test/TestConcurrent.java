package com.leway.test;

import cn.hutool.core.util.RandomUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.leway.constant.ShopConstants;
import com.leway.enums.AfterSalesStateEnum;
import com.leway.enums.OrderInfoEnum;
import com.leway.enums.OrderRefundStatusEnum;
import com.leway.enums.PayTypeEnum;
import com.leway.enums.ShipperCodeEnum;
import com.leway.modules.order.domain.Order;
import com.leway.modules.order.vo.YxStoreOrderQueryVo;
import com.leway.modules.product.domain.SKU;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.product.service.TmpInventoryService;
import com.leway.modules.sales.domain.AfterSales;
import com.leway.modules.sales.param.StoreAfterSalesParam;

import com.leway.modules.tools.express.KDBirdExpressService;
import com.leway.utils.ArrayUtils;
import com.leway.utils.RedisLock;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@Slf4j
public class TestConcurrent extends AdminServiceTest {

    /**
     * mock快递鸟
     */
    @MockBean
    private KDBirdExpressService kdMock;

    @Autowired
    private ProductService productService;

    @Autowired
    private TmpInventoryService tmpInventoryService;

    @Autowired
    private RedisLock redisLock;

    List<Long> candidateIds = Arrays.asList(
            10L, 11L, 12L, 14L, 15L, 16L, 17L, 18L, 19L, 20L,
            31L, 32L, 33L, 34L, 35L, 36L, 37L, 38L, 39L, 40L,
            41L, 42L, 43L, 44L, 45L, 46L, 47L, 48L, 49L, 50L,
            51L, 52L, 53L, 54L, 55L, 56L, 57L, 58L, 59L, 60L,
            61L, 62L, 63L, 64L, 65L, 66L, 67L, 68L, 69L, 70L,
            71L, 72L, 73L, 74L, 75L, 76L, 77L, 78L, 79L, 80L);

    /**
     * 测试单规格在售库存的变化
     */
    @Test
    public void testSingleSkuStockChangeWhenOrderAndCancel() {
        SKU sku = getSingleSpecSKU();
        int stock = productService.getProductStock(singleSkuPid, sku.getUnique());
        log.info("单规格商品在售库存：{}", stock);
        Order order = makeSingleSpecOrder();
        int stockMinus1 = productService.getProductStock(singleSkuPid, sku.getUnique());

        assertEquals(stock - 1, stockMinus1, "实时库存减少");

        // 清理数据
        storeOrderService.cancelOrder(order.getOrderId(), getTmpUid());
        int stockReturned = productService.getProductStock(singleSkuPid, sku.getUnique());
        assertEquals(stock, stockReturned, "实时库存增加");
    }

    /**
     * 测试多规格实时库存的变化
     */
    @Test
    public void testMultiSkuStockChangeWhenOrderAndCancel() {
        List<SKU> skuList = getMultiSKU();
        Map<String, Integer> stockMap = new HashMap<>();
        for (SKU sku : skuList) {
            int stock = productService.getProductStock(multiSkuPid, sku.getUnique());
            log.info("多规格商品{}库存：{}", sku.getSku(), stock);
            stockMap.put(sku.getUnique(), stock);
        }

        Order order = makeMultiSpecOrder();

        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockMinus = productService.getProductStock(multiSkuPid, sku.getUnique());
            assertEquals(stock - 1, stockMinus, sku.getSku() + "实时库存减少");
        }

        // 清理数据
        storeOrderService.cancelOrder(order.getOrderId(), getTmpUid());
        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockReturned = productService.getProductStock(multiSkuPid, sku.getUnique());
            assertEquals(stock, stockReturned, sku.getSku() + "实时库存增加");
        }
    }

    /**
     * 退款库存回滚问题
     * 未实际发货
     */
    @Test
    public void testStockBack() {
        // 假设未签收
        when(kdMock.isSigned(anyString(), anyString(), anyString(),
                anyString())).thenReturn(false);

        // 3.2 检查库存
        List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
        SKU sku = skuList.get(0);

        int initSalesLimit = sku.getStock();
        int initSales = sku.getSales();
        List<Integer> tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
        int initTotalTmpStock = tmpStocks.stream() // 创建流
                .mapToInt(Integer::intValue) // 将Integer转换为int
                .sum(); // 对int进行求和

        // 0. 下单，待付款
        Order order = makeSingleSpecOrder();
        String orderIdStr = String.valueOf(order.getOrderId());
        log.info("单规格订单：{}", order);

        // 0.2 检查销售限额和销售量变化
        skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
        sku = skuList.get(0);

        int curSalesLimit = sku.getStock();
        int curSales = sku.getSales();
        assertEquals(initSales + 1, curSales, "销售量上涨");
        assertEquals(initSalesLimit - 1, curSalesLimit, "可售数量下降");

        tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
        int curTotalTmpStock = tmpStocks.stream() // 创建流
                .mapToInt(Integer::intValue) // 将Integer转换为int
                .sum(); // 对int进行求和

        assertEquals(initTotalTmpStock - 1, curTotalTmpStock, "临时库存下降");

        // 1. 支付
        storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

        YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
        assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

        // 2. 1小时后标记出库
        storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

        // 2. 发货
        storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                ShopConstants.DELIVERY_TYPE_EXPRESS);

        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
        assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
        assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(), "已发货");

        // 2. 申请售后
        StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
        afterSalesParam.setOrderCode(orderIdStr);
        afterSalesParam.setReasonForApplication("不想要了");
        afterSalesParam.setApplicationInstructions("不想要了");
        afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");
        afterSalesParam.setProductParamList(getOrderCartSkuList(order));

        afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

        // 此处mock掉快递签收查询
        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
        assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
        assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(), orderQueryVo.getRefundStatus(),
                "售后中");
        assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                "在已发货状态申请售后");

        LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                // 查找未完成的售后
                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                .orderByDesc(AfterSales::getId).last("limit 1");
        AfterSales afterSales = afterSalesService.getOne(wrapper);

        assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
        assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "正常状态");

        assertEquals(order.getPayPrice().intValue(),
                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");
        assertEquals(order.getPayPrice().intValue() - 10, afterSales.getRefundAmount().intValue(),
                "发生拦截，扣10块运费，偏远地区一样也退10块，不收取多收的5块");

        // 3. 批量审批
        // 3.1
        // afterSalesService.batchApproveUndeliveryOrders("system-test");
        String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
        // !!!处理退款
        storeOrderService.handleWechatRefundCallback(orderIdStr, afterSales.getRefundAmount(), outRefundNo);

        // 3.2 检查库存
        // 0.2 检查销售限额和销售量变化
        skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
        sku = skuList.get(0);

        curSalesLimit = sku.getStock();
        curSales = sku.getSales();
        assertEquals(initSales, curSales, "销售量回归");
        assertEquals(initSalesLimit, curSalesLimit, "可售数量回归");

        tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
        curTotalTmpStock = tmpStocks.stream() // 创建流
                .mapToInt(Integer::intValue) // 将Integer转换为int
                .sum(); // 对int进行求和

        assertEquals(initTotalTmpStock, curTotalTmpStock, "临时库存回归");

        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
        assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "已发货");
        assertEquals(OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue(), orderQueryVo.getRefundStatus(), "已退款");
    }

    /**
     * 退款库存回滚问题
     * 已有物流，在途
     */
    @Test
    public void testStockBack2() {
        when(kdMock.isSigned(anyString(), anyString(), anyString(),
                anyString())).thenReturn(false);

        // 3.2 检查库存
        List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
        SKU sku = skuList.get(0);

        int initSalesLimit = sku.getStock();
        int initSales = sku.getSales();

        List<Integer> tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
        int initTotalTmpStock = tmpStocks.stream() // 创建流
                .mapToInt(Integer::intValue) // 将Integer转换为int
                .sum(); // 对int进行求和

        // 0. 下单，待付款
        Order order = makeSingleSpecOrder();
        String orderIdStr = String.valueOf(order.getOrderId());
        log.info("单规格订单：{}", order);

        // 0.2 检查销售限额和销售量变化
        skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
        sku = skuList.get(0);

        int curSalesLimit = sku.getStock();
        int curSales = sku.getSales();
        assertEquals(initSales + 1, curSales, "销售量上涨");
        assertEquals(initSalesLimit - 1, curSalesLimit, "可售数量下降");

        tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
        int curTotalTmpStock = tmpStocks.stream() // 创建流
                .mapToInt(Integer::intValue) // 将Integer转换为int
                .sum(); // 对int进行求和

        assertEquals(initTotalTmpStock - 1, curTotalTmpStock, "临时库存下降");

        // 1. 支付
        storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

        YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
        assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

        // 2. 1小时后标记出库
        storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

        // 2. 发货
        storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                ShopConstants.DELIVERY_TYPE_EXPRESS);

        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
        assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
        assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(), "已发货");

        // 2. 申请售后
        StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
        afterSalesParam.setOrderCode(orderIdStr);
        afterSalesParam.setReasonForApplication("不想要了");
        afterSalesParam.setApplicationInstructions("不想要了");
        afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");
        afterSalesParam.setProductParamList(getOrderCartSkuList(order));

        afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
        assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
        assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(), orderQueryVo.getRefundStatus(),
                "售后中");
        assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                "在已发货在途状态申请售后");

        LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                // 查找未完成的售后
                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                .orderByDesc(AfterSales::getId).last("limit 1");
        AfterSales afterSales = afterSalesService.getOne(wrapper);

        assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType(), "退款类型");

        assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
        assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "正常状态");

        assertEquals(order.getPayPrice().intValue(),
                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");
        assertEquals(order.getPayPrice().intValue() - 10, afterSales.getRefundAmount().intValue(),
                "发生拦截，扣10块运费，偏远地区一样也退10块，不收取多收的5块");

        // 3. 批量审批
        // 3.1
        // afterSalesService.batchApproveUndeliveryOrders("system-test");

        // !!!处理退款
        String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
        storeOrderService.handleWechatRefundCallback(orderIdStr, afterSales.getRefundAmount(), outRefundNo);

        // 3.2 检查库存
        // 0.2 检查销售限额和销售量变化
        skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
        sku = skuList.get(0);

        curSalesLimit = sku.getStock();
        curSales = sku.getSales();

        tmpStocks = tmpInventoryService.getUnsyncedStock(sku.getUnique());
        curTotalTmpStock = tmpStocks.stream() // 创建流
                .mapToInt(Integer::intValue) // 将Integer转换为int
                .sum(); // 对int进行求和

        assertEquals(initTotalTmpStock, curTotalTmpStock, "临时库存回归");

        assertEquals(initSales, curSales, "销售量回归");
        assertEquals(initSalesLimit, curSalesLimit, "可售数量回归");

        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
        assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "已发货");
        assertEquals(OrderRefundStatusEnum.REFUND_STATUS_2_DONE.getValue(), orderQueryVo.getRefundStatus(), "已退款");
    }

    /**
     * 测试并发购买多sku商品，测试库存是否紊乱
     *
     * @throws InterruptedException
     */
    @Test
    public void testConcurrentMakeOrder() throws InterruptedException {
        List<SKU> skuList = getMultiSKU();
        Map<String, Integer> stockMap = new HashMap<>();
        for (SKU sku : skuList) {
            int stock = productService.getProductStock(multiSkuPid, sku.getUnique());
            log.info("商品{}\t库存：{}", sku.getSku(), stock);
            stockMap.put(sku.getUnique(), stock);
        }

        int numberOfThreads = 10;
        int taskCount = 60;
        ExecutorService service = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(taskCount);

        List<Order> orders = Collections.synchronizedList(new ArrayList<>());
        for (int i = 0; i < taskCount; i++) {
            CountDownLatch finalLatch = latch;
            int finalI = i;
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    Order order = makeOrder(multiSkuPid, 36l, 1, candidateIds.get(finalI));
                    orders.add(order);
                    // 你可以添加一些断言或者其他逻辑来检查结果
                    System.out.println("线程 " + Thread.currentThread().getName() + " 创建订单完成: " + order.getOrderId());
                } finally {
                    finalLatch.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池
        log.info("全部订单创建完成");

        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockMinus = productService.getProductStock(multiSkuPid, sku.getUnique());
            log.info("并发下单后\t商品{}\t库存：{}", sku.getSku(), stockMinus);
            assertEquals(stock - taskCount, stockMinus, sku.getSku() + "实时库存减少");
        }

        service = Executors.newFixedThreadPool(numberOfThreads);
        latch = new CountDownLatch(taskCount);
        for (int i = 0; i < taskCount; i++) {
            Order order = orders.get(i);
            CountDownLatch finalLatch1 = latch;
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    storeOrderService.cancelOrder(order.getOrderId(), order.getUid());
                    // 你可以添加一些断言或者其他逻辑来检查结果
                    System.out.println("线程 " + Thread.currentThread().getName() + " 取消订单完成: " + order.getOrderId());
                } finally {
                    finalLatch1.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池
        log.info("全部订单取消完成");

        // 在所有线程完成之后，你可以添加一些测试断言
        // 例如，检查结果的一致性或数据库的状态等

        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockReturned = productService.getProductStock(multiSkuPid, sku.getUnique());
            log.info("并发订单取消后\t商品{}\t库存：{}", sku.getSku(), stockReturned);
            assertEquals(stock, stockReturned, sku.getSku() + "实时库存恢复");
        }
    }

    /**
     * 测试并发购买单规格商品，测试库存是否紊乱
     *
     * @throws InterruptedException
     */
    @Test
    public void testConcurrentMakeSingleOrder() throws InterruptedException {
        List<SKU> skuList = Arrays.asList(getSingleSpecSKU());
        // 初始库存
        Map<String, Integer> stockMap = new HashMap<>();
        for (SKU sku : skuList) {
            int stock = productService.getProductStock(singleSkuPid, sku.getUnique());
            log.info("单规格商品{}库存：{}", sku.getSku(), stock);
            stockMap.put(sku.getUnique(), stock);
        }

        // 获取系统 CPU 核心数
        int processors = Runtime.getRuntime().availableProcessors();
        // // 设置线程数为 CPU 核心数
        // int numberOfThreads = processors;
        // 或者对于 I/O 密集型任务
        int numberOfThreads = processors * 2;
        int taskCount = 60; // 模拟的并发用户数量
        ExecutorService service = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(taskCount);

        List<Long> userIds = new ArrayList<>();
        // Random random = new Random();
        for (int i = 0; i < taskCount; i++) {
            // 从userIds中随机取一个用户id
            // long randomUid = candidateIds.get(random.nextInt(candidateIds.size()));
            long randomUid = candidateIds.get(i);
            userIds.add(randomUid);
        }

        CountDownLatch finalLatch = latch;
        List<Order> orders = Collections.synchronizedList(new ArrayList<>());
        for (int i = 0; i < taskCount; i++) {
            int finalI = i;
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    Order order = makeOrder(singleSkuPid, 36l, 1, userIds.get(finalI));
                    orders.add(order);
                    // 你可以添加一些断言或者其他逻辑来检查结果
                    System.out.println("线程 " + Thread.currentThread().getName() + " 创建订单完成: " + order.getOrderId());
                } catch (Exception e) {
                    // 失败则跳过
                    log.error("创建订单失败: {}", e.getMessage());
                    e.printStackTrace();
                } finally {
                    finalLatch.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池
        Set<String> orderIds = new HashSet<>();
        for (Order order : orders) {
            orderIds.add(order.getOrderId());
        }
        log.info("全部订单创建完成，共{}笔订单，去重后{}笔订单", orders.size(), orderIds.size());

        Map<String, Integer> stockAfterAllCreateMap = new HashMap<>();

        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockMinus = productService.getProductStock(singleSkuPid, sku.getUnique());
            log.info("并发下单后， sku: {} 库存：{}", sku.getSku(), stockMinus);
            assertEquals(stock - orders.size(), stockMinus, sku.getSku() + "实时库存减少");
            stockAfterAllCreateMap.put(sku.getSku(), stockMinus);
        }

        service = Executors.newFixedThreadPool(numberOfThreads);
        latch = new CountDownLatch(taskCount);
        CountDownLatch finalLatch1 = latch;
        List<Order> cancelFailedOrders = Collections.synchronizedList(new ArrayList<>());
        for (int i = 0; i < taskCount; i++) {
            Order order = orders.get(i);
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    storeOrderService.cancelOrder(order.getOrderId(), order.getUid());
                    // 你可以添加一些断言或者其他逻辑来检查结果
                    log.info("线程 " + Thread.currentThread().getName() + " 取消订单完成: " + order.getOrderId());
                } catch (Exception e) {
                    cancelFailedOrders.add(order);
                    // 失败则跳过
                    log.error("线程 " + Thread.currentThread().getName() + "取消订单失败：{}, err: {}", order.getOrderId(),
                            e.getMessage());
                } finally {
                    finalLatch1.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池

        // 在所有线程完成之后，你可以添加一些测试断言
        // 例如，检查结果的一致性或数据库的状态等
        for (SKU sku : skuList) {
            int initStock = stockMap.get(sku.getUnique());
            int finalStockReturned = productService.getProductStock(singleSkuPid, sku.getUnique());
            // !!! 任意的下单、取消订单失败，不影响总库存
            log.info("订单取消后， sku: {} 最终库存：{}", sku.getSku(), finalStockReturned);

            log.info("初始库存：{}，下单后库存：{}，取消后库存：{}", initStock, stockAfterAllCreateMap.get(sku.getSku()),
                    finalStockReturned);
            log.info("下单成功：{}笔订单，取消失败：{}笔订单", orders.size(), cancelFailedOrders.size());
            log.info("下单后库存: {}", initStock);
            log.info("取消后库存+失败库存: {}", finalStockReturned + cancelFailedOrders.size());
            assertEquals(initStock, finalStockReturned + cancelFailedOrders.size(), sku.getSku() + "实时库存恢复");
        }
    }

    /**
     * 混合创建订单和取消订单，测试库存
     *
     * @throws InterruptedException
     */
    @Test
    public void testConcurrentMakeOrderAndCancelSameTime() throws InterruptedException {
        List<SKU> skuList = getMultiSKU();
        Map<String, Integer> stockMap = new HashMap<>();
        for (SKU sku : skuList) {
            int stock = productService.getProductStock(multiSkuPid, sku.getUnique());
            log.info("商品{}\t库存：{}", sku.getSku(), stock);
            stockMap.put(sku.getUnique(), stock);
        }

        int numberOfThreads = 20; // 模拟的并发用户数量
        int taskCount = 60;
        int qps = 1000; // 每秒并发数

        ExecutorService service = Executors.newFixedThreadPool(numberOfThreads);
        CountDownLatch latch = new CountDownLatch(taskCount);

        List<Order> failOrders = Collections.synchronizedList(new ArrayList<>());
        List<Order> cancelFailOrders = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch finalLatch = latch;
        for (int i = 0; i < taskCount; i++) {
            int finalI = i;
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    Order order = null;
                    try {
                        // 计时开始
                        Long start = System.currentTimeMillis();
                        order = makeOrder(multiSkuPid, testAddressId, 1, candidateIds.get(finalI));
                        // 计时结束
                        Long end = System.currentTimeMillis();
                        System.out
                                .println("线程 " + Thread.currentThread().getName() + " 创建订单耗时: " + (end - start) + "ms");
                    } catch (Exception e) {
                        log.error("线程 " + Thread.currentThread().getName() + "创建订单失败: {}", e.getMessage());
                        failOrders.add(order);
                        e.printStackTrace();
                    } finally {
                        try {
                            Thread.sleep(1000 / qps);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                    }

                    try {
                        if (order != null) {
                            storeOrderService.cancelOrder(order.getOrderId(), order.getUid());
                            System.out.println(
                                    "线程 " + Thread.currentThread().getName() + " 取消订单完成: " + order.getOrderId());
                        }
                    } catch (Exception e) {
                        cancelFailOrders.add(order);
                        log.error("取消订单失败：{}", e.getMessage());
                    }
                } finally {
                    finalLatch.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池
        log.info("全部订单创建完成: 失败：{}/{}", failOrders.size(), numberOfThreads);

        // 在所有线程完成之后，你可以添加一些测试断言
        // 例如，检查结果的一致性或数据库的状态等
        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockReturned = productService.getProductStock(multiSkuPid, sku.getUnique());
            // log.info("并发订单取消后\t商品{}\t库存：{}", sku.getSku(), stockReturned);
            assertEquals(stock, stockReturned + cancelFailOrders.size(), sku.getSku() + "实时库存恢复");
        }
        for (Order order : cancelFailOrders) {
            storeOrderService.cancelOrder(order.getOrderId(), order.getUid());
        }
        log.info("cancelFailOrders: 失败: {}/{}", cancelFailOrders.size(), numberOfThreads - failOrders.size());
        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockReturned = productService.getProductStock(multiSkuPid, sku.getUnique());
            // log.info("并发订单取消后\t商品{}\t库存：{}", sku.getSku(), stockReturned);
            assertEquals(stock, stockReturned, sku.getSku() + "实时库存恢复");
        }
    }

    /**
     * 测试性能
     */
    @Test
    public void testPerformance() throws InterruptedException {
        List<SKU> skuList = getMultiSKU();
        Map<String, Integer> stockMap = new HashMap<>();
        for (SKU sku : skuList) {
            int stock = productService.getProductStock(multiSkuPid, sku.getUnique());
            log.info("商品{}\t库存：{}", sku.getSku(), stock);
            stockMap.put(sku.getUnique(), stock);
        }

        // M1 Max 10核心10线程，8个性能核心
        // 在Java中，获取当前系统的CPU线程数量（逻辑处理器的数量，即核心数乘以每个核心的线程数，有时称为“硬件线程”）
        int processors = Runtime.getRuntime().availableProcessors();
        // 逻辑线程数量
        log.info("Number of available processors (cores): " + processors);
        int numberOfThreads = processors * 4;
        int taskCount = 60;
        ExecutorService service = Executors.newFixedThreadPool(numberOfThreads);

        // 10个人，每人下1000单，一万单为性能基准
        int totalRepeat = 10;
        CountDownLatch latch = new CountDownLatch(taskCount);
        long startTime = System.currentTimeMillis();

        // 线程安全
        List<Order> orders = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch finalLatch = latch;
        for (int i = 0; i < taskCount; i++) {
            int finalI = i;
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    for (int j = 0; j < totalRepeat; j++) {
                        // 查看单笔执行时间
                        long start = System.currentTimeMillis();
                        try {
                            Order order = makeOrder(multiSkuPid, 36l, 1, candidateIds.get(finalI));
                            long end = System.currentTimeMillis();
                            System.out.println(
                                    "线程 " + Thread.currentThread().getName() + " 创建订单耗时: " + (end - start) + "ms");
                            orders.add(order);
                            // try {
                            // Thread.sleep(1);
                            // } catch (InterruptedException e) {
                            // }
                        } catch (Exception e) {
                            log.error("线程 " + Thread.currentThread().getName() + "创建订单失败: {}", e.getMessage());
                            e.printStackTrace();
                        }
                        // System.out.println("线程 " + Thread.currentThread().getName() + " 创建订单完成: " +
                        // order.getOrderId());
                    }
                } finally {
                    finalLatch.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池

        log.info("全部订单创建完成: {}", orders.size());
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println("执行花费时间: " + duration + " 毫秒");
        // 下单的TPS，保留1为小数输出
        double orderCreateTps = totalRepeat * taskCount * 1000 / duration;
        System.out.println("订单创建的TPS: " + orderCreateTps);

        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockMinus = productService.getProductStock(multiSkuPid, sku.getUnique());
            log.info("并发下单后\t商品{}\t库存：{}", sku.getSku(), stockMinus);
            assertEquals(stock - orders.size(), stockMinus, sku.getSku() + "库存减少");
        }

        startTime = System.currentTimeMillis();
        service = Executors.newFixedThreadPool(numberOfThreads);
        latch = new CountDownLatch(taskCount);

        List<Order> failOrderList = Collections.synchronizedList(new ArrayList<>());
        List<List<Order>> parts = ArrayUtils.splitListIntoEqualParts(orders, taskCount);
        CountDownLatch finalLatch1 = latch;
        for (int i = 0; i < taskCount; i++) {
            List<Order> part = parts.get(i);
            int finalI = i;
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    for (Order order : part) {
                        try {
                            // 查看单笔执行时间
                            long start = System.currentTimeMillis();
                            storeOrderService.cancelOrder(order.getOrderId(), order.getUid());
                            long end = System.currentTimeMillis();
                            System.out.println(
                                    "线程 " + Thread.currentThread().getName() + " 取消订单耗时: " + (end - start) + "ms");
                            // try {
                            // Thread.sleep(1);
                            // } catch (InterruptedException e) {
                            // }
                            // System.out.println("线程 " + Thread.currentThread().getName() + " 取消订单完成: " +
                            // order.getOrderId());
                        } catch (Exception e) {
                            failOrderList.add(order);
                            log.error("线程 " + Thread.currentThread().getName() + "取消订单失败: {}", e.getMessage());
                            e.printStackTrace();
                        }
                    }
                } finally {
                    finalLatch1.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池
        log.info("全部订单取消完成");

        endTime = System.currentTimeMillis();
        duration = endTime - startTime;
        System.out.println("执行花费时间: " + duration + " 毫秒");
        // 取消订单的TPS
        double cancelOrderTps = totalRepeat * taskCount * 1000 / duration;

        // 在所有线程完成之后，你可以添加一些测试断言
        // 例如，检查结果的一致性或数据库的状态等

        for (SKU sku : skuList) {
            int stock = stockMap.get(sku.getUnique());
            int stockReturned = productService.getProductStock(multiSkuPid, sku.getUnique());
            log.info("并发订单取消后\t商品{}\t库存：{}", sku.getSku(), stockReturned);
            assertEquals(stock, stockReturned + failOrderList.size(), sku.getSku() + "库存恢复正常");
        }
        // 创建失败
        int createFailCount = taskCount * totalRepeat - orders.size();
        System.out.println("创建失败订单数: " + createFailCount);
        // 取消失败
        int cancelFailCount = failOrderList.size();
        System.out.println("取消失败订单数: " + cancelFailCount);

        System.out.println("订单创建的TPS: " + orderCreateTps);
        System.out.println("取消订单的TPS: " + cancelOrderTps);
    }

    /**
     * 测试性能
     */
    @Test
    public void testPerformance2() throws InterruptedException {
        SKU sku = getSingleSpecSKU();
        Map<String, Integer> stockMap = new HashMap<>();
        int stock = productService.getProductStock(singleSkuPid, sku.getUnique());
        log.info("商品{}\t库存：{}", sku.getSku(), stock);
        stockMap.put(sku.getUnique(), stock);

        // M1 Max 10核心10线程，8个性能核心
        // 在Java中，获取当前系统的CPU线程数量（逻辑处理器的数量，即核心数乘以每个核心的线程数，有时称为“硬件线程”）
        int processors = Runtime.getRuntime().availableProcessors();
        // 逻辑线程数量
        log.info("Number of available processors (cores): " + processors);
        // 线程池多开一点
        int numberOfThreads = processors * 2;
        int taskCount = 60;
        ExecutorService service = Executors.newFixedThreadPool(numberOfThreads);

        // 10个人，每人下1000单，一万单为性能基准
        int totalRepeat = 100;
        CountDownLatch latch = new CountDownLatch(taskCount);
        long startTime = System.currentTimeMillis();

        // 线程安全
        CountDownLatch finalLatch = latch;
        List<Order> orders = Collections.synchronizedList(new ArrayList<>());
        for (int i = 0; i < taskCount; i++) {
            int finalI = i;
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    for (int j = 0; j < totalRepeat; j++) {
                        // 查看单笔执行时间
                        long start = System.currentTimeMillis();
                        try {
                            // 随机一个用户
                            // long uid = userIds.get(RandomUtil.randomInt(0, userIds.size() - 1));
                            // 购物车会互相干扰
                            long uid = candidateIds.get(finalI);
                            Order order = makeOrder(singleSkuPid, 36l, 1, uid);
                            long end = System.currentTimeMillis();
                            System.out.println(
                                    "线程 " + Thread.currentThread().getName() + " 创建订单耗时: " + (end - start) + "ms");
                            orders.add(order);
                            // try {
                            // Thread.sleep(1);
                            // } catch (InterruptedException e) {
                            // }
                        } catch (Exception e) {
                            log.error("线程 " + Thread.currentThread().getName() + "创建订单失败: {}", e.getMessage());
                            e.printStackTrace();
                        }
                        // System.out.println("线程 " + Thread.currentThread().getName() + " 创建订单完成: " +
                        // order.getOrderId());
                    }
                } finally {
                    finalLatch.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池

        log.info("全部订单创建完成: {}", orders.size());
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        System.out.println("执行花费时间: " + duration + " 毫秒");
        int createFailCount = (totalRepeat * taskCount) - orders.size();
        // 下单的TPS，保留1为小数输出
        double orderCreateTps = (totalRepeat * taskCount - createFailCount) * 1000 / duration;
        System.out.println("订单创建的TPS: " + orderCreateTps);

        stock = stockMap.get(sku.getUnique());
        int stockMinus = productService.getProductStock(singleSkuPid, sku.getUnique());
        log.info("并发下单后\t商品{}\t库存：{}", sku.getSku(), stockMinus);
        assertEquals(stock - orders.size(), stockMinus, sku.getSku() + "库存减少");

        startTime = System.currentTimeMillis();
        service = Executors.newFixedThreadPool(numberOfThreads);
        latch = new CountDownLatch(taskCount);

        List<Order> failOrderList = Collections.synchronizedList(new ArrayList<>());
        List<List<Order>> parts = ArrayUtils.splitListIntoEqualParts(orders, taskCount);
        CountDownLatch finalLatch1 = latch;
        for (int i = 0; i < taskCount; i++) {
            List<Order> part = parts.get(i);
            int finalI = i;
            service.execute(() -> {
                try {
                    // 这里调用你想要测试并发性的方法
                    for (Order order : part) {
                        try {
                            // 查看单笔执行时间
                            long start = System.currentTimeMillis();
                            storeOrderService.cancelOrder(order.getOrderId(), order.getUid());
                            long end = System.currentTimeMillis();
                            System.out.println(
                                    "线程 " + Thread.currentThread().getName() + " 取消订单耗时: " + (end - start) + "ms");
                            // try {
                            // Thread.sleep(1);
                            // } catch (InterruptedException e) {
                            // }
                            // System.out.println("线程 " + Thread.currentThread().getName() + " 取消订单完成: " +
                            // order.getOrderId());
                        } catch (Exception e) {
                            failOrderList.add(order);
                            log.error("线程 " + Thread.currentThread().getName() + "取消订单失败: {}", e.getMessage());
                            e.printStackTrace();
                        }
                    }
                } finally {
                    finalLatch1.countDown();
                }
            });
        }

        latch.await(); // 等待直到所有线程执行完毕
        service.shutdown(); // 关闭线程池
        log.info("全部订单取消完成");

        endTime = System.currentTimeMillis();
        duration = endTime - startTime;
        System.out.println("执行花费时间: " + duration + " 毫秒");
        // 取消订单的TPS
        double cancelOrderTps = totalRepeat * taskCount * 1000 / duration;

        // 在所有线程完成之后，你可以添加一些测试断言
        // 例如，检查结果的一致性或数据库的状态等

        stock = stockMap.get(sku.getUnique());
        System.out.println("初始库存: " + stock);
        System.out.println("批量创建订单后库存: " + (stock - orders.size()));

        System.out.println("取消失败订单数: " + failOrderList.size());
        int stockReturned = productService.getProductStock(singleSkuPid, sku.getUnique());
        System.out.println("最终库存: " + stockReturned);

        // log.info("并发订单取消后\t商品{}\t库存：{}", sku.getSku(), stockReturned);
        assertEquals(stock, stockReturned + failOrderList.size(), sku.getSku() + "库存恢复正常");

        // 创建失败
        System.out.println("创建失败订单数: " + createFailCount);
        // 取消失败
        int cancelFailCount = failOrderList.size();
        System.out.println("取消失败订单数: " + cancelFailCount);

        System.out.println("订单创建的TPS: " + orderCreateTps);
        System.out.println("订单创建的TPS预期是测试5倍，因为不用清空购物车: " + orderCreateTps * 5);
        System.out.println("取消订单的TPS: " + cancelOrderTps);
    }

}
