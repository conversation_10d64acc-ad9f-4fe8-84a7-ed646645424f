package com.leway.test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.leway.api.YshopException;
import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.service.WxPayService;
import com.leway.constant.ShopConstants;
import com.leway.enums.AfterSalesStateEnum;
import com.leway.enums.DepotEnum;
import com.leway.enums.OrderInfoEnum;
import com.leway.enums.OrderRefundStatusEnum;
import com.leway.enums.PayMethodEnum;
import com.leway.enums.PayTypeEnum;
import com.leway.enums.ShipperCodeEnum;
import com.leway.modules.cart.vo.YxStoreCartQueryVo;
import com.leway.modules.inventory.vo.InventoryTranscationParam;
import com.leway.modules.mp.config.WxPayConfiguration;
import com.leway.modules.order.domain.Order;
import com.leway.modules.order.domain.OrderCartItem;
import com.leway.modules.order.vo.YxStoreOrderQueryVo;
import com.leway.modules.product.domain.SKU;
import com.leway.modules.product.service.dto.SkuFormatDto;
import com.leway.modules.product.service.dto.StoreProductDto;
import com.leway.modules.product.vo.MaxSalesParam;
import com.leway.modules.product.vo.ProductQueryVo;
import com.leway.modules.product.vo.ProductVo;
import com.leway.modules.sales.domain.AfterSales;
import com.leway.modules.sales.param.SkuParam;
import com.leway.modules.sales.param.StoreAfterSalesParam;
import com.leway.modules.tools.express.KDBirdExpressService;
import com.leway.modules.user.domain.YxUser;
import com.leway.modules.user.domain.YxUserAddress;
import com.leway.utils.BigNum;

import cn.hutool.core.lang.Snowflake;
import lombok.extern.slf4j.Slf4j;

/**
 * 邮费
 */
@Slf4j
public class TestOrder extends AdminServiceTest {

        /**
         * mock快递鸟
         */
        @MockBean
        private KDBirdExpressService kdMock;

        /**
         * 普通地区
         */
        @Test
        public void testCommonArea() {
                ProductQueryVo product = productService.getStoreProductById(multiSkuPid);
                // 北京
                YxUserAddress address = userAddressService.getById(36);

                BigDecimal postage = storeOrderService.queryPostage(product, address);
                log.info("常规地区邮费: {}", postage);
        }

        /**
         * 偏远地区
         */
        @Test
        public void testOutlyingArea() {
                // 新疆
                YxUserAddress address = userAddressService.getById(73);
                ProductQueryVo product = productService.getStoreProductById(multiSkuPid);

                BigDecimal postage = storeOrderService.queryPostage(product, address);
                log.info("偏远地区邮费: {}", postage);
        }

        @Test
        public void testSnowFlake() {
                Snowflake sf = snowFlakeService.getMySnowFlake();
                Long id = sf.nextId();
                log.info("id: {}", id);
                log.info("worker id from conf: {}", workerId);

                long wid = Long.valueOf(workerId);
                assertEquals(wid, sf.getWorkerId(id), "worker id 可追踪");
        }

        /**
         * 测试修改地址
         */
        @Test
        public void testMultiSkuInsurePrice() {
                // 邮费只能平调或下调
                ProductQueryVo product = productService.getStoreProductById(multiSkuPid);
                BigDecimal insurePrice = product.getInsurePrice();

                // 0. 数据准备
                int num = 2;
                Order order = makeOrder(multiSkuPid, 36L, num);

                List<SKU> skuList = getMultiSKU();
                assertEquals(num * insurePrice.intValue() * skuList.size(), order.getInsurePrice().intValue(),
                                "订单保价金额");

                storeOrderService.cancelOrder(order.getOrderId(), getTmpUid());
        }

        /**
         * 测试修改地址
         */
        @Test
        public void testChangeAddressFail() {
                // 邮费只能平调或下调
                // 0. 数据准备
                Order order = makeSingleSpecOrder();
                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                BigDecimal postage = order.getPayPostage();
                log.info("订单：北京邮费:{}", postage);

                // 2. 修改地址为偏远地区，邮费为 5.00
                // 不允许
                try {
                        YxUserAddress addressXj = userAddressService.getById(73);
                        storeOrderService.changeAddress(order.getOrderId(), addressXj.getId());
                        log.info("修改地址成功");
                } catch (Exception e) {
                        log.error("邮费增加，不支持修改地址");
                }
        }

        @Test
        public void testChangeAddressSucc() {
                // 邮费只能平调或下调
                // 0. 数据准备
                Order order = makeSingleSpecOrder();
                BigDecimal postage = order.getPayPostage();
                log.info("订单：北京邮费:{}", postage);

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 修改地址
                // 允许
                YxUserAddress addressXj = userAddressService.getById(36);
                storeOrderService.changeAddress(order.getOrderId(), addressXj.getId());
                log.info("1修改地址成功");

                try {
                        storeOrderService.changeAddress(order.getOrderId(), addressXj.getId());
                        log.info("2修改地址成功");
                } catch (Exception e) {
                        log.error("修改地址失败，只能修改一次地址");
                }
        }

        /**
         * 测试单规格订单正常流程
         */
        @Test
        public void testSingleSkuPay() {
                // setProductLimit(singleSkuPid, 1);
                clearProductLimit(multiSkuPid);
                SKU sku = getSingleSpecSKU();
                int stock = productService.getProductStock(singleSkuPid, sku.getUnique());
                log.info("初始库存:{}", stock);

                // 0. 下单，待付款
                try {
                        Order order = makeSingleSpecOrder();
                        String orderIdStr = String.valueOf(order.getOrderId());
                        log.info("单规格订单：{}", order);

                        // 1. 支付
                        storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                        YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(),
                                        getTmpUid());
                        assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                        // 2. 1小时后标记出库
                        storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                        assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                        assertEquals(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue(),
                                        orderQueryVo.getDeliveryStatus(), "已出库");

                        // 3. 真实发货
                        storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                        ShopConstants.DELIVERY_TYPE_EXPRESS);

                        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                        assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                        assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(),
                                        orderQueryVo.getDeliveryStatus(), "已发货");

                        // 4. 用户确认收货
                        storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
                        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                        assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");
                } catch (Exception e) {
                        stock = productService.getProductStock(singleSkuPid, sku.getUnique());
                        log.info("下单限购异常后的库存：{}", stock);
                        log.error("单规格订单正常流程异常：{}", e.getMessage());
                }
        }

        /**
         * 测试多规格订单
         * 原价退款，包含多收的邮费
         */
        @Test
        public void testMultiSkuPay() {
                // 0. 下单，待付款
                Order order = makeMultiSpecOrder();
                String orderIdStr = String.valueOf(order.getOrderId());
                log.info("单规格订单：{}", order);

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已出库");

                // 3. 真实发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 4. 用户确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");
        }

        /**
         * 测试待发货：售后、撤销售后
         * 原价退款，包含多收的邮费
         */
        @Test
        public void testSingleSkuAfterSale1() {
                clearProductLimit(singleSkuPid);
                // 0. 下单，待付款
                Order order = makeSingleSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                log.info("单规格订单：{}", order);
                BigDecimal postage = order.getPayPostage();
                assertEquals(5, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");
                afterSalesParam.setProductParamList(getOrderCartSkuList(order));

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_NORMAL_0.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在待发货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");
                assertEquals(order.getPayPrice().intValue(), afterSales.getRefundAmount().intValue(), "全额退款");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_NORMAL_0.getValue(), orderQueryVo.getStatus(), "待发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试出库中：售后、撤销售后，扣十块
         */
        @Test
        public void testSingleSkuAfterSaleWhenOuting() {
                // 0. 下单，待付款
                Order order = makeSingleSpecOrder();
                String orderIdStr = String.valueOf(order.getOrderId());
                log.info("单规格订单：{}", order);

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已出库");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");
                afterSalesParam.setProductParamList(getOrderCartSkuList(order));

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "出库中状态申请售后");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue(), orderQueryVo.getDeliveryStatus(),
                                "出库中状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "正常状态");
                assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType(), "仅退款模式");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");
                assertEquals(order.getPayPrice().intValue() - 10, afterSales.getRefundAmount().intValue(),
                                "虽然没发生实际运送，但是已经在出库中，扣10块");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "待发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试已发货：售后、撤销售后
         * 扣10块运费
         */
        @Test
        public void testSingleSkuAfterSaleOnSent() {
                // 0. 下单，待付款
                Order order = makeSingleSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                log.info("单规格订单：{}", order);
                BigDecimal postage = order.getPayPostage();
                assertEquals(5, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");
                afterSalesParam.setProductParamList(getOrderCartSkuList(order));

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已发货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");
                assertEquals(order.getPayPrice().intValue() - 10, afterSales.getRefundAmount().intValue(),
                                "发生拦截，扣10块运费，偏远地区一样也退10块，不收取多收的5块");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试多规格已发货：部分售后、撤销售后
         * 未签收，只能全部退，准备拦截
         * 预先扣10块运费
         */
        @Test
        public void testMultiSkuAfterSaleOnSentAndNotSigned() {
                // 假设未签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(false);

                // 0. 下单，待付款
                Order order = makeMultiSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(5, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        // if (i < skuAfterSaleList.size() / 2) {
                        // skuParam.setQuantity(0);
                        // skuAfterSaleList.set(i, skuParam);
                        // keepUniques.add(skuParam.getProductAttrUnique());
                        // log.info("不退货 数量:{} unique: {}", skuParam.getQuantity(),
                        // skuParam.getProductAttrUnique());
                        // } else {
                        log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(), skuParam.getProductAttrUnique());
                        // }
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已发货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");

                List<OrderCartItem> cartItems = getOrderCartList(order);
                int refundAmount = 0;
                for (int i = 0; i < cartItems.size(); i++) {
                        OrderCartItem cartItem = cartItems.get(i);
                        YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                        if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                                refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                                log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                                                cartInfo.getProductAttrUnique(),
                                                cartInfo.getTruePrice() * cartInfo.getCartNum());
                        }
                }

                refundAmount = refundAmount + order.getPayPostage().intValue() - 10;
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "发生拦截，扣10块运费，偏远地区一样也退10块，不收取多收的5块");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试多规格已发货：部分售后、撤销售后
         * 已签收: 先改订单到完成状态再处理
         */
        @Test
        public void testMultiSkuAfterSaleOnSentAndSigned() {
                // 假设用户已经签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(true);

                // 0. 下单，待付款
                Order order = makeMultiSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(5, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(), skuParam.getProductAttrUnique());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 确定掉了快递鸟接口
                verify(kdMock).isSigned(anyString(), anyString(), anyString(),
                                anyString());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已发货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");
                assertEquals(AfterSales.TYPE_BOTH, afterSales.getServiceType());

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");

                List<OrderCartItem> cartItems = getOrderCartList(order);
                int refundAmount = 0;
                for (int i = 0; i < cartItems.size(); i++) {
                        OrderCartItem cartItem = cartItems.get(i);
                        YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                        if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                                refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                                log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                                                cartInfo.getProductAttrUnique(),
                                                cartInfo.getTruePrice() * cartInfo.getCartNum());
                        }
                }

                refundAmount = refundAmount + order.getPayPostage().intValue();
                // refundAmount = refundAmount + order.getPayPostage().intValue() - 10;
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "没有拦截，已签收，预期");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试多规格已发货：部分售后、撤销售后
         * 已签收: 先改订单到完成状态再处理
         */
        @Test
        public void testMultiSkuAfterSaleWhenDone() {
                // 假设用户已经签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(true);

                // 0. 下单，待付款
                Order order = makeMultiSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(5, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 3. 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");
                // assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(),
                // orderQueryVo.getDeliveryStatus(), "已发货");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        if (i < skuAfterSaleList.size() / 2) {
                                skuParam.setQuantity(0);
                                skuAfterSaleList.set(i, skuParam);
                                keepUniques.add(skuParam.getProductAttrUnique());
                                log.info("不退货 数量:{} unique: {}", skuParam.getQuantity(),
                                                skuParam.getProductAttrUnique());
                        } else {
                                log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(),
                                                skuParam.getProductAttrUnique());
                        }
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 确定掉了快递鸟接口
                verify(kdMock, times(0)).isSigned(anyString(), anyString(), anyString(),
                                anyString());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已完成状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");
                assertEquals(AfterSales.TYPE_BOTH, afterSales.getServiceType());

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");

                List<OrderCartItem> cartItems = getOrderCartList(order);
                int refundAmount = 0;
                for (int i = 0; i < cartItems.size(); i++) {
                        OrderCartItem cartItem = cartItems.get(i);
                        YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                        if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                                refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                                log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                                                cartInfo.getProductAttrUnique(),
                                                cartInfo.getTruePrice() * cartInfo.getCartNum());
                        }
                }

                refundAmount = refundAmount + order.getPayPostage().intValue();
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "没有拦截，已完成");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        @Test
        public void testSyncTmpInventory() {
                storeOrderService.syncTmpInventory("20231211");
        }

        /**
         * 向数据库中新增订单(石家庄)
         */
        @Test
        public void test5SJZOrder() {
                // 1. 生成石家庄订单
                Long sjzPid = 624L;
                List<Order> os = new ArrayList<>();
                int num = 1;
                for (int i = 0; i < num; i++) {
                        Order o = makeSingleSpecOrder(sjzPid);
                        os.add(o);
                }

                // 2. 批量发货
                for (Order o : os) {
                        // 支付
                        storeOrderService.paySuccess(o.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                        // 出库
                        storeOrderService.setDeliverOfOutDepot(o.getOrderId(), getTmpUid());
                }
                storeOrderService.batchMakeEOrder(ShopConstants.DEPOT_SHIJIAZHUANG_JD);
                log.info("批量发货完成");

                // 3. 取消订单
                for (Order o : os) {
                        boolean succ = storeOrderService.cancelJDEOrder(o.getOrderId());
                        log.info("取消订单：{} {}", o.getOrderId(), succ);
                }
        }

        /**
         * 向数据库中新增订单(北京)
         */
        @Test
        public void test5BJOrder() {
                Long bjPid = 623L;
                List<Order> os = new ArrayList<>();
                int num = 1;
                for (int i = 0; i < num; i++) {
                        Order o = makeSingleSpecOrder(bjPid);
                        os.add(o);
                }

                // 2. 批量发货
                for (Order o : os) {
                        // 支付
                        storeOrderService.paySuccess(o.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                        // 出库
                        storeOrderService.setDeliverOfOutDepot(o.getOrderId(), getTmpUid());
                }
                storeOrderService.batchMakeEOrder(ShopConstants.DEPOT_SHIJIAZHUANG_JD);
                log.info("批量发货完成");

                // 3. 顺丰订单不能取消
        }

        /**
         * 测试新增商品
         */
        @Test
        public void testInsertAndUpdate() {
                // 1. 新增商品
                StoreProductDto p = new StoreProductDto();
                p.setDescription("desc");

                // sku
                List<SkuFormatDto> attrs = new ArrayList<>();
                SkuFormatDto skuDto = new SkuFormatDto();

                // 这个设置不生效，销售限额还是0
                int curSalesLimit = 5;
                skuDto.setStock(curSalesLimit);
                skuDto.setPrice(1.0);
                // skuDto.setSalesLimit(curSalesLimit);

                attrs.add(skuDto);
                p.setAttrs(attrs);

                List<String> sliderImg = new ArrayList<>();
                sliderImg.add("https://foo.com/bar.jpg");
                p.setSliderImage(sliderImg);
                p.setImage("https://foo.com/bar.jpg");
                p.setStoreName("foo");
                p.setStoreInfo("foo");
                p.setKeyword("bar");
                p.setCateId("1");
                // p.setDepotId(0);
                p.setCate1stId("1");
                p.setRecImage("https://foo.com/bar.jpg");

                productService.insertAndEditYxStoreProduct(p);
                log.info("product id: {}", p.getId());

                List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", p.getId()));
                SKU sku = skuList.get(0);

                assertEquals(0, sku.getStock().intValue(), "商品创建后，sku在售库存为0");
                assertEquals(1, sku.getPrice().intValue(), "价格为1");
                assertEquals(0, sku.getSales().intValue(), "销量为0");

                // 2. 入库商品
                List<InventoryTranscationParam> transList = new ArrayList<>();
                InventoryTranscationParam transParam = new InventoryTranscationParam();
                transParam.setSkuId(sku.getId());
                transParam.setBatchNumber("1");
                transParam.setCost(BigDecimal.valueOf(5));
                transParam.setQuantity(5);
                transList.add(transParam);
                productService.stockIn(p.getId(), transList, 0L);

                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", p.getId()));
                sku = skuList.get(0);

                assertEquals(0, sku.getStock().intValue(), "sku销售限额不变");
                assertEquals(0, sku.getSales().intValue(), "销量为0");

                // 3. 更新销售限额到5
                List<MaxSalesParam> limitList = new ArrayList<>();
                MaxSalesParam msParam = new MaxSalesParam();
                msParam.setSkuId(sku.getId());
                msParam.setMaxSales(curSalesLimit);
                limitList.add(msParam);

                productService.stockSetMaxSaleLimit(p.getId(), limitList);

                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", p.getId()));
                sku = skuList.get(0);

                assertEquals(curSalesLimit, sku.getStock().intValue(), "更新库存后，sku在售库存为5");
                assertEquals(0, sku.getSales().intValue(), "销量为0");

                // 4. 购买一个商品，销售限额降为4
                Order order = makeSingleSpecOrder(p.getId());
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", p.getId()));
                sku = skuList.get(0);
                assertEquals(curSalesLimit - 1, sku.getStock().intValue(), "sku在售库存减少");
                assertEquals(1, sku.getSales().intValue(), "销量为1");

                // 5. 测试修改商品，不影响销售量和销售限额
                skuDto = new SkuFormatDto();
                // 此处不生效，限额保持为4
                skuDto.setStock(curSalesLimit);
                skuDto.setPrice(2.0);
                p.setAttrs(Arrays.asList(skuDto));

                productService.insertAndEditYxStoreProduct(p);

                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", p.getId()));
                sku = skuList.get(0);

                assertEquals(2, sku.getPrice().intValue(), "价格修改");
                assertEquals(curSalesLimit - 1, sku.getStock().intValue(), "sku在售库存不变");
                assertEquals(1, sku.getSales().intValue(), "销量不变");

                // 6. 报损商品 第一次，不允许
                try {
                        transList = new ArrayList<>();
                        transParam = new InventoryTranscationParam();
                        transParam.setSkuId(sku.getId());
                        transParam.setBatchNumber("1");
                        transParam.setCost(BigDecimal.valueOf(5));
                        transParam.setQuantity(2);
                        transList.add(transParam);
                        productService.stockWriteOff(p.getId(), transList, 0L);

                        skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", p.getId()));
                        sku = skuList.get(0);

                        assertEquals(0, sku.getStock().intValue(), "sku销售限额不变");
                        assertEquals(0, sku.getSales().intValue(), "销量为0");
                } catch (Exception e) {
                        log.error("报损商品1 失败: {}", e);
                }

                limitList = new ArrayList<>();
                msParam = new MaxSalesParam();
                msParam.setSkuId(sku.getId());
                // 更新销售限额到2
                msParam.setMaxSales(2);
                limitList.add(msParam);

                productService.stockSetMaxSaleLimit(p.getId(), limitList);

                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", p.getId()));
                sku = skuList.get(0);

                assertEquals(2, sku.getStock().intValue(), "更新库存后，sku在售库存为2");
                assertEquals(1, sku.getSales().intValue(), "销量为1");

                // 6. 报损商品
                try {
                        transList = new ArrayList<>();
                        transParam = new InventoryTranscationParam();
                        transParam.setSkuId(sku.getId());
                        transParam.setBatchNumber("1");
                        transParam.setCost(BigDecimal.valueOf(5));
                        transParam.setQuantity(2);
                        transList.add(transParam);
                        productService.stockWriteOff(p.getId(), transList, 0L);
                        log.info("报损商品2 成功");
                } catch (Exception e) {
                        log.error("超额报损商品失败: {}", e);
                }

                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", p.getId()));
                sku = skuList.get(0);

                assertEquals(2, sku.getStock().intValue(), "在售库存为2");
                assertEquals(1, sku.getSales().intValue(), "销量为1");
        }

//        @Test
//        public void testCancelJDOrder() {
//                // String did = "JDVA23970779463";
//                String orderId = null;
//                // orderId = "1732937811415269376";
//                // orderId = "1732938514846187520";
//                // orderId = "1732938522865696768";
//                // orderId = "1732941855596740608";
//                // orderId = "1732941847350738944";
//                orderId = "1732986042316750848";
//
//                boolean succ = storeOrderService.cancelJDEOrder(orderId);
//                log.info("取消订单：{} {}", orderId, succ);
//        }

        @Test
        public void test1minBefore() {
                Date nowOri = new Date();
                Date now = new Date();
                now.setTime(now.getTime() - 60 * 1000);
                log.info("now: {}\tnowOri: {}", now, nowOri);
        }

        @Test
        public void handleRefund() {
                try {
                        String xmlData = "<xml><return_code>SUCCESS</return_code><appid><![CDATA[wxa39c8d97a73a512f]]></appid><mch_id><![CDATA[1636528171]]></mch_id><nonce_str><![CDATA[d68ac140ffa86c2487cc8f2bf61158c4]]></nonce_str><req_info><![CDATA[9Gwdo+bYMfJSxDGJIgz2nTGTG01DyCaQBJsO3eJZ1KuUe0pC50pRPZj8zqlpD9u5sORTVLLfAM1GT9petpsb/u9IGpQXEAz7EwCxVk1Q9m3N1RT2rmF5IW1vspTg8GCoEJutG0OnDbSwGfUlki1iJFJIZ+h/Uc5oXWTRAHNDtLAbWBgLKzMT0pgv/SyybPaXiGWbPuDd62WTkKWeSDqOnAdhOKQ7tmbi5VD/B8bKf7jWr7e+ePj8UeHkP1xUeBPPOmqQsKH7iBcVTccMSePuh7NMvKtBWbfD8XBH39KxRAgNO3A2F8WXy2eLKKlfLT4jRFgMncEc0NATdtTMhsnaGRzIAtccfNq6vORD9FYGMuaFdLTXoCzQxezCaor/PRPd3VfglqTR4ew8G2N5lDPhDQpttw/FLODU+e9b+5ujSbgID/dihpz7HlIBGTl/5xvofA4gmtWM6HZAFwc0+r9RM1TS0mIFf2JBt5Y6SjR98F+VLrYWSrGbKSW0MU8nOpJUDsM9F/15inBT6oVL3jrDuGDSotaYzkaRdTVRTF/SEuUbgg0ScqxfGvRkZQwZVPKpj3CNVqcqCNvS0v3U6ID4eNnuStGaT6mIrkZN1E7F8pAAUGUdBbDoSWOTtg1kSIbW0h/oOE4YiRGo5bYYjy14t08ELL08ZXWuDrhQkJfQQ5Rim3UyckRKzwzXgomWnFFlLeYRWwd9xkPAJM9e8uWgxApoT36m*****************************************+JZjictNZnzCJeM+6q/BRZcp4uSqEY1TraXoToGJ+0sVvw/OvCN7lcqGDvo30+z7CowwJQe60KUo/bXw6ABzJ4ztsIhV6wX4VNfcX5oA6Nl/gqSWgL5lftvkVYX/J7KqnYOmLOgsTRxgPVL48CZSPf9cVPsgahumdHt1WR0fcryVsLs+DZWcd/S0/mOMHeS5sAmkBXgoMqyM0u6NPyzWq9i9J+FEyIouCWsEgxgnlzgYuA84cdov/ZgiXo0otqi53u/QcX6oj1SXYFRkBgpleXftjJ1r42jjcqao6YQSClludbdt5Od9xksNke9e+xDZWO2PMq8vgdcr5C/FJi3/rPAiNeygIkKW2fXLT4=]]></req_info></xml>";
                        WxPayService wxPayService = WxPayConfiguration.getPayService(PayMethodEnum.WXAPP);
                        WxPayRefundNotifyResult result = wxPayService.parseRefundNotifyResult(xmlData);
                        log.info("result", result);
                } catch (Exception e) {
                        log.error("parse xml error");
                }
        }

        /**
         * 测试退货退款，库存的变化时机
         * 客服通过售后时，退货退款
         */
        @Test
        public void testInventoryWhenReturn() {
                // 假设已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(true);

                int totalNum = 2;
                int refundNum = 1;

                SKU sku = getSingleSKU(singleSkuPid);
                int salesLimit = sku.getStock();
                int sales = sku.getSales();

                // 0. 下单，待付款
                Order order = makeOrder(singleSkuPid, testAddressId, totalNum);
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(0, postage.intValue(), "包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 3. 用户确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");

                sku = getSingleSKU(singleSkuPid);
                int salesLimit1 = sku.getStock();
                int sales1 = sku.getSales();

                assertEquals(salesLimit, salesLimit1 + totalNum, "限售数量降低");
                assertEquals(sales, sales1 - totalNum, "销售数量增加");
                //
                //
                // part 2 售后
                //
                //

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        // 退1个
                        skuParam.setQuantity(1);
                        log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(), skuParam.getProductAttrUnique());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已收货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_BOTH, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(),
                                "支付金额 = 货品价格 + 邮费");

                // 应退金额
                // List<OrderCartItem> cartItems = getOrderCartList(order);
                // int refundAmount = 0;
                // for (int i = 0; i < cartItems.size(); i++) {
                // OrderCartItem cartItem = cartItems.get(i);
                // YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                // if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                // refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                // log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                // cartInfo.getProductAttrUnique(),
                // cartInfo.getTruePrice() * cartInfo.getCartNum());
                // }
                // }

                int refundAmount = sku.getPrice().intValue() * refundNum;
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "部分退款");

                // 3. 同意售后
                String customSupport1 = "客服1";
                afterSalesService.salesCheck(afterSales.getId(), afterSales.getOrderCode(), AfterSales.APPROVED,
                                "幻觉贸易客服部", "17600083957", "石家庄幻觉贸易总部", customSupport1, null);

                // 4. 客户回寄商品
                String code = "JD";
                String name = ShipperCodeEnum.JD.name();
                String postalCode = "JDVB21910829961";
                String phone = "18601296102";
                Boolean succ = afterSalesService.addLogisticsInformation(code, name, postalCode, phone,
                                afterSales.getOrderCode());
                log.info("客户已经寄回商品: {}", succ);

                // 5. 客服确认商品无误，入库、退款
                // 5.1 售后标记退款、订单标记退款
                // 5.2 微信支付退款
                // 5.3 库存回退

                BigDecimal refundFee = afterSales.getRefundAmount();

                // !!!假装微信回调了已经
                String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                sku = getSingleSKU(singleSkuPid);
                int salesLimit2 = sku.getStock();
                int sales2 = sku.getSales();

                int actualSale = (totalNum - refundNum);
                log.info("实际销售数量: {}", actualSale);

                assertEquals(salesLimit - actualSale, salesLimit2, "限售数量变化");
                assertEquals(sales + actualSale, sales2, "销售数量变化");
        }

        /**
         * 测试退货退款且报损
         */
        @Test
        public void testLoss() {
                // 假设已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(true);

                // 0. 下单，待付款
                Order order = makeSingleSpecOrder();
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(0, postage.intValue(), "包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 3. 用户确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");

                //
                //
                // part 2 售后
                //
                //

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(), skuParam.getProductAttrUnique());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已收货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_BOTH, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(),
                                "支付金额 = 货品价格 + 邮费");

                // 应退金额
                List<OrderCartItem> cartItems = getOrderCartList(order);
                int refundAmount = 0;
                for (int i = 0; i < cartItems.size(); i++) {
                        OrderCartItem cartItem = cartItems.get(i);
                        YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                        if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                                refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                                log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                                                cartInfo.getProductAttrUnique(),
                                                cartInfo.getTruePrice() * cartInfo.getCartNum());
                        }
                }

                refundAmount = refundAmount + order.getPayPostage().intValue();
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "全额退款");

                // 3. 同意售后
                String customSupport1 = "客服1";
                afterSalesService.salesCheck(afterSales.getId(), afterSales.getOrderCode(), AfterSales.APPROVED,
                                "幻觉贸易客服部", "17600083957", "石家庄幻觉贸易总部", customSupport1, null);

                // 4. 客户回寄商品
                String code = "JD";
                String name = ShipperCodeEnum.JD.name();
                String postalCode = "JDVB21910829961";
                String phone = "18601296102";
                Boolean succ = afterSalesService.addLogisticsInformation(code, name, postalCode, phone,
                                afterSales.getOrderCode());
                log.info("客户已经寄回商品: {}", succ);

                // 获取当前库存
                Long productId = 9L;

                List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", productId));
                SKU sku = skuList.get(0);
                int stock = sku.getStock();
                int sales = sku.getSales();

                // 5. 客服确认商品无误，入库、退款
                // 5.1 售后标记退款、订单标记退款
                // 5.2 微信支付退款
                // 5.3 库存回退

                afterSalesService.returnMoney2UserAndReportLoss(afterSales.getId(), orderQueryVo.getOrderId(),
                                "系统");

                BigDecimal refundFee = afterSales.getRefundAmount();

                // !!!假装微信回调了已经
                String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", productId));
                sku = skuList.get(0);
                int newStock = sku.getStock();
                int newSales = sku.getSales();
                assertEquals(stock, newStock, "库存不变");
                assertEquals(sales, newSales + 1, "销量降低");
        }

        /**
         * 测试仅退款，先扣除10元，后续退回扣除的10元
         */
        @Test
        public void test10Return() {
                // 假设未签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(false);

                int totalNum = 2;

                SKU sku = getSingleSKU(singleSkuPid);
                int salesLimit = sku.getStock();
                int sales = sku.getSales();

                // 0. 下单，待付款
                Order order = makeOrder(singleSkuPid, testAddressId, totalNum);
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(0, postage.intValue(), "包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                sku = getSingleSKU(singleSkuPid);
                int salesLimit1 = sku.getStock();
                int sales1 = sku.getSales();

                assertEquals(salesLimit, salesLimit1 + totalNum, "限售数量降低");
                assertEquals(sales, sales1 - totalNum, "销售数量增加");
                //
                //
                // part 2 售后
                //
                //

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        // 退全部
                        skuParam.setQuantity(skuParam.getQuantity());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(),
                                "支付金额 = 货品价格 + 邮费");

                int shouldRefundPrice = sku.getPrice().intValue() * totalNum - 10;
                assertEquals(shouldRefundPrice, afterSales.getRefundAmount().intValue(), "部分退款");

                // 3. 同意售后
                String customSupport1 = "客服1";
                afterSalesService.salesCheck(afterSales.getId(), afterSales.getOrderCode(), AfterSales.APPROVED,
                                "幻觉贸易客服部", "17600083957", "石家庄幻觉贸易总部", customSupport1, null);

                // 4. 第一次退款
                BigDecimal refundFee = afterSales.getRefundAmount();

                List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int stockBeforeRefund = sku.getStock();
                int salesBeforeRefund = sku.getSales();

                // !!!假装微信回调了已经
                String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue(), "退款金额");

                // 库存回退
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock = sku.getStock();
                int newSales = sku.getSales();
                assertEquals(stockBeforeRefund + totalNum, newStock, "库存增加");
                assertEquals(salesBeforeRefund - totalNum, newSales, "销量减少");

                // 5. 第二次退款(10块)
                String outRefundNo2 = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), new BigDecimal("10"), outRefundNo2);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue() + 10, "退款金额");

                // 库存不变
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock2 = sku.getStock();
                int newSales2 = sku.getSales();
                assertEquals(newStock, newStock2, "库存不变");
                assertEquals(newSales, newSales2, "销量不变");

                // 6. 第三次退款(10块)
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), new BigDecimal("10"), outRefundNo2);

                // orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(),
                // getTmpUid());
                // assertEquals(orderQueryVo.getRefundPrice().intValue(),
                // sku.getPrice().intValue()*totalNum, "退款金额");

                // 库存不变
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock3 = sku.getStock();
                int newSales3 = sku.getSales();
                assertEquals(newStock, newStock3, "库存不变");
                assertEquals(newSales, newSales3, "销量不变");

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), sku.getPrice().intValue() * totalNum, "退款金额");
        }

        /**
         * 测试仅退款，先扣除10元，后续退回扣除的10元
         * 有额外邮费的情况
         */
        @Test
        public void test10Return2() {
                // 假设未签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(false);

                int totalNum = 2;

                SKU sku = getSingleSKU(singleSkuPid);
                int salesLimit = sku.getStock();
                int sales = sku.getSales();

                // 乌鲁木齐
                Long specialAddressId = 73L;
                // 石家庄
                Long freeAddressId = 74L;
                YxUser user = userService.getById(10L);
                tmpUser = user;

                userAddressService.setDefault(user.getUid(), specialAddressId);

                ProductVo productVo = productService.goodsDetail(singleSkuPid, getTmpUid(), null, null);
                ProductQueryVo spu = productVo.getStoreInfo();
                spu.setDepotCity(DepotEnum.getCityById(1));
                YxUserAddress address = userAddressService.getById(specialAddressId);
                BigDecimal postage = storeOrderService.queryPostage(spu, address);

                // 0. 下单，待付款
                Order order = makeOrder(singleSkuPid, specialAddressId, totalNum, getTmpUid());
                String orderIdStr = String.valueOf(order.getOrderId());
                assertEquals(postage.intValue(), order.getPayPostage().intValue(), "邮费");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                sku = getSingleSKU(singleSkuPid);
                int salesLimit1 = sku.getStock();
                int sales1 = sku.getSales();

                assertEquals(salesLimit, salesLimit1 + totalNum, "限售数量降低");
                assertEquals(sales, sales1 - totalNum, "销售数量增加");
                //
                //
                // part 2 售后
                //
                //

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        // 退全部
                        skuParam.setQuantity(skuParam.getQuantity());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(),
                                "支付金额 = 货品价格 + 邮费");

                int shouldRefundPrice = sku.getPrice().intValue() * totalNum + postage.intValue() - 10;
                assertEquals(shouldRefundPrice, afterSales.getRefundAmount().intValue(), "部分退款");

                // 3. 同意售后
                String customSupport1 = "客服1";
                afterSalesService.salesCheck(afterSales.getId(), afterSales.getOrderCode(), AfterSales.APPROVED,
                                "幻觉贸易客服部", "17600083957", "石家庄幻觉贸易总部", customSupport1, null);

                // 4. 第一次退款
                BigDecimal refundFee = afterSales.getRefundAmount();

                List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int stockBeforeRefund = sku.getStock();
                int salesBeforeRefund = sku.getSales();

                // !!!假装微信回调了已经
                String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue(), "退款金额");

                // 库存回退
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock = sku.getStock();
                int newSales = sku.getSales();
                assertEquals(stockBeforeRefund + totalNum, newStock, "库存增加");
                assertEquals(salesBeforeRefund - totalNum, newSales, "销量减少");

                // !!!处理退款回调幂等问题
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue(), "退款金额");

                // 库存回退
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                newStock = sku.getStock();
                newSales = sku.getSales();
                assertEquals(stockBeforeRefund + totalNum, newStock, "库存增加");
                assertEquals(salesBeforeRefund - totalNum, newSales, "销量减少");

                // 5. 第二次退款(10块)
                String outRefundNo2 = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), new BigDecimal("10"), outRefundNo2);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue() + 10, "退款金额");

                // 库存不变
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock2 = sku.getStock();
                int newSales2 = sku.getSales();
                assertEquals(newStock, newStock2, "库存不变");
                assertEquals(newSales, newSales2, "销量不变");

                // 6. 第三次退款(10块)
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), new BigDecimal("10"), outRefundNo2);

                // orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(),
                // getTmpUid());
                // assertEquals(orderQueryVo.getRefundPrice().intValue(),
                // sku.getPrice().intValue()*totalNum, "退款金额");

                // 库存不变
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock3 = sku.getStock();
                int newSales3 = sku.getSales();
                assertEquals(newStock, newStock3, "库存不变");
                assertEquals(newSales, newSales3, "销量不变");

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(),
                                sku.getPrice().intValue() * totalNum + order.getTotalPostage().intValue(), "退款金额");

                userAddressService.setDefault(user.getUid(), freeAddressId);
        }
}
