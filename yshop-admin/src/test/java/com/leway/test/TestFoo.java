package com.leway.test;

//import com.leway.modules.user.domain.YxUserAddress;

import com.leway.modules.product.domain.Product;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.services.CreateShareProductService;
import com.leway.modules.system.service.MenuService;
import com.leway.modules.system.service.dto.MenuQueryCriteria;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.awt.*;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

@Slf4j
public class TestFoo extends AdminServiceTest {

    @Autowired
    CreateShareProductService createShareProductService;

    @Autowired
    ProductService productService;

    @Autowired
    MenuService yxMenuService;


    @Test
    public void testMenuCache() {
        MenuQueryCriteria q = new MenuQueryCriteria();
        yxMenuService.queryAll(q);
    }

    @Test
    public void testSort() {
        List<String> list = new ArrayList<String>();
        list.add("1-2");
        list.add("2-4,1-2");
        list.add("1-2,2-4");
        list.add("2-5");
        list.add("2-3");

        list.sort(Comparator.naturalOrder());

        log.info("list after sort: {}", String.join("|", list));
    }

    @Test
    public void testRemove() {
//        YxUserAddress a = new YxUserAddress();
//        a.setCity("1");
//        a.setUid(2L);
//        boolean succ = userAddressService.save(a);
//        _addressIDs.add(a.getId());
//        log.info("create succ:{} id:{}", succ, a.getId());
//
//        succ = userAddressService.removeById(a.getId());
//        log.info("remove succ:{}", succ);
    }

    @Test
    public void testPoster() throws IOException, FontFormatException {

        // 1. 获取商品信息
        Long id = 622l;
        Product storeProduct = productService.getById(id);

        createShareProductService.creatCableProductPic(storeProduct, "SC01", "分享图", "/Users/<USER>/Downloads/poster.jpg", "http://baidu.com");
    }


}
