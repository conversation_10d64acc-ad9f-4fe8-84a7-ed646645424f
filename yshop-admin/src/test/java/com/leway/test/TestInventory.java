package com.leway.test;

import static org.junit.Assert.assertNotNull;

import org.junit.After;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import com.leway.modules.inventory.domain.InventoryRecord;
import com.leway.modules.inventory.domain.InventoryTransaction;
import com.leway.modules.inventory.service.InventoryRecordService;
import com.leway.modules.inventory.service.InventoryTransactionService;

import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 测试库存管理
 */
@Slf4j
public class TestInventory extends AdminServiceTest {

    @Autowired
    private InventoryRecordService inventoryRecordService;

    @Autowired
    private InventoryTransactionService inventoryTransactionService;

    private List<Long> createdInventoryRecordIds = new ArrayList<>();
    private List<Long> createdInventoryTransactionIds = new ArrayList<>();

    @Test
    public void testFoo() {
    }

    @After
    public void tearDown() {
        // 删除创建的记录
        // 例如：删除库存操作记录和库存记录
        // 注意：应该首先删除依赖项，然后删除主项
        // 首先删除库存操作记录
        for (Long transactionId : createdInventoryTransactionIds) {
            inventoryTransactionService.deleteTransaction(transactionId);
            log.info("删除库存操作记录: {}", transactionId);
        }

        // 然后删除库存记录
        for (Long recordId : createdInventoryRecordIds) {
            inventoryRecordService.deleteInventoryRecord(recordId);
            log.info("删除库存记录: {}", recordId);
        }
    }

    @Test
    public void testInventoryOperations() {
        // 创建并保存库存记录
        InventoryRecord record = new InventoryRecord();
        record.setSpuId(1L); // 示例SPU ID
        record.setSkuId(1L); // 示例SKU ID
        record.setCurrentQuantity(100);
        record.setWarehouseLocation("仓库A");
        InventoryRecord savedRecord = inventoryRecordService.saveInventoryRecord(record);
        createdInventoryRecordIds.add(savedRecord.getInventoryId());

        // 验证库存记录已保存
        assertNotNull(savedRecord);
        assertNotNull(savedRecord.getInventoryId());

        // 创建并保存库存操作记录
        InventoryTransaction transaction = new InventoryTransaction();
        transaction.setInventoryId(savedRecord.getInventoryId());
        transaction.setOperatorId(1L); // 示例操作员ID
        transaction.setBatchNumber("BATCH-2023-01");
        transaction.setTransactionType("入库");
        transaction.setQuantity(100);
        transaction.setTransactionDate(new Date());
        transaction.setSourceOrDestination("供应商A");
        transaction.setRemarks("备注信息");
        InventoryTransaction savedTransaction = inventoryTransactionService.saveTransaction(transaction);
        createdInventoryTransactionIds.add(savedTransaction.getTransactionId());

        // 验证库存操作记录已保存
        assertNotNull(savedTransaction);
        assertNotNull(savedTransaction.getTransactionId());
    }

}
