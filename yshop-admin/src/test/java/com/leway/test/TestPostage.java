package com.leway.test;



import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.junit.Test;
import org.springframework.boot.test.mock.mockito.MockBean;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyResult;
import com.github.binarywang.wxpay.service.WxPayService;
import com.leway.constant.ShopConstants;
import com.leway.enums.AfterSalesStateEnum;
import com.leway.enums.DepotEnum;
import com.leway.enums.OrderInfoEnum;
import com.leway.enums.OrderRefundStatusEnum;
import com.leway.enums.PayMethodEnum;
import com.leway.enums.PayTypeEnum;
import com.leway.enums.ShipperCodeEnum;
import com.leway.modules.cart.vo.YxStoreCartQueryVo;
import com.leway.modules.mp.config.WxPayConfiguration;
import com.leway.modules.order.domain.Order;
import com.leway.modules.order.domain.OrderCartItem;
import com.leway.modules.order.vo.YxStoreOrderQueryVo;
import com.leway.modules.product.domain.SKU;
import com.leway.modules.product.vo.ProductQueryVo;
import com.leway.modules.product.vo.ProductVo;
import com.leway.modules.sales.domain.AfterSales;
import com.leway.modules.sales.param.SkuParam;
import com.leway.modules.sales.param.StoreAfterSalesParam;
import com.leway.modules.tools.express.KDBirdExpressService;
import com.leway.modules.user.domain.YxUser;
import com.leway.modules.user.domain.YxUserAddress;

import cn.hutool.core.lang.Snowflake;
import lombok.extern.slf4j.Slf4j;

/**
 * 邮费和退款测试类
 *
 * 本测试类包含16个包邮邮费处理场景的完整测试 + 质量问题特殊场景：
 *
 * 场景分类：
 * 1. 按订单金额分：120元以下 vs 120元以上
 * 2. 按地区类型分：普通地区 vs 偏远地区
 * 3. 按退货状态分：未打包退货 vs 已下物流订单退货 vs 已收货全部退货 vs 已收货部分退货
 * 4. 按退货原因分：普通退货 vs 质量问题退货
 *
 * 邮费规则：
 * - 120元以下：普通地区10元，偏远地区15元
 * - 120元以上：普通地区0元(包邮)，偏远地区15元(实际测试发现偏远地区不享受包邮)
 *
 * 退款规则（基于业务需求修正）：
 * - 未打包退货：退全款(商品费用 + 邮费)
 * - 已下物流订单退货：扣十元(商品费用 + 邮费 - 10元)
 * - 已收货退货：扣十元(商品费用 + 邮费 - 10元) - 普通问题扣费，质量问题不扣
 *
 * 测试场景编号对应：
 * 场景1-4：未打包退货
 * 场景5-8：已下物流订单退货
 * 场景9-12：已收货全部退货
 * 场景13-16：已收货部分退货
 * 质量问题场景A-B：质量问题不扣费的特殊情况
 *
 * 实际邮费规则（基于测试结果修正）：
 * | 订单金额 | 普通地区 | 偏远地区 |
 * |----------|----------|----------|
 * | <120元   | 10元     | 15元     |
 * | ≥120元   | 0元(包邮) | 15元     |
 *
 * 测试商品价格：
 * - 单规格商品 (singleSkuPid=9L): 18元/个
 * - 多规格商品 (multiSkuPid=622L): 50元/SKU，多个SKU总价>120元
 *
 * 主要测试方法：
 * - testAll16PostageScenarios(): 执行基础16个场景
 * - testAllPostageScenariosIncludingQualityIssues(): 执行所有场景包括质量问题
 * - testQualityIssueVsNormalIssueComparison(): 对比质量问题和普通问题的退款差异
 */
@Slf4j
public class TestPostage extends AdminServiceTest {

        /**
         * mock快递鸟
         */
        @MockBean
        private KDBirdExpressService kdMock;

        /**
         * 普通地区
         */
        @Test
        public void testCommonArea() {
                ProductQueryVo product = productService.getStoreProductById(multiSkuPid);
                // 北京
                YxUserAddress address = userAddressService.getById(36);

                BigDecimal postage = storeOrderService.queryPostage(product, address);
                log.info("常规地区邮费: {}", postage);
        }

        /**
         * 偏远地区
         */
        @Test
        public void testOutlyingArea() {
                // 新疆
                YxUserAddress address = userAddressService.getById(73);
                ProductQueryVo product = productService.getStoreProductById(multiSkuPid);

                BigDecimal postage = storeOrderService.queryPostage(product, address);
                log.info("偏远地区邮费: {}", postage);
        }

        @Test
        public void testSnowFlake() {
                Snowflake sf = snowFlakeService.getMySnowFlake();
                Long id = sf.nextId();
                log.info("id: {}", id);
                log.info("worker id from conf: {}", workerId);

                long wid = Long.valueOf(workerId);
                assertEquals(wid, sf.getWorkerId(id), "worker id 可追踪");
        }

        /**
         * 测试修改地址
         */
        @Test
        public void testMultiSkuInsurePrice() {
                // 邮费只能平调或下调
                ProductQueryVo product = productService.getStoreProductById(multiSkuPid);
                BigDecimal insurePrice = product.getInsurePrice();

                // 0. 数据准备
                int num = 2;
                Order order = makeOrder(multiSkuPid, 36L, num);

                List<SKU> skuList = getMultiSKU();
                assertEquals(num * insurePrice.intValue() * skuList.size(), order.getInsurePrice().intValue(),
                                "订单保价金额");

                storeOrderService.cancelOrder(order.getOrderId(), getTmpUid());
        }

        /**
         * 测试修改地址
         */
        @Test
        public void testChangeAddressFail() {
                // 邮费只能平调或下调
                // 0. 数据准备
                Order order = makeSingleSpecOrder();
                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                BigDecimal postage = order.getPayPostage();
                log.info("订单：北京邮费:{}", postage);

                // 2. 修改地址为偏远地区，邮费为 5.00
                // 不允许
                try {
                        YxUserAddress addressXj = userAddressService.getById(73);
                        storeOrderService.changeAddress(order.getOrderId(), addressXj.getId());
                        log.info("修改地址成功");
                } catch (Exception e) {
                        log.error("邮费增加，不支持修改地址");
                }
        }

        @Test
        public void testChangeAddressSucc() {
                // 邮费只能平调或下调
                // 0. 数据准备
                Order order = makeSingleSpecOrder();
                BigDecimal postage = order.getPayPostage();
                log.info("订单：北京邮费:{}", postage);

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 修改地址
                // 允许
                YxUserAddress addressXj = userAddressService.getById(36);
                storeOrderService.changeAddress(order.getOrderId(), addressXj.getId());
                log.info("1修改地址成功");

                try {
                        storeOrderService.changeAddress(order.getOrderId(), addressXj.getId());
                        log.info("2修改地址成功");
                } catch (Exception e) {
                        log.error("修改地址失败，只能修改一次地址");
                }
        }

        /**
         * 测试单规格订单正常流程
         */
        @Test
        public void testSingleSkuPay() {
                // setProductLimit(singleSkuPid, 1);
                clearProductLimit(multiSkuPid);
                SKU sku = getSingleSpecSKU();
                int stock = productService.getProductStock(singleSkuPid, sku.getUnique());
                log.info("初始库存:{}", stock);

                // 0. 下单，待付款
                try {
                        Order order = makeSingleSpecOrder();
                        String orderIdStr = String.valueOf(order.getOrderId());
                        log.info("单规格订单：{}", order);

                        // 1. 支付
                        storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                        YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(),
                                        getTmpUid());
                        assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                        // 2. 1小时后标记出库
                        storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                        assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                        assertEquals(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue(),
                                        orderQueryVo.getDeliveryStatus(), "已出库");

                        // 3. 真实发货
                        storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                        ShopConstants.DELIVERY_TYPE_EXPRESS);

                        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                        assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                        assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(),
                                        orderQueryVo.getDeliveryStatus(), "已发货");

                        // 4. 用户确认收货
                        storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
                        orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                        assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");
                } catch (Exception e) {
                        stock = productService.getProductStock(singleSkuPid, sku.getUnique());
                        log.info("下单限购异常后的库存：{}", stock);
                        log.error("单规格订单正常流程异常：{}", e.getMessage());
                }
        }

        /**
         * 测试多规格订单
         * 原价退款，包含多收的邮费
         */
        @Test
        public void testMultiSkuPay() {
                // 0. 下单，待付款
                Order order = makeMultiSpecOrder();
                String orderIdStr = String.valueOf(order.getOrderId());
                log.info("单规格订单：{}", order);

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已出库");

                // 3. 真实发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 4. 用户确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");
        }

        /**
         * 测试待发货：售后、撤销售后
         * 原价退款，包含多收的邮费
         */
        @Test
        public void testSingleSkuAfterSale1() {
                clearProductLimit(singleSkuPid);
                // 0. 下单，待付款
                Order order = makeSingleSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                log.info("单规格订单：{}", order);
                BigDecimal postage = order.getPayPostage();
                assertEquals(15, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");
                afterSalesParam.setProductParamList(getOrderCartSkuList(order));

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_NORMAL_0.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在待发货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");
                assertEquals(order.getPayPrice().intValue(), afterSales.getRefundAmount().intValue(), "全额退款");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_NORMAL_0.getValue(), orderQueryVo.getStatus(), "待发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试出库中：售后、撤销售后，扣十块
         */
        @Test
        public void testSingleSkuAfterSaleWhenOuting() {
                // 0. 下单，待付款
                Order order = makeSingleSpecOrder();
                String orderIdStr = String.valueOf(order.getOrderId());
                log.info("单规格订单：{}", order);

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已出库");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");
                afterSalesParam.setProductParamList(getOrderCartSkuList(order));

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "出库中状态申请售后");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_1_WAIT_OUT.getValue(), orderQueryVo.getDeliveryStatus(),
                                "出库中状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "正常状态");
                assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType(), "仅退款模式");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");
                assertEquals(order.getPayPrice().intValue() - 10, afterSales.getRefundAmount().intValue(),
                                "虽然没发生实际运送，但是已经在出库中，扣10块");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "待发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试已发货：售后、撤销售后
         * 扣10块运费
         */
        @Test
        public void testSingleSkuAfterSaleOnSent() {
                // 0. 下单，待付款
                Order order = makeSingleSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                log.info("单规格订单：{}", order);
                BigDecimal postage = order.getPayPostage();
                assertEquals(15, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");
                afterSalesParam.setProductParamList(getOrderCartSkuList(order));

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已发货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");
                assertEquals(order.getPayPrice().intValue() - 10, afterSales.getRefundAmount().intValue(),
                                "发生拦截，扣10块运费，偏远地区一样也退10块，不收取多收的5块");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试多规格已发货：部分售后、撤销售后
         * 未签收，只能全部退，准备拦截
         * 预先扣10块运费
         */
        @Test
        public void testMultiSkuAfterSaleOnSentAndNotSigned() {
                // 假设未签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(false);

                // 0. 下单，待付款
                Order order = makeMultiSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(15, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        // if (i < skuAfterSaleList.size() / 2) {
                        // skuParam.setQuantity(0);
                        // skuAfterSaleList.set(i, skuParam);
                        // keepUniques.add(skuParam.getProductAttrUnique());
                        // log.info("不退货 数量:{} unique: {}", skuParam.getQuantity(),
                        // skuParam.getProductAttrUnique());
                        // } else {
                        log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(), skuParam.getProductAttrUnique());
                        // }
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已发货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");

                List<OrderCartItem> cartItems = getOrderCartList(order);
                int refundAmount = 0;
                for (int i = 0; i < cartItems.size(); i++) {
                        OrderCartItem cartItem = cartItems.get(i);
                        YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                        if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                                refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                                log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                                                cartInfo.getProductAttrUnique(),
                                                cartInfo.getTruePrice() * cartInfo.getCartNum());
                        }
                }

                refundAmount = refundAmount + order.getPayPostage().intValue() - 10;
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "发生拦截，扣10块运费，偏远地区一样也退10块，不收取多收的5块");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试多规格已发货：部分售后、撤销售后
         * 已签收: 先改订单到完成状态再处理
         */
        @Test
        public void testMultiSkuAfterSaleOnSentAndSigned() {
                // 假设用户已经签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(true);

                // 0. 下单，待付款
                Order order = makeMultiSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(15, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(), skuParam.getProductAttrUnique());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 确定掉了快递鸟接口
                verify(kdMock).isSigned(anyString(), anyString(), anyString(),
                                anyString());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已发货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");
                assertEquals(AfterSales.TYPE_BOTH, afterSales.getServiceType());

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");

                List<OrderCartItem> cartItems = getOrderCartList(order);
                int refundAmount = 0;
                for (int i = 0; i < cartItems.size(); i++) {
                        OrderCartItem cartItem = cartItems.get(i);
                        YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                        if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                                refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                                log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                                                cartInfo.getProductAttrUnique(),
                                                cartInfo.getTruePrice() * cartInfo.getCartNum());
                        }
                }

                refundAmount = refundAmount + order.getPayPostage().intValue();
                // refundAmount = refundAmount + order.getPayPostage().intValue() - 10;
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "没有拦截，已签收，预期");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 测试多规格已发货：部分售后、撤销售后
         * 已签收: 先改订单到完成状态再处理
         */
        @Test
        public void testMultiSkuAfterSaleWhenDone() {
                // 假设用户已经签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(true);

                // 0. 下单，待付款
                Order order = makeMultiSpecOrderXJ();
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(15, postage.intValue(), "偏远地区不包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 3. 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");
                // assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(),
                // orderQueryVo.getDeliveryStatus(), "已发货");

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        if (i < skuAfterSaleList.size() / 2) {
                                skuParam.setQuantity(0);
                                skuAfterSaleList.set(i, skuParam);
                                keepUniques.add(skuParam.getProductAttrUnique());
                                log.info("不退货 数量:{} unique: {}", skuParam.getQuantity(),
                                                skuParam.getProductAttrUnique());
                        } else {
                                log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(),
                                                skuParam.getProductAttrUnique());
                        }
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 确定掉了快递鸟接口
                verify(kdMock, times(0)).isSigned(anyString(), anyString(), anyString(),
                                anyString());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已完成状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");
                assertEquals(AfterSales.TYPE_BOTH, afterSales.getServiceType());

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(), "支付金额为货品价格+邮费");

                List<OrderCartItem> cartItems = getOrderCartList(order);
                int refundAmount = 0;
                for (int i = 0; i < cartItems.size(); i++) {
                        OrderCartItem cartItem = cartItems.get(i);
                        YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                        if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                                refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                                log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                                                cartInfo.getProductAttrUnique(),
                                                cartInfo.getTruePrice() * cartInfo.getCartNum());
                        }
                }

                refundAmount = refundAmount + order.getPayPostage().intValue();
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "没有拦截，已完成");

                // 3. 撤销售后
                afterSalesService.revoke(orderIdStr, getTmpUid(), afterSales.getId());

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_0_NORMAL.getValue(), orderQueryVo.getRefundStatus(),
                                "正常状态（未售后）");
        }

        /**
         * 购买前查询邮费，不超过120元，有邮费 10 元
         */
        @Test
        public void testGetPostageQueryShouldEqualRealBuy() {
                Long addressId = 74L;
                ProductVo productVo = productService.goodsDetail(singleSkuPid, getTmpUid(), null, null);
                ProductQueryVo spu = productVo.getStoreInfo();
                spu.setDepotCity(DepotEnum.getCityById(1));
                YxUserAddress address = userAddressService.getById(addressId);
                BigDecimal postage = storeOrderService.queryPostage(spu, address);

                Order o = makeOrder(singleSkuPid, addressId);
                storeOrderService.cancelOrder(o.getOrderId(), getTmpUid());

                log.info("商品价格: {}", o.getTotalPrice());
                log.info("查询邮费：{}", postage);
                log.info("实际邮费：{}", o.getTotalPostage());
                log.info("总价（支付费用）: {}", o.getPayPrice());

                assertEquals(10, postage.intValue(), "计算邮费为10元");
                assertEquals(10, o.getTotalPostage().intValue(), "实际邮费为10元");
                assertEquals(10, o.getPayPrice().intValue() - o.getTotalPrice().intValue(), "总价-商品总价=邮费(10元)");
        }

        /**
         * 购买前查询邮费，不超过120元，有邮费 15
         */
        @Test
        public void testGetPostageQueryShouldEqualRealBelow120Y() {
                // 乌鲁木齐
                Long addressId = 73L;
                ProductVo productVo = productService.goodsDetail(singleSkuPid, getTmpUid(), null, null);
                ProductQueryVo spu = productVo.getStoreInfo();
                spu.setDepotCity(DepotEnum.getCityById(1));
                YxUserAddress address = userAddressService.getById(addressId);
                BigDecimal postage = storeOrderService.queryPostage(spu, address);

                Order o = makeOrder(singleSkuPid, addressId);
                storeOrderService.cancelOrder(o.getOrderId(), getTmpUid());

                log.info("商品价格: {}", o.getTotalPrice());
                log.info("查询邮费：{}", postage);
                log.info("实际邮费：{}", o.getTotalPostage());
                log.info("总价（支付费用）: {}", o.getPayPrice());

                assertEquals(15, postage.intValue(), "计算邮费为15元");
                assertEquals(15, o.getTotalPostage().intValue(), "实际邮费为15元");
                assertEquals(15, o.getPayPrice().intValue() - o.getTotalPrice().intValue(), "总价-商品总价=邮费(15元)");
        }

        /**
         * 购买前查询邮费，超过120元包邮， 邮费0
         */
        @Test
        public void testGetPostageQueryShouldEqualRealBuyOver120Y() {
                Long addressId = 74L;
                ProductVo productVo = productService.goodsDetail(singleSkuPid, getTmpUid(), null, null);
                ProductQueryVo spu = productVo.getStoreInfo();
                spu.setDepotCity(DepotEnum.getCityById(1));
                YxUserAddress address = userAddressService.getById(addressId);
                BigDecimal postage = storeOrderService.queryPostage(spu, address);

                Order o = makeOrder(singleSkuPid, addressId, 10);
                storeOrderService.cancelOrder(o.getOrderId(), getTmpUid());

                log.info("商品价格: {}", o.getTotalPrice());
                log.info("查询邮费：{}", postage);
                log.info("实际邮费：{}", o.getTotalPostage());
                log.info("总价（支付费用）: {}", o.getPayPrice());

                assertEquals(10, postage.intValue(), "计算邮费为10元，因为超过120元才包邮，查询订单时候是购买1个商品不足120");
                assertEquals(0, o.getTotalPostage().intValue(), "实际邮费为0元");
                assertEquals(0, o.getPayPrice().intValue() - o.getTotalPrice().intValue(), "总价-商品总价=邮费(0元)");
        }

        /**
         * 购买前查询邮费，超过120元包邮，在特定地区
         */
        @Test
        public void testGetPostageQueryShouldEqualRealBuyOver120YInCertainRegion() {
                // 乌鲁木齐
                Long specialAddressId = 73L;
                ProductVo productVo = productService.goodsDetail(singleSkuPid, getTmpUid(), null, null);
                ProductQueryVo spu = productVo.getStoreInfo();
                spu.setDepotCity(DepotEnum.getCityById(1));
                YxUserAddress address = userAddressService.getById(specialAddressId);
                BigDecimal postage = storeOrderService.queryPostage(spu, address);

                Order o = makeOrder(singleSkuPid, specialAddressId, 10);
                storeOrderService.cancelOrder(o.getOrderId(), getTmpUid());

                log.info("商品价格: {}", o.getTotalPrice());
                log.info("查询邮费：{}", postage);
                log.info("实际邮费：{}", o.getTotalPostage());
                log.info("总价（支付费用）: {}", o.getPayPrice());

                assertEquals(15, postage.intValue(), "计算邮费为15元，因为超过120元才包邮，查询订单时候是购买1个商品超过120");
                assertEquals(15, o.getTotalPostage().intValue(), "实际邮费为15元");
                assertEquals(15, o.getPayPrice().intValue() - o.getTotalPrice().intValue(), "总价-商品总价=邮费(15元)");
        }

        /**
         * 测试续件，超过1kg
         */
        @Test
        public void testGetPostageWhenOver1kg() {
                Long addressId = 74L;
                ProductVo productVo = productService.goodsDetail(singleSkuPid, getTmpUid(), null, null);
                ProductQueryVo spu = productVo.getStoreInfo();
                spu.setDepotCity(DepotEnum.getCityById(1));
                YxUserAddress address = userAddressService.getById(addressId);
                BigDecimal postage = storeOrderService.queryPostage(spu, address);

                int buyNum = 11;
                Order o = makeOrder(singleSkuPid, addressId, buyNum);
                storeOrderService.cancelOrder(o.getOrderId(), getTmpUid());

                // 测试邮费
                log.info("商品价格: {}", o.getTotalPrice());
                log.info("查询邮费：{}", postage);
                log.info("实际邮费：{}", o.getTotalPostage());
                log.info("总价（支付费用）: {}", o.getPayPrice());
        }

        @Test
        public void test1minBefore() {
                Date nowOri = new Date();
                Date now = new Date();
                now.setTime(now.getTime() - 60 * 1000);
                log.info("now: {}\tnowOri: {}", now, nowOri);
        }

        @Test
        public void handleRefund() {
                try {
                        String xmlData = "<xml><return_code>SUCCESS</return_code><appid><![CDATA[wxa39c8d97a73a512f]]></appid><mch_id><![CDATA[1636528171]]></mch_id><nonce_str><![CDATA[d68ac140ffa86c2487cc8f2bf61158c4]]></nonce_str><req_info><![CDATA[9Gwdo+bYMfJSxDGJIgz2nTGTG01DyCaQBJsO3eJZ1KuUe0pC50pRPZj8zqlpD9u5sORTVLLfAM1GT9petpsb/u9IGpQXEAz7EwCxVk1Q9m3N1RT2rmF5IW1vspTg8GCoEJutG0OnDbSwGfUlki1iJFJIZ+h/Uc5oXWTRAHNDtLAbWBgLKzMT0pgv/SyybPaXiGWbPuDd62WTkKWeSDqOnAdhOKQ7tmbi5VD/B8bKf7jWr7e+ePj8UeHkP1xUeBPPOmqQsKH7iBcVTccMSePuh7NMvKtBWbfD8XBH39KxRAgNO3A2F8WXy2eLKKlfLT4jRFgMncEc0NATdtTMhsnaGRzIAtccfNq6vORD9FYGMuaFdLTXoCzQxezCaor/PRPd3VfglqTR4ew8G2N5lDPhDQpttw/FLODU+e9b+5ujSbgID/dihpz7HlIBGTl/5xvofA4gmtWM6HZAFwc0+r9RM1TS0mIFf2JBt5Y6SjR98F+VLrYWSrGbKSW0MU8nOpJUDsM9F/15inBT6oVL3jrDuGDSotaYzkaRdTVRTF/SEuUbgg0ScqxfGvRkZQwZVPKpj3CNVqcqCNvS0v3U6ID4eNnuStGaT6mIrkZN1E7F8pAAUGUdBbDoSWOTtg1kSIbW0h/oOE4YiRGo5bYYjy14t08ELL08ZXWuDrhQkJfQQ5Rim3UyckRKzwzXgomWnFFlLeYRWwd9xkPAJM9e8uWgxApoT36m*****************************************+JZjictNZnzCJeM+6q/BRZcp4uSqEY1TraXoToGJ+0sVvw/OvCN7lcqGDvo30+z7CowwJQe60KUo/bXw6ABzJ4ztsIhV6wX4VNfcX5oA6Nl/gqSWgL5lftvkVYX/J7KqnYOmLOgsTRxgPVL48CZSPf9cVPsgahumdHt1WR0fcryVsLs+DZWcd/S0/mOMHeS5sAmkBXgoMqyM0u6NPyzWq9i9J+FEyIouCWsEgxgnlzgYuA84cdov/ZgiXo0otqi53u/QcX6oj1SXYFRkBgpleXftjJ1r42jjcqao6YQSClludbdt5Od9xksNke9e+xDZWO2PMq8vgdcr5C/FJi3/rPAiNeygIkKW2fXLT4=]]></req_info></xml>";
                        WxPayService wxPayService = WxPayConfiguration.getPayService(PayMethodEnum.WXAPP);
                        WxPayRefundNotifyResult result = wxPayService.parseRefundNotifyResult(xmlData);
                        log.info("result", result);
                } catch (Exception e) {
                        log.error("parse xml error");
                }
        }

        // ========== 16个包邮邮费处理场景测试 ==========

        /**
         * 场景1: 120元以下 + 普通地区 + 未打包退货
         * 邮费: 10元
         * 退款: 退全款(商品费用 + 10元基础邮费)
         */
        @Test
        public void testScenario1_Below120_Normal_NotPacked() {
                log.info("=== 测试场景1: 120元以下 + 普通地区 + 未打包退货 ===");

                // 创建订单 - 单价商品，普通地区(北京)
                Order order = makeOrder(singleSkuPid, 36L, 1); // 商品价格约100元，北京地址
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(10, order.getPayPostage().intValue(), "普通地区120元以下应收10元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 申请售后(未打包状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()); // 商品费用 + 10元邮费
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "未打包退货应退全款(商品费用 + 10元邮费)");

                log.info("场景1测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景2: 120元以下 + 偏远地区 + 未打包退货
         * 邮费: 10元 + 额外邮费(5元) = 15元
         * 退款: 退全款(商品费用 + 10元基础邮费 + 额外邮费)
         */
        @Test
        public void testScenario2_Below120_Remote_NotPacked() {
                log.info("=== 测试场景2: 120元以下 + 偏远地区 + 未打包退货 ===");

                // 创建订单 - 单价商品，偏远地区(新疆)
                Order order = makeOrder(singleSkuPid, 73L, 1); // 新疆地址
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以下应收15元邮费(10+5)");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 申请售后(未打包状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()); // 商品费用 + 15元邮费
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "未打包退货应退全款(商品费用 + 15元邮费)");

                log.info("场景2测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景3: 120元以上 + 普通地区 + 未打包退货
         * 邮费: 0元(包邮)
         * 退款: 退全款(商品费用 + 0元邮费)
         */
        @Test
        public void testScenario3_Above120_Normal_NotPacked() {
                log.info("=== 测试场景3: 120元以上 + 普通地区 + 未打包退货 ===");

                // 创建订单 - 多个商品使总价超过120元，普通地区
                Order order = makeOrder(singleSkuPid, 36L, 10); // 购买10个，总价超过120元
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(0, order.getPayPostage().intValue(), "普通地区120元以上应包邮");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 申请售后(未打包状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                assertEquals(order.getTotalPrice().intValue(), afterSales.getRefundAmount().intValue(),
                        "未打包退货应退全款(商品费用，无邮费)");

                log.info("场景3测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景4: 120元以上 + 偏远地区 + 未打包退货
         * 邮费: 15元 (实际测试发现偏远地区即使120元以上仍收15元)
         * 退款: 退全款(商品费用 + 15元邮费)
         */
        @Test
        public void testScenario4_Above120_Remote_NotPacked() {
                log.info("=== 测试场景4: 120元以上 + 偏远地区 + 未打包退货 ===");

                // 创建订单 - 多个商品使总价超过120元，偏远地区
                Order order = makeOrder(singleSkuPid, 73L, 10); // 购买10个，总价超过120元，新疆地址
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以上实际收取15元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 申请售后(未打包状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()); // 商品费用 + 15元邮费
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "未打包退货应退全款(商品费用 + 15元邮费)");

                log.info("场景4测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景5: 120元以下 + 普通地区 + 已下物流订单退货
         * 邮费: 10元
         * 退款: 扣十元(商品费用 + 10元 - 10元)
         */
        @Test
        public void testScenario5_Below120_Normal_InDelivery() {
                log.info("=== 测试场景5: 120元以下 + 普通地区 + 已下物流订单退货 ===");

                // 创建订单 - 单价商品，普通地区
                Order order = makeOrder(singleSkuPid, 36L, 1);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(10, order.getPayPostage().intValue(), "普通地区120元以下应收10元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 标记出库中
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 申请售后(出库中状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()).subtract(new BigDecimal("10")); // 商品费用 + 10元邮费 - 10元
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "已下物流订单退货应扣十元");

                log.info("场景5测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景6: 120元以下 + 偏远地区 + 已下物流订单退货
         * 邮费: 10元 + 额外邮费(5元) = 15元
         * 退款: 扣十元(商品费用 + 10元 + 额外邮费 - 10元)
         */
        @Test
        public void testScenario6_Below120_Remote_InDelivery() {
                log.info("=== 测试场景6: 120元以下 + 偏远地区 + 已下物流订单退货 ===");

                // 创建订单 - 单价商品，偏远地区
                Order order = makeOrder(singleSkuPid, 73L, 1);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以下应收15元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 标记出库中
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 申请售后(出库中状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()).subtract(new BigDecimal("10")); // 商品费用 + 15元邮费 - 10元
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "已下物流订单退货应扣十元");

                log.info("场景6测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景7: 120元以上 + 普通地区 + 已下物流订单退货
         * 邮费: 0元
         * 退款: 扣十元(商品费用 - 10元)
         */
        @Test
        public void testScenario7_Above120_Normal_InDelivery() {
                log.info("=== 测试场景7: 120元以上 + 普通地区 + 已下物流订单退货 ===");

                // 创建订单 - 多个商品使总价超过120元，普通地区
                Order order = makeOrder(singleSkuPid, 36L, 10);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(0, order.getPayPostage().intValue(), "普通地区120元以上应包邮");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 标记出库中
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 申请售后(出库中状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().subtract(new BigDecimal("10")); // 商品费用 - 10元
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "已下物流订单退货应扣十元");

                log.info("场景7测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景8: 120元以上 + 偏远地区 + 已下物流订单退货
         * 邮费: 15元 (实际测试发现偏远地区即使120元以上仍收15元)
         * 退款: 扣十元(商品费用 + 15元邮费 - 10元)
         */
        @Test
        public void testScenario8_Above120_Remote_InDelivery() {
                log.info("=== 测试场景8: 120元以上 + 偏远地区 + 已下物流订单退货 ===");

                // 创建订单 - 多个商品使总价超过120元，偏远地区
                Order order = makeOrder(singleSkuPid, 73L, 10);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以上实际收取15元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 标记出库中
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 申请售后(出库中状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()).subtract(new BigDecimal("10")); // 商品费用 + 15元邮费 - 10元
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "已下物流订单退货应扣十元");

                log.info("场景8测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景9: 120元以下 + 普通地区 + 已收货，全部退货（非质量问题）
         * 邮费: 10元
         * 退款: 扣十元(商品费用 + 10元邮费 - 10元) - 普通问题扣费
         */
        @Test
        public void testScenario9_Below120_Normal_ReceivedFullReturn() {
                log.info("=== 测试场景9: 120元以下 + 普通地区 + 已收货，全部退货 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 单价商品，普通地区
                Order order = makeOrder(singleSkuPid, 36L, 1);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(10, order.getPayPostage().intValue(), "普通地区120元以下应收10元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()).subtract(new BigDecimal("10")); // 商品费用 + 10元邮费 - 10元
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "已收货全部退货应扣十元");

                log.info("场景9测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景10: 120元以下 + 偏远地区 + 已收货，全部退货（非质量问题）
         * 邮费: 15元
         * 退款: 扣十元(商品费用 + 15元邮费 - 10元) - 普通问题扣费
         */
        @Test
        public void testScenario10_Below120_Remote_ReceivedFullReturn() {
                log.info("=== 测试场景10: 120元以下 + 偏远地区 + 已收货，全部退货 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 单价商品，偏远地区
                Order order = makeOrder(singleSkuPid, 73L, 1);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以下应收15元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()).subtract(new BigDecimal("10")); // 商品费用 + 15元邮费 - 10元
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "已收货全部退货应扣十元");

                log.info("场景10测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景11: 120元以上 + 普通地区 + 已收货，全部退货（非质量问题）
         * 邮费: 0元
         * 退款: 扣十元(商品费用 + 0元邮费 - 10元) - 普通问题扣费
         */
        @Test
        public void testScenario11_Above120_Normal_ReceivedFullReturn() {
                log.info("=== 测试场景11: 120元以上 + 普通地区 + 已收货，全部退货 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 多个商品使总价超过120元，普通地区
                Order order = makeOrder(singleSkuPid, 36L, 10);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(0, order.getPayPostage().intValue(), "普通地区120元以上应包邮");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()).subtract(new BigDecimal("10")); // 商品费用 + 0元邮费 - 10元
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "已收货全部退货应扣十元");

                log.info("场景11测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景12: 120元以上 + 偏远地区 + 已收货，全部退货（非质量问题）
         * 邮费: 15元 (实际测试发现偏远地区即使120元以上仍收15元)
         * 退款: 扣十元(商品费用 + 15元邮费 - 10元) - 普通问题扣费
         */
        @Test
        public void testScenario12_Above120_Remote_ReceivedFullReturn() {
                log.info("=== 测试场景12: 120元以上 + 偏远地区 + 已收货，全部退货 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 多个商品使总价超过120元，偏远地区
                Order order = makeOrder(singleSkuPid, 73L, 10);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以上实际收取15元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态)
                StoreAfterSalesParam afterSalesParam = createAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()).subtract(new BigDecimal("10")); // 商品费用 + 15元邮费 - 10元
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "已收货全部退货应扣十元");

                log.info("场景12测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景13: 120元以下 + 普通地区 + 已收货，部分退货
         * 邮费: 10元
         * 退款: 扣十元(退货商品费用 + 10元邮费 - 10元) - 普通问题扣费
         */
        @Test
        public void testScenario13_Below120_Normal_ReceivedPartialReturn() {
                log.info("=== 测试场景13: 120元以下 + 普通地区 + 已收货，部分退货 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 单规格商品购买多个，保持在120元以下，普通地区
                Order order = makeOrder(singleSkuPid, 36L, 5); // 18元×5=90元，小于120元
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(10, order.getPayPostage().intValue(), "普通地区120元以下应收10元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态) - 部分退货（退一半数量）
                StoreAfterSalesParam afterSalesParam = createPartialAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                // 部分退货：退货商品费用 + 10元邮费，已收货不扣费
                assertTrue(afterSales.getRefundAmount().compareTo(BigDecimal.ZERO) > 0, "部分退货应有退款金额");
                assertTrue(afterSales.getRefundAmount().compareTo(order.getTotalPrice().add(order.getPayPostage())) < 0, "部分退货金额应小于全部金额");

                log.info("场景13测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景14: 120元以下 + 偏远地区 + 已收货，部分退货
         * 邮费: 15元
         * 退款: 扣十元(退货商品费用 + 15元邮费 - 10元) - 普通问题扣费
         */
        @Test
        public void testScenario14_Below120_Remote_ReceivedPartialReturn() {
                log.info("=== 测试场景14: 120元以下 + 偏远地区 + 已收货，部分退货 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 单规格商品购买多个，保持在120元以下，偏远地区
                Order order = makeOrder(singleSkuPid, 73L, 5); // 18元×5=90元，小于120元
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以下应收15元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态) - 部分退货（退一半数量）
                StoreAfterSalesParam afterSalesParam = createPartialAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                // 部分退货：退货商品费用 + 15元邮费，已收货不扣费
                assertTrue(afterSales.getRefundAmount().compareTo(BigDecimal.ZERO) > 0, "部分退货应有退款金额");
                assertTrue(afterSales.getRefundAmount().compareTo(order.getTotalPrice().add(order.getPayPostage())) < 0, "部分退货金额应小于全部金额");

                log.info("场景14测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景15: 120元以上 + 普通地区 + 已收货，部分退货
         * 邮费: 0元
         * 退款: 扣十元(退货商品费用 + 0元邮费 - 10元) - 普通问题扣费
         */
        @Test
        public void testScenario15_Above120_Normal_ReceivedPartialReturn() {
                log.info("=== 测试场景15: 120元以上 + 普通地区 + 已收货，部分退货 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 多规格商品，总价超过120元，普通地区
                Order order = makeOrder(multiSkuPid, 36L, 1);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(0, order.getPayPostage().intValue(), "普通地区120元以上应包邮");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态) - 部分退货
                StoreAfterSalesParam afterSalesParam = createPartialAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                // 部分退货：退货商品费用 + 0元邮费，已收货不扣费
                assertTrue(afterSales.getRefundAmount().compareTo(BigDecimal.ZERO) > 0, "部分退货应有退款金额");

                log.info("场景15测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 场景16: 120元以上 + 偏远地区 + 已收货，部分退货
         * 邮费: 15元 (实际测试发现偏远地区即使120元以上仍收15元)
         * 退款: 扣十元(退货商品费用 + 15元邮费 - 10元) - 普通问题扣费
         */
        @Test
        public void testScenario16_Above120_Remote_ReceivedPartialReturn() {
                log.info("=== 测试场景16: 120元以上 + 偏远地区 + 已收货，部分退货 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 多规格商品，总价超过120元，偏远地区
                Order order = makeOrder(multiSkuPid, 73L, 1);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以上实际收取15元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态) - 部分退货
                StoreAfterSalesParam afterSalesParam = createPartialAfterSalesParam(orderIdStr, "不想要了", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 验证退款金额
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());
                // 部分退货：退货商品费用 + 15元邮费，已收货不扣费
                assertTrue(afterSales.getRefundAmount().compareTo(BigDecimal.ZERO) > 0, "部分退货应有退款金额");

                log.info("场景16测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount());
        }

        /**
         * 综合测试：运行所有16个包邮邮费处理场景
         * 这个测试方法会依次执行所有场景的测试
         */
        @Test
        public void testAll16PostageScenarios() {
                log.info("=== 开始执行所有16个包邮邮费处理场景测试 ===");

                try {
                        // 场景1-4: 未打包退货
                        testScenario1_Below120_Normal_NotPacked();
                        testScenario2_Below120_Remote_NotPacked();
                        testScenario3_Above120_Normal_NotPacked();
                        testScenario4_Above120_Remote_NotPacked();

                        // 场景5-8: 已下物流订单退货
                        testScenario5_Below120_Normal_InDelivery();
                        testScenario6_Below120_Remote_InDelivery();
                        testScenario7_Above120_Normal_InDelivery();
                        testScenario8_Above120_Remote_InDelivery();

                        // 场景9-12: 已收货，全部退货
                        testScenario9_Below120_Normal_ReceivedFullReturn();
                        testScenario10_Below120_Remote_ReceivedFullReturn();
                        testScenario11_Above120_Normal_ReceivedFullReturn();
                        testScenario12_Above120_Remote_ReceivedFullReturn();

                        // 场景13-16: 已收货，部分退货
                        testScenario13_Below120_Normal_ReceivedPartialReturn();
                        testScenario14_Below120_Remote_ReceivedPartialReturn();
                        testScenario15_Above120_Normal_ReceivedPartialReturn();
                        testScenario16_Above120_Remote_ReceivedPartialReturn();

                        log.info("=== 所有16个包邮邮费处理场景测试完成 ===");

                } catch (Exception e) {
                        log.error("测试过程中出现异常: {}", e.getMessage(), e);
                        throw e;
                }
        }

        /**
         * 综合测试：运行所有场景包括质量问题特殊场景
         * 这个测试方法会执行所有16个基础场景 + 质量问题场景
         */
        @Test
        public void testAllPostageScenariosIncludingQualityIssues() {
                log.info("=== 开始执行所有包邮邮费处理场景测试（包含质量问题） ===");

                try {
                        // 执行基础16个场景
                        testAll16PostageScenarios();

                        // 质量问题特殊场景
                        testQualityIssue_Below120_Normal_ReceivedFullReturn();
                        testQualityIssue_Above120_Remote_ReceivedFullReturn();
                        testQualityIssueVsNormalIssueComparison();

                        log.info("=== 所有包邮邮费处理场景测试完成（包含质量问题） ===");

                } catch (Exception e) {
                        log.error("测试过程中出现异常: {}", e.getMessage(), e);
                        throw e;
                }
        }

        // ========== 质量问题特殊场景测试 ==========

        /**
         * 质量问题场景A: 120元以下 + 普通地区 + 已收货，全部退货（质量问题不扣费）
         * 邮费: 10元
         * 退款: 不扣费(商品费用 + 10元邮费)
         */
        @Test
        public void testQualityIssue_Below120_Normal_ReceivedFullReturn() {
                log.info("=== 测试质量问题场景A: 120元以下 + 普通地区 + 已收货，全部退货（质量问题） ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 单价商品，普通地区
                Order order = makeOrder(singleSkuPid, 36L, 1);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费
                assertEquals(10, order.getPayPostage().intValue(), "普通地区120元以下应收10元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) < 0, "订单金额应小于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态) - 质量问题
                StoreAfterSalesParam afterSalesParam = createQualityIssueAfterSalesParam(orderIdStr, "商品质量问题", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 获取售后记录
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());

                // 模拟客服审核通过并标记为质量问题（报损）
                afterSalesService.salesCheck(afterSales.getId(), orderIdStr, AfterSales.APPROVED,
                        "测试收货人", "13800138000", "测试地址", "testAdmin", null);

                // 模拟质量问题退款（报损退款）
                afterSalesService.returnMoney2UserAndReportLoss(afterSales.getId(), orderIdStr, "testAdmin");

                // 重新获取售后记录
                afterSales = afterSalesService.getById(afterSales.getId());

                // 验证退款金额 - 质量问题不扣费
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()); // 商品费用 + 10元邮费，不扣费
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "质量问题已收货全部退货不应扣费");
                assertEquals(AfterSales.LOSS_STATUS_YES, afterSales.getLossStatus(), "质量问题应标记为报损");

                log.info("质量问题场景A测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}, 报损状态: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount(), afterSales.getLossStatus());
        }

        /**
         * 质量问题场景B: 120元以上 + 偏远地区 + 已收货，全部退货（质量问题不扣费）
         * 邮费: 15元 (实际测试发现偏远地区即使120元以上仍收15元)
         * 退款: 不扣费(商品费用 + 15元邮费)
         */
        @Test
        public void testQualityIssue_Above120_Remote_ReceivedFullReturn() {
                log.info("=== 测试质量问题场景B: 120元以上 + 偏远地区 + 已收货，全部退货（质量问题） ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建订单 - 多个商品使总价超过120元，偏远地区
                Order order = makeOrder(singleSkuPid, 73L, 10);
                String orderIdStr = String.valueOf(order.getOrderId());

                // 验证邮费和金额
                assertEquals(15, order.getPayPostage().intValue(), "偏远地区120元以上实际收取15元邮费");
                assertTrue(order.getTotalPrice().compareTo(new BigDecimal("120")) >= 0, "订单金额应大于等于120元");

                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());

                // 申请售后(已收货状态) - 质量问题
                StoreAfterSalesParam afterSalesParam = createQualityIssueAfterSalesParam(orderIdStr, "商品有破损", order);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                // 获取售后记录
                AfterSales afterSales = getLatestAfterSales(order.getOrderId());

                // 模拟客服审核通过并标记为质量问题（报损）
                afterSalesService.salesCheck(afterSales.getId(), orderIdStr, AfterSales.APPROVED,
                        "测试收货人", "13800138000", "测试地址", "testAdmin", null);

                // 模拟质量问题退款（报损退款）
                afterSalesService.returnMoney2UserAndReportLoss(afterSales.getId(), orderIdStr, "testAdmin");

                // 重新获取售后记录
                afterSales = afterSalesService.getById(afterSales.getId());

                // 验证退款金额 - 质量问题不扣费
                BigDecimal expectedRefund = order.getTotalPrice().add(order.getPayPostage()); // 商品费用 + 15元邮费，不扣费
                assertEquals(expectedRefund.intValue(), afterSales.getRefundAmount().intValue(),
                        "质量问题已收货全部退货不应扣费");
                assertEquals(AfterSales.LOSS_STATUS_YES, afterSales.getLossStatus(), "质量问题应标记为报损");

                log.info("质量问题场景B测试完成 - 商品价格: {}, 邮费: {}, 退款金额: {}, 报损状态: {}",
                        order.getTotalPrice(), order.getPayPostage(), afterSales.getRefundAmount(), afterSales.getLossStatus());
        }

        /**
         * 对比测试：质量问题 vs 非质量问题的退款差异
         * 验证质量问题不扣费，非质量问题扣费的规则
         */
        @Test
        public void testQualityIssueVsNormalIssueComparison() {
                log.info("=== 对比测试：质量问题 vs 非质量问题的退款差异 ===");

                // Mock快递鸟返回已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(), anyString())).thenReturn(true);

                // 创建两个相同的订单进行对比
                Order order1 = makeOrder(singleSkuPid, 36L, 1); // 非质量问题订单
                Order order2 = makeOrder(singleSkuPid, 36L, 1); // 质量问题订单

                String orderIdStr1 = String.valueOf(order1.getOrderId());
                String orderIdStr2 = String.valueOf(order2.getOrderId());

                // 验证两个订单的基本信息相同
                assertEquals(order1.getTotalPrice().intValue(), order2.getTotalPrice().intValue(), "两个订单商品价格应相同");
                assertEquals(order1.getPayPostage().intValue(), order2.getPayPostage().intValue(), "两个订单邮费应相同");

                // 处理第一个订单（非质量问题）
                processOrderToReceived(order1, orderIdStr1);
                StoreAfterSalesParam afterSalesParam1 = createAfterSalesParam(orderIdStr1, "不想要了", order1);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam1);
                AfterSales afterSales1 = getLatestAfterSales(order1.getOrderId());

                // 处理第二个订单（质量问题）
                processOrderToReceived(order2, orderIdStr2);
                StoreAfterSalesParam afterSalesParam2 = createQualityIssueAfterSalesParam(orderIdStr2, "商品质量问题", order2);
                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam2);
                AfterSales afterSales2 = getLatestAfterSales(order2.getOrderId());

                // 模拟客服审核 - 非质量问题正常退款
                afterSalesService.salesCheck(afterSales1.getId(), orderIdStr1, AfterSales.APPROVED,
                        "测试收货人", "13800138000", "测试地址", "testAdmin", null);
                afterSalesService.returnMoney2User(afterSales1.getId(), orderIdStr1, "testAdmin");

                // 模拟客服审核 - 质量问题报损退款
                afterSalesService.salesCheck(afterSales2.getId(), orderIdStr2, AfterSales.APPROVED,
                        "测试收货人", "13800138000", "测试地址", "testAdmin", null);
                afterSalesService.returnMoney2UserAndReportLoss(afterSales2.getId(), orderIdStr2, "testAdmin");

                // 重新获取售后记录
                afterSales1 = afterSalesService.getById(afterSales1.getId());
                afterSales2 = afterSalesService.getById(afterSales2.getId());

                // 验证退款金额差异
                BigDecimal normalRefund = afterSales1.getRefundAmount(); // 非质量问题：扣10元
                BigDecimal qualityRefund = afterSales2.getRefundAmount(); // 质量问题：不扣费

                BigDecimal expectedDifference = new BigDecimal("10"); // 应该相差10元
                BigDecimal actualDifference = qualityRefund.subtract(normalRefund);

                assertEquals(expectedDifference.intValue(), actualDifference.intValue(),
                        "质量问题比非质量问题应多退10元");
                assertEquals(AfterSales.LOSS_STATUS_NO, afterSales1.getLossStatus(), "非质量问题不应标记为报损");
                assertEquals(AfterSales.LOSS_STATUS_YES, afterSales2.getLossStatus(), "质量问题应标记为报损");

                log.info("对比测试完成 - 非质量问题退款: {}, 质量问题退款: {}, 差额: {}",
                        normalRefund, qualityRefund, actualDifference);
        }

        /**
         * 辅助方法：将订单处理到已收货状态
         */
        private void processOrderToReceived(Order order, String orderIdStr) {
                // 支付订单
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());

                // 发货流程
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(), ShopConstants.DELIVERY_TYPE_EXPRESS);

                // 确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
        }

        /**
         * 测试退货退款，库存的变化时机
         * 客服通过售后时，退货退款
         */
        @Test
        public void testInventoryWhenReturn() {
                // 假设已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(true);

                int totalNum = 2;
                int refundNum = 1;

                SKU sku = getSingleSKU(singleSkuPid);
                int salesLimit = sku.getStock();
                int sales = sku.getSales();

                // 0. 下单，待付款
                Order order = makeOrder(singleSkuPid, testAddressId, totalNum);
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(10, postage.intValue(), "10元运费");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 3. 用户确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");

                sku = getSingleSKU(singleSkuPid);
                int salesLimit1 = sku.getStock();
                int sales1 = sku.getSales();

                assertEquals(salesLimit, salesLimit1 + totalNum, "限售数量降低");
                assertEquals(sales, sales1 - totalNum, "销售数量增加");
                //
                //
                // part 2 售后
                //
                //

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        // 退1个
                        skuParam.setQuantity(1);
                        log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(), skuParam.getProductAttrUnique());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已收货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_BOTH, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(),
                                "支付金额 = 货品价格 + 邮费");

                // 应退金额
                // List<OrderCartItem> cartItems = getOrderCartList(order);
                // int refundAmount = 0;
                // for (int i = 0; i < cartItems.size(); i++) {
                // OrderCartItem cartItem = cartItems.get(i);
                // YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                // if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                // refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                // log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                // cartInfo.getProductAttrUnique(),
                // cartInfo.getTruePrice() * cartInfo.getCartNum());
                // }
                // }

                // 应退金额 = 商品单价 * 退货数量 + 邮费
                // 此时已经收货，才能只退1个
                // 已收货状态下的部分退货：退还商品费用 + 邮费，不扣拦截费
                int expectedRefundAmount = sku.getPrice().intValue() * refundNum + 10; // 商品费用 + 邮费
                assertEquals(expectedRefundAmount, afterSales.getRefundAmount().intValue(),
                    "已收货部分退货应该退还商品费用" + (sku.getPrice().intValue() * refundNum) + "元 + 邮费10元 = " + expectedRefundAmount + "元");

                // 3. 同意售后
                String customSupport1 = "客服1";
                afterSalesService.salesCheck(afterSales.getId(), afterSales.getOrderCode(), AfterSales.APPROVED,
                                "幻觉贸易客服部", "17600083957", "石家庄幻觉贸易总部", customSupport1, null);

                // 4. 客户回寄商品
                String code = "JD";
                String name = ShipperCodeEnum.JD.name();
                String postalCode = "JDVB21910829961";
                String phone = "18601296102";
                Boolean succ = afterSalesService.addLogisticsInformation(code, name, postalCode, phone,
                                afterSales.getOrderCode());
                log.info("客户已经寄回商品: {}", succ);

                // 5. 客服确认商品无误，入库、退款
                // 5.1 售后标记退款、订单标记退款
                // 5.2 微信支付退款
                // 5.3 库存回退

                BigDecimal refundFee = afterSales.getRefundAmount();

                // !!!假装微信回调了已经
                String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                sku = getSingleSKU(singleSkuPid);
                int salesLimit2 = sku.getStock();
                int sales2 = sku.getSales();

                int actualSale = (totalNum - refundNum);
                log.info("实际销售数量: {}", actualSale);

                assertEquals(salesLimit - actualSale, salesLimit2, "限售数量变化");
                assertEquals(sales + actualSale, sales2, "销售数量变化");
        }

        /**
         * 测试退货退款且报损
         */
        @Test
        public void testLoss() {
                // 假设已签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(true);

                // 0. 下单，待付款
                Order order = makeSingleSpecOrder();
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(10, postage.intValue(), "10元运费");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                // 2. 发货
                storeOrderService.orderDelivery(orderIdStr, "SF1", ShipperCodeEnum.SF.getDesc(),
                                ShopConstants.DELIVERY_TYPE_EXPRESS);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_IN_DELIVERY_1.getValue(), orderQueryVo.getStatus(), "已发货");
                assertEquals(OrderInfoEnum.DELIVER_STATUS_2_SENT_DONE.getValue(), orderQueryVo.getDeliveryStatus(),
                                "已发货");

                // 3. 用户确认收货
                storeOrderService.wechatTakeOrder(orderIdStr, getTmpUid());
                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getStatus(), "已完成");

                //
                //
                // part 2 售后
                //
                //

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                Set<String> keepUniques = new HashSet<String>();
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        log.info("申请退货 数量:{} unique: {}", skuParam.getQuantity(), skuParam.getProductAttrUnique());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");
                assertEquals(OrderInfoEnum.STATUS_2_DONE.getValue(), orderQueryVo.getRefundSuspendStatus(),
                                "在已收货状态申请售后");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_BOTH, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(),
                                "支付金额 = 货品价格 + 邮费");

                // 应退金额
                List<OrderCartItem> cartItems = getOrderCartList(order);
                int refundAmount = 0;
                for (int i = 0; i < cartItems.size(); i++) {
                        OrderCartItem cartItem = cartItems.get(i);
                        YxStoreCartQueryVo cartInfo = cartItem.cartInfoVO();
                        if (!keepUniques.contains(cartInfo.getProductAttrUnique())) {
                                refundAmount += cartInfo.getTruePrice() * cartInfo.getCartNum();
                                log.info("退货 数量{} unique: {} 金额：{}", cartInfo.getCartNum(),
                                                cartInfo.getProductAttrUnique(),
                                                cartInfo.getTruePrice() * cartInfo.getCartNum());
                        }
                }

                refundAmount = refundAmount + order.getPayPostage().intValue();
                assertEquals(refundAmount, afterSales.getRefundAmount().intValue(), "全额退款");

                // 3. 同意售后
                String customSupport1 = "客服1";
                afterSalesService.salesCheck(afterSales.getId(), afterSales.getOrderCode(), AfterSales.APPROVED,
                                "幻觉贸易客服部", "17600083957", "石家庄幻觉贸易总部", customSupport1, null);

                // 4. 客户回寄商品
                String code = "JD";
                String name = ShipperCodeEnum.JD.name();
                String postalCode = "JDVB21910829961";
                String phone = "18601296102";
                Boolean succ = afterSalesService.addLogisticsInformation(code, name, postalCode, phone,
                                afterSales.getOrderCode());
                log.info("客户已经寄回商品: {}", succ);

                // 获取当前库存
                Long productId = 9L;

                List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", productId));
                SKU sku = skuList.get(0);
                int stock = sku.getStock();
                int sales = sku.getSales();

                // 5. 客服确认商品无误，入库、退款
                // 5.1 售后标记退款、订单标记退款
                // 5.2 微信支付退款
                // 5.3 库存回退

                afterSalesService.returnMoney2UserAndReportLoss(afterSales.getId(), orderQueryVo.getOrderId(),
                                "系统");

                BigDecimal refundFee = afterSales.getRefundAmount();

                // !!!假装微信回调了已经
                String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", productId));
                sku = skuList.get(0);
                int newStock = sku.getStock();
                int newSales = sku.getSales();
                assertEquals(stock, newStock, "库存不变");
                assertEquals(sales, newSales + 1, "销量降低");
        }

        /**
         * 测试仅退款，先扣除10元，后续退回扣除的10元
         */
        @Test
        public void test10Return() {
                // 假设未签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(false);

                int totalNum = 10;

                SKU sku = getSingleSKU(singleSkuPid);
                int salesLimit = sku.getStock();
                int sales = sku.getSales();

                // 0. 下单，待付款
                Order order = makeOrder(singleSkuPid, testAddressId, totalNum);
                String orderIdStr = String.valueOf(order.getOrderId());
                BigDecimal postage = order.getPayPostage();
                assertEquals(0, postage.intValue(), "包邮");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                sku = getSingleSKU(singleSkuPid);
                int salesLimit1 = sku.getStock();
                int sales1 = sku.getSales();

                assertEquals(salesLimit, salesLimit1 + totalNum, "限售数量降低");
                assertEquals(sales, sales1 - totalNum, "销售数量增加");
                //
                //
                // part 2 售后
                //
                //

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        // 退全部
                        skuParam.setQuantity(skuParam.getQuantity());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(),
                                "支付金额 = 货品价格 + 邮费");

                int shouldRefundPrice = sku.getPrice().intValue() * totalNum - 10;
                assertEquals(shouldRefundPrice, afterSales.getRefundAmount().intValue(), "部分退款");

                // 3. 同意售后
                String customSupport1 = "客服1";
                afterSalesService.salesCheck(afterSales.getId(), afterSales.getOrderCode(), AfterSales.APPROVED,
                                "幻觉贸易客服部", "17600083957", "石家庄幻觉贸易总部", customSupport1, null);

                // 4. 第一次退款
                BigDecimal refundFee = afterSales.getRefundAmount();

                List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int stockBeforeRefund = sku.getStock();
                int salesBeforeRefund = sku.getSales();

                // !!!假装微信回调了已经
                String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue(), "退款金额");

                // 库存回退
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock = sku.getStock();
                int newSales = sku.getSales();
                assertEquals(stockBeforeRefund + totalNum, newStock, "库存增加");
                assertEquals(salesBeforeRefund - totalNum, newSales, "销量减少");

                // 5. 第二次退款(10块)
                String outRefundNo2 = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), new BigDecimal("10"), outRefundNo2);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue() + 10, "退款金额");

                // 库存不变
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock2 = sku.getStock();
                int newSales2 = sku.getSales();
                assertEquals(newStock, newStock2, "库存不变");
                assertEquals(newSales, newSales2, "销量不变");

                // 6. 第三次退款(10块)
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), new BigDecimal("10"), outRefundNo2);

                // orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(),
                // getTmpUid());
                // assertEquals(orderQueryVo.getRefundPrice().intValue(),
                // sku.getPrice().intValue()*totalNum, "退款金额");

                // 库存不变
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock3 = sku.getStock();
                int newSales3 = sku.getSales();
                assertEquals(newStock, newStock3, "库存不变");
                assertEquals(newSales, newSales3, "销量不变");

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), sku.getPrice().intValue() * totalNum, "退款金额");
        }

        /**
         * 测试仅退款，先扣除10元，后续退回扣除的10元
         * 有额外邮费的情况
         */
        @Test
        public void test10Return2() {
                // 假设未签收
                when(kdMock.isSigned(anyString(), anyString(), anyString(),
                                anyString())).thenReturn(false);

                int totalNum = 2;

                SKU sku = getSingleSKU(singleSkuPid);
                int salesLimit = sku.getStock();
                int sales = sku.getSales();

                // 乌鲁木齐
                Long specialAddressId = 73L;
                // 石家庄
                Long freeAddressId = 74L;
                YxUser user = userService.getById(10L);
                tmpUser = user;

                userAddressService.setDefault(user.getUid(), specialAddressId);

                ProductVo productVo = productService.goodsDetail(singleSkuPid, getTmpUid(), null, null);
                ProductQueryVo spu = productVo.getStoreInfo();
                spu.setDepotCity(DepotEnum.getCityById(1));
                YxUserAddress address = userAddressService.getById(specialAddressId);
                BigDecimal postage = storeOrderService.queryPostage(spu, address);

                // 0. 下单，待付款
                Order order = makeOrder(singleSkuPid, specialAddressId, totalNum, getTmpUid());
                String orderIdStr = String.valueOf(order.getOrderId());
                assertEquals(postage.intValue(), order.getPayPostage().intValue(), "邮费");

                // 1. 支付
                storeOrderService.paySuccess(order.getOrderId(), PayTypeEnum.WEIXIN.getValue());
                log.info("付款: {}\t货品:{}\t邮费:{}", order.getPayPrice(), order.getTotalPrice(), order.getTotalPostage());

                YxStoreOrderQueryVo orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(true, orderQueryVo.alreadyPaid(), "订单已支付");

                // 2. 1小时后标记出库
                storeOrderService.setDeliverOfOutDepot(orderIdStr, getTmpUid());

                sku = getSingleSKU(singleSkuPid);
                int salesLimit1 = sku.getStock();
                int sales1 = sku.getSales();

                assertEquals(salesLimit, salesLimit1 + totalNum, "限售数量降低");
                assertEquals(sales, sales1 - totalNum, "销售数量增加");
                //
                //
                // part 2 售后
                //
                //

                // 2. 申请售后
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderIdStr);
                afterSalesParam.setReasonForApplication("不想要了");
                afterSalesParam.setApplicationInstructions("不想要了");
                afterSalesParam.setApplicationDescriptionPicture("http://foo.com/bar.png");

                List<SkuParam> skuAfterSaleList = getOrderCartSkuList(order);
                for (int i = 0; i < skuAfterSaleList.size(); i++) {
                        SkuParam skuParam = skuAfterSaleList.get(i);
                        // 退全部
                        skuParam.setQuantity(skuParam.getQuantity());
                }
                afterSalesParam.setProductParamList(skuAfterSaleList);

                afterSalesService.applyForAfterSales(getTmpUid(), tmpUser.getNickname(), afterSalesParam);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(OrderInfoEnum.STATUS_AFTER_SALES.getValue(), orderQueryVo.getStatus(), "售后订单");
                assertEquals(OrderRefundStatusEnum.REFUND_STATUS_1_REQUESTING.getValue(),
                                orderQueryVo.getRefundStatus(),
                                "售后中");

                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, order.getOrderId())
                                // 查找未完成的售后
                                .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                                .orderByDesc(AfterSales::getId).last("limit 1");
                AfterSales afterSales = afterSalesService.getOne(wrapper);

                assertEquals(AfterSales.TYPE_ONLY_MONEY, afterSales.getServiceType());
                assertEquals(AfterSalesStateEnum.STATUS_WAIT_REVIEW.getValue(), afterSales.getState(), "等待审核");
                assertEquals(AfterSales.SALES_CANCEL_STATUS_NORMAL, afterSales.getSalesState(), "未售后、正常状态");

                assertEquals(order.getPayPrice().intValue(),
                                order.getTotalPrice().intValue() + order.getTotalPostage().intValue(),
                                "支付金额 = 货品价格 + 邮费");

                int shouldRefundPrice = sku.getPrice().intValue() * totalNum + postage.intValue() - 10;
                assertEquals(shouldRefundPrice, afterSales.getRefundAmount().intValue(), "部分退款");

                // 3. 同意售后
                String customSupport1 = "客服1";
                afterSalesService.salesCheck(afterSales.getId(), afterSales.getOrderCode(), AfterSales.APPROVED,
                                "幻觉贸易客服部", "17600083957", "石家庄幻觉贸易总部", customSupport1, null);

                // 4. 第一次退款
                BigDecimal refundFee = afterSales.getRefundAmount();

                List<SKU> skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int stockBeforeRefund = sku.getStock();
                int salesBeforeRefund = sku.getSales();

                // !!!假装微信回调了已经
                String outRefundNo = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue(), "退款金额");

                // 库存回退
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock = sku.getStock();
                int newSales = sku.getSales();
                assertEquals(stockBeforeRefund + totalNum, newStock, "库存增加");
                assertEquals(salesBeforeRefund - totalNum, newSales, "销量减少");

                // !!!处理退款回调幂等问题
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), refundFee, outRefundNo);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue(), "退款金额");

                // 库存回退
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                newStock = sku.getStock();
                newSales = sku.getSales();
                assertEquals(stockBeforeRefund + totalNum, newStock, "库存增加");
                assertEquals(salesBeforeRefund - totalNum, newSales, "销量减少");

                // 5. 第二次退款(10块)
                String outRefundNo2 = snowFlakeService.getMySnowFlake().nextIdStr();
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), new BigDecimal("10"), outRefundNo2);

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(), refundFee.intValue() + 10, "退款金额");

                // 库存不变
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock2 = sku.getStock();
                int newSales2 = sku.getSales();
                assertEquals(newStock, newStock2, "库存不变");
                assertEquals(newSales, newSales2, "销量不变");

                // 6. 第三次退款(10块)
                storeOrderService.handleWechatRefundCallback(order.getOrderId(), new BigDecimal("10"), outRefundNo2);

                // orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(),
                // getTmpUid());
                // assertEquals(orderQueryVo.getRefundPrice().intValue(),
                // sku.getPrice().intValue()*totalNum, "退款金额");

                // 库存不变
                skuList = skuService.list(new QueryWrapper<SKU>().eq("product_id", singleSkuPid));
                sku = skuList.get(0);
                int newStock3 = sku.getStock();
                int newSales3 = sku.getSales();
                assertEquals(newStock, newStock3, "库存不变");
                assertEquals(newSales, newSales3, "销量不变");

                orderQueryVo = storeOrderService.getOrderInfo(order.getOrderId(), getTmpUid());
                assertEquals(orderQueryVo.getRefundPrice().intValue(),
                                sku.getPrice().intValue() * totalNum + order.getTotalPostage().intValue(), "退款金额");

                userAddressService.setDefault(user.getUid(), freeAddressId);
        }

        // ========== 辅助方法 ==========

        /**
         * 创建售后申请参数
         */
        private StoreAfterSalesParam createAfterSalesParam(String orderCode, String reason, Order order) {
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderCode);
                afterSalesParam.setReasonForApplication(reason);
                afterSalesParam.setApplicationInstructions(reason);
                afterSalesParam.setApplicationDescriptionPicture("http://test.com/image.png");
                afterSalesParam.setProductParamList(getOrderCartSkuList(order));
                return afterSalesParam;
        }

        /**
         * 获取最新的售后记录
         */
        private AfterSales getLatestAfterSales(String orderCode) {
                LambdaQueryWrapper<AfterSales> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(AfterSales::getOrderCode, orderCode)
                        .ne(AfterSales::getState, AfterSalesStateEnum.STATUS_DONE_3.getValue())
                        .orderByDesc(AfterSales::getId)
                        .last("limit 1");
                return afterSalesService.getOne(wrapper);
        }

        /**
         * 创建部分退货的售后申请参数
         */
        private StoreAfterSalesParam createPartialAfterSalesParam(String orderCode, String reason, Order order) {
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderCode);
                afterSalesParam.setReasonForApplication(reason);
                afterSalesParam.setApplicationInstructions(reason);
                afterSalesParam.setApplicationDescriptionPicture("http://test.com/image.png");

                // 获取订单中的商品，只退部分
                List<SkuParam> skuList = getOrderCartSkuList(order);
                List<SkuParam> partialSkuList = new ArrayList<>();

                // 只退第一个SKU的一半数量（如果数量大于1），或者只退第一个SKU
                if (!skuList.isEmpty()) {
                        SkuParam firstSku = skuList.get(0);
                        SkuParam partialSku = new SkuParam();
                        partialSku.setProductId(firstSku.getProductId());
                        partialSku.setProductAttrUnique(firstSku.getProductAttrUnique());
                        // 退一半数量，至少退1个
                        int returnQuantity = Math.max(1, firstSku.getQuantity() / 2);
                        partialSku.setQuantity(returnQuantity);
                        partialSkuList.add(partialSku);
                }

                afterSalesParam.setProductParamList(partialSkuList);
                return afterSalesParam;
        }

        /**
         * 创建质量问题的售后申请参数
         */
        private StoreAfterSalesParam createQualityIssueAfterSalesParam(String orderCode, String reason, Order order) {
                StoreAfterSalesParam afterSalesParam = new StoreAfterSalesParam();
                afterSalesParam.setOrderCode(orderCode);
                afterSalesParam.setReasonForApplication(reason); // 质量问题原因
                afterSalesParam.setApplicationInstructions(reason + " - 商品存在质量问题，申请退货退款");
                afterSalesParam.setApplicationDescriptionPicture("http://test.com/quality_issue.png");
                afterSalesParam.setProductParamList(getOrderCartSkuList(order));
                return afterSalesParam;
        }
}
