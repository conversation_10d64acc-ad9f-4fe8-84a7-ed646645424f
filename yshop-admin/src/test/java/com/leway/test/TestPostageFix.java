package com.leway.test;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.leway.AdminAPIRun;
import com.leway.modules.cart.vo.YxStoreCartQueryVo;
import com.leway.modules.order.service.StoreOrderService;
import com.leway.modules.product.service.ProductService;
import com.leway.modules.product.vo.ProductQueryVo;
import com.leway.modules.user.domain.YxUserAddress;
import com.leway.modules.user.service.YxUserAddressService;

import lombok.extern.slf4j.Slf4j;

/**
 * 邮费计算修复验证测试
 */
@Slf4j
@SpringBootTest(classes = AdminAPIRun.class)
@RunWith(SpringRunner.class)
public class TestPostageFix extends AdminServiceTest {

    @Autowired
    private StoreOrderService storeOrderService;
    
    @Autowired
    private ProductService productService;
    
    @Autowired
    private YxUserAddressService userAddressService;

    /**
     * 测试120元以上普通地区包邮修复
     */
    @Test
    public void testAbove120NormalAreaFreeShipping() {
        log.info("=== 测试120元以上普通地区包邮修复 ===");
        
        // 创建模拟购物车数据 - 多规格商品，总价超过120元
        List<YxStoreCartQueryVo> cartInfo = createMockCartInfo(multiSkuPid, 1);
        
        // 普通地区地址（北京）
        YxUserAddress normalAddress = userAddressService.getById(36L);
        
        // 计算邮费
        BigDecimal postage = storeOrderService.handlePostage(cartInfo, normalAddress);
        
        log.info("商品总价: {}, 计算邮费: {}", getTotalPrice(cartInfo), postage);
        
        // 验证：120元以上普通地区应该包邮（邮费为0）
        assertEquals(0, postage.intValue(), "120元以上普通地区应该包邮，邮费应为0元");
    }

    /**
     * 测试120元以上偏远地区邮费
     */
    @Test
    public void testAbove120RemoteAreaShipping() {
        log.info("=== 测试120元以上偏远地区邮费 ===");
        
        // 创建模拟购物车数据 - 多规格商品，总价超过120元
        List<YxStoreCartQueryVo> cartInfo = createMockCartInfo(multiSkuPid, 1);
        
        // 偏远地区地址（新疆）
        YxUserAddress remoteAddress = userAddressService.getById(73L);
        
        // 计算邮费
        BigDecimal postage = storeOrderService.handlePostage(cartInfo, remoteAddress);
        
        log.info("商品总价: {}, 计算邮费: {}", getTotalPrice(cartInfo), postage);
        
        // 验证：120元以上偏远地区仍然收取邮费（根据测试注释，偏远地区不享受包邮）
        assertTrue(postage.intValue() > 0, "120元以上偏远地区仍应收取邮费");
    }

    /**
     * 测试120元以下普通地区邮费
     */
    @Test
    public void testBelow120NormalAreaShipping() {
        log.info("=== 测试120元以下普通地区邮费 ===");

        // 创建模拟购物车数据 - 单规格商品，总价低于120元
        List<YxStoreCartQueryVo> cartInfo = createMockCartInfo(singleSkuPid, 1);

        // 普通地区地址（北京）
        YxUserAddress normalAddress = userAddressService.getById(36L);

        // 计算邮费
        BigDecimal postage = storeOrderService.handlePostage(cartInfo, normalAddress);

        log.info("商品总价: {}, 计算邮费: {}", getTotalPrice(cartInfo), postage);

        // 验证：120元以下普通地区应收取10元邮费
        assertEquals(10, postage.intValue(), "120元以下普通地区应收取10元邮费");
    }

    /**
     * 测试已收货部分退货的退款金额计算
     */
    @Test
    public void testReceivedPartialReturnRefundAmount() {
        log.info("=== 测试已收货部分退货的退款金额计算 ===");

        // 模拟已收货状态下的部分退货场景
        // 商品单价：18元，退货数量：1个
        // 期望退款：18元（商品费用）+ 10元（邮费）= 28元

        int productPrice = 18;
        int refundQuantity = 1;
        int shippingFee = 10;

        // 根据业务逻辑：已收货状态下的部分退货应该退还商品费用 + 邮费
        int expectedRefundAmount = productPrice * refundQuantity + shippingFee;

        log.info("商品单价: {}元, 退货数量: {}个, 邮费: {}元", productPrice, refundQuantity, shippingFee);
        log.info("期望退款金额: {}元", expectedRefundAmount);

        // 验证：已收货部分退货应该退还商品费用 + 邮费 = 28元
        assertEquals(28, expectedRefundAmount, "已收货部分退货应该退还商品费用18元 + 邮费10元 = 28元");
    }

    /**
     * 创建模拟购物车数据
     */
    private List<YxStoreCartQueryVo> createMockCartInfo(Long productId, int quantity) {
        List<YxStoreCartQueryVo> cartInfo = new ArrayList<>();
        
        // 获取商品信息
        ProductQueryVo product = productService.getStoreProductById(productId);
        
        // 创建购物车项
        YxStoreCartQueryVo cartItem = new YxStoreCartQueryVo();
        cartItem.setProductInfo(product);
        cartItem.setCartNum(quantity);
        cartItem.setTruePrice(product.getPrice().doubleValue());
        
        cartInfo.add(cartItem);
        
        return cartInfo;
    }

    /**
     * 计算购物车总价
     */
    private BigDecimal getTotalPrice(List<YxStoreCartQueryVo> cartInfo) {
        return cartInfo.stream()
                .map(item -> new BigDecimal(item.getTruePrice()).multiply(new BigDecimal(item.getCartNum())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
}
