import re
import csv
from collections import namedtuple

Order = namedtuple("Order", "did, num")
pattern = r"CD-《冀西南林路行》-默认\*(\d+)[;]?"

sf_data = []
sf_map = {}
with open("/Users/<USER>/Downloads/0.csv") as csvfile:
    reader = csv.reader(csvfile)
    for row in reader:
        # print("sf", row)
        s = "-".join(row)
        matches = re.findall(pattern, s)
        if matches:
            numbers = sum([int(num) for num in matches])
            # print("matches", matches)
            did = row[1]
            o = Order(did, numbers)
            sf_data.append(row)
            sf_map[did] = o


my_map = {}
with open("/Users/<USER>/Downloads/2024-07-18 15_56_00-数据.csv") as csvfile:
    reader = csv.reader(csvfile)
    for row in reader:
        # print("my", row)
        s = "".join(row)
        sku = row[6]
        sku = sku.split(";")
        num = row[7]
        num = num.split(";")
        did = row[9]
        for i, s in enumerate(sku):
            if "冀西南林路行" in s:
                # print(sku, did, num[i])
                o = my_map.get(did, None)
                if not o:
                    o = Order(did, int(num[i]))
                else:
                    o2 = Order(did, int(num[i]))
                    o = Order(did, int(o.num) + int(o2.num))
                my_map[did] = o

print("sf", len(sf_map))
print("my", len(my_map))

sf_num = sum([int(o.num) for o in sf_map.values()])
print("sf_num", sf_num)

my_sum = sum([int(o.num) for o in my_map.values()])
print("my_sum", my_sum)

for did, o in sf_map.items():
    my_o = my_map.get(did, None)
    if my_o:
        if o.num != my_o.num:
            print("did", did, "sf", o.num, "my", my_o.num)
    else:
        print("error did", did, "sf", o.num, "my", 0)
