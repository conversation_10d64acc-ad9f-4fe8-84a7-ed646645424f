import csv
import datetime
import json
import os

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    SKU_DEFAULT,
    Cart,
    NoticeSub,
    Order,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    all_products,
    format_date,
    get_cart_sku_id,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    read_wechat_pay_log,
    write_csv,
)


def fix(dry_run=True):
    session = get_session(read_only=dry_run)
    """库存基准到
    2024-05-06 15:00:00
    05-06 下单时间：2024-05-06 15:01:45
    2024-05-05 03:00"""
    """2024-05-06 14:44:05"""

    ps = all_products(session, verbose=False, onsale_only=None)
    for p in ps:
        for sku in p.sku_list:
            all_empty_update_time_rs = (
                session.query(NoticeSub)
                .filter(
                    NoticeSub.product_id == p.id,
                    NoticeSub.sku_unique == sku.unique,
                    # NoticeSub.update_time.is_(None),
                    NoticeSub.is_del == 1,
                )
                .all()
            )
            print(p.name, sku.sku, len(all_empty_update_time_rs))
            for r in all_empty_update_time_rs:
                # print(r.id, r.product_id, r.sku_unique, r.create_time, r.update_time)
                if not dry_run:
                    r.update_time = r.create_time
                    session.commit()
                # 是否有没删除的记录
                not_delete_rs = (
                    session.query(NoticeSub)
                    .filter(
                        NoticeSub.product_id == p.id,
                        NoticeSub.sku_unique == sku.unique,
                        NoticeSub.is_del == 0,
                        NoticeSub.uid == r.uid,
                    )
                    .all()
                )
                if not_delete_rs:
                    print(
                        "有未删除的记录",
                        r.uid,
                        r.product_id,
                        r.sku_unique,
                        len(not_delete_rs),
                    )
            print("修复", len(all_empty_update_time_rs), "条记录")

        print("=" * 30)
    session.close()


if __name__ == "__main__":
    dry_run = True
    fix(dry_run)
