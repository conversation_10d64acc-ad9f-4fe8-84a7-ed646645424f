import csv
import datetime
import json
import os

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    VINYL_PID,
    VINYL_SKU1,
    Cart,
    Order,
    format_date,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    read_wechat_pay_log,
)

# 创建一个 Session 实例
session = get_session()


def cal(filename, product_id, sku):
    f_path = PROJ_DIR + "/bug_logs/" + filename
    rows = read_wechat_pay_log(f_path)

    # 净卖出商品数量
    total_sale_num = 0
    total_refund_num = 0

    total_succ_order_list = []
    total_refund_order_list = []
    trade_date_set = set()
    for idx, row in enumerate(rows):
        # 输出处理进度 500个一次
        if idx % 500 == 0:
            print("处理进度: ", idx, "/", len(rows))
        order_id = row[6]
        # 交易类型
        trade_status = row[9]
        order = Order.query(session, order_id)
        if not order:
            print("订单不存在: ", order_id)
            continue

        cart_ids = order.cart_id
        items = Cart.query(session, cart_ids, order_id)

        # 2024-05-06 15:19:13
        # trade 时间
        trade_time = row[0]
        # 转为 datetime
        trade_time = datetime.datetime.strptime(trade_time, "%Y-%m-%d %H:%M:%S")

        if trade_status == "SUCCESS":
            # 商品数量
            p_num = sum(
                [get_certain_cart_sku_num(cart, product_id, sku=sku) for cart in items]
            )

            if p_num == 0:
                # 不统计
                continue

            trade_date_set.add(format_date(trade_time))
            total_sale_num += p_num
            # print("成功订单: ", order_id, "购买数量: ", no2_cd_num)
            total_succ_order_list.append([order, trade_time])
        elif trade_status == "REFUND":
            trade_date_set.add(format_date(trade_time))
            refund_p_num = sum(
                [
                    get_certain_cart_sku_refund_num(order, cart, product_id, sku=sku)
                    for cart in items
                ]
            )
            if refund_p_num > 0:
                total_refund_num += refund_p_num
                total_refund_order_list.append([order, trade_time])
                # print("退款订单: ", order_id, "退款数量: ", no2_cd_refund_num)
        else:
            print("未知交易状态", trade_status)

    print("商品售出数量: ", total_sale_num, "商品退款数量: ", total_refund_num)
    print("净售出:", total_sale_num - total_refund_num)

    print("交易日期", sorted(trade_date_set))

    # 按日统计，返回统计结果
    def daily_count(day="2024-05-06", verbose=False):
        # 开始结束时间
        start = datetime.datetime.strptime(day, "%Y-%m-%d")
        end = start + datetime.timedelta(days=1)  # 下一日

        ret_order_list = []

        total_product_count = 0
        total_product_refund_count = 0
        day_refund_orders = []
        for o, trade_time in total_succ_order_list:
            cart_ids = o.cart_id
            if not (trade_time >= start and trade_time < end):
                continue
            items = Cart.query(session, cart_ids, o.order_id)
            p_num = sum(
                [get_certain_cart_sku_num(cart, product_id, sku=sku) for cart in items]
            )

            total_product_count += p_num
            ret_order_list.append(o)
            # 有退单
            found_refund_list = [
                (x, t) for (x, t) in total_refund_order_list if o.order_id == x.order_id
            ]

            assert len(found_refund_list) <= 1

            if found_refund_list:
                refund_time = found_refund_list[0][1]

                # if format_date(refund_time) != format_date(trade_time):
                #     # 暂时不计入非当日退款
                #     print(
                #         "非当日退款",
                #         o.order_id,
                #         trade_time,
                #         refund_time,
                #         format_date(trade_time),
                #         format_date(refund_time),
                #     )
                #     continue
                no2_cd_refund_num = sum(
                    [
                        get_certain_cart_sku_refund_num(o, cart, product_id, sku=sku)
                        for cart in items
                    ]
                )
                if no2_cd_refund_num > 0:
                    total_product_refund_count += no2_cd_refund_num
                    day_refund_orders.append(o)
                    if p_num == no2_cd_refund_num:
                        pass
                    else:
                        print("部分退款: ", o.order_id)

        print(
            day,
            "销售订单数:",
            len(ret_order_list),
            "销售数量:",
            total_product_count,
            "退货数量：",
            total_product_refund_count,
            "净销售数量:",
            total_product_count - total_product_refund_count,
        )
        if verbose:
            print("*" * 30)
            for r in day_refund_orders:
                print(r)
            print("*" * 30)
        return (
            ret_order_list,
            day_refund_orders,
            total_product_count - total_product_refund_count,
        )

    # 退款订单不在支付订单中
    # sid_set = set([x.order_id for x in total_succ_order_list])

    # special_order_list = [x for x in total_refund_order_list if x.order_id not in sid_set]
    # print("退款订单不在支付订单中", len(special_order_list))

    count = 0
    verbose = False
    for day in sorted(trade_date_set):
        _, _, daily_no2_cd_num = daily_count(day, verbose)
        count += daily_no2_cd_num

    print("合计净售出", count)


if __name__ == "__main__":
    # filename = "1636528171All2024-04-01_2024-05-01.csv"
    # filename = "1636528171All2024-05-06_2024-05-09.csv"
    filename = "1636528171All2024-05-05_2024-05-11.csv"
    # cal(filename, VINYL_PID, VINYL_SKU1)
    # cal(filename, 641, '默认')
    cal(filename, 642, "默认")
    session.close()
