import csv
import datetime
import json
import logging
import os
from decimal import Decimal

import requests

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额
# https://pay.weixin.qq.com/index.php/xphp/cfund_bill_nc/funds_bill_nc#/
# 微信支付-交易中心-资金账单

# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    AfterSales,
    AfterSalesItem,
    Cart,
    Order,
    Product,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    download_bill,
    filter_special,
    format_date,
    get_cart_key,
    get_cart_num,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    read_csv,
    read_json,
    read_wechat_pay_log,
    write_csv,
)

# 创建一个 Session 实例
session = get_session()


MAYBE_DELETED_ORDER_IDS = set([])


def parse_bill_date(date_str):
    """Parse date string to datetime object"""
    try:
        return datetime.datetime.strptime(date_str, "%Y-%m-%d")
    except ValueError:
        raise ValueError("Invalid date format. Please use YYYY-MM-DD")


def search_bill_by_order(bill_file, order_number):
    """Search bills by merchant order number"""
    results = []
    with open(bill_file, "r", encoding="utf-8") as f:
        reader = csv.DictReader(f)
        for row in reader:
            if order_number and order_number in row.get("商户订单号"):
                results.append(row)
    return results


def get_daily_bill(date_str=None, order_number=None):
    """Get WeChat Pay bills for specific date or order number"""
    if not date_str and not order_number:
        raise ValueError("请提供日期或订单号")

    # 账单文件存储在bills目录
    base_path = "/data/wechat_pay"
    bills_dir = os.path.join(base_path, "raw")
    report_dir = os.path.join(base_path, "report")

    if date_str:
        date = parse_bill_date(date_str)
        bill_file = os.path.join(
            bills_dir, f"微信支付账单{date.strftime('%Y-%m-%d')}.csv"
        )
        report_file = os.path.join(
            report_dir, f"流水数据{date.strftime('%Y-%m-%d')}.csv"
        )
        print(f"账单文件: {bill_file}")
        print(f"流水文件: {report_file}")

        results = []
        # 检查账单文件
        if os.path.exists(bill_file):
            if order_number:
                results.extend(search_bill_by_order(bill_file, order_number))
            else:
                with open(bill_file, "r", encoding="utf-8") as f:
                    results.extend(list(csv.DictReader(f)))

        # 检查流水文件
        if os.path.exists(report_file):
            with open(report_file, "r", encoding="utf-8") as f:
                reader = csv.reader(f)
                flow_data = list(reader)
                if flow_data:
                    print("\n当日流水明细:")
                    for row in flow_data:
                        if order_number and order_number not in row[8]:
                            continue
                        print(
                            f"时间:{row[0]} 类型:{row[3]} 金额:{row[2]} 商品:{row[5]} 数量:{row[7]} 订单:{row[8]}"
                        )
                    print(f"期末余额: {flow_data[-1][4]}")

        if not results and not os.path.exists(report_file):
            print(f"警告: {date_str} 的账单文件和流水文件都不存在")
            return []

        return results

    else:
        # 在所有账单文件中搜索订单号
        results = []
        if os.path.exists(bills_dir):
            for file in os.listdir(bills_dir):
                if file.startswith("bill_") and file.endswith(".csv"):
                    results.extend(
                        search_bill_by_order(
                            os.path.join(bills_dir, file), order_number
                        )
                    )
        return results


def stat_sku_daily(date_str, sku: int, verbose=False, init_stock=0):
    """统计指定SKU某天的销量和出入库情况

    Args:
        date_str: 日期字符串，格式为YYYY-MM-DD
        sku: SKU编号
        verbose: 是否显示详细信息
        init_stock: 初始库存

    Returns:
        dict: 包含销量、退款、出入库等统计信息
    """
    base_path = "/data/wechat_pay/raw"
    bill_csv_path = base_path + f"/微信支付账单{date_str}.csv"

    # 初始化统计数据
    stats = {
        "sale_count": 0,  # 销售数量
        "refund_count": 0,  # 退款数量
        "stock_in": 0,  # 入库数量
        "stock_out": 0,  # 出库数量
        "orders": {"success": [], "refund": []},  # 订单详情
        "tmp_stock": {  # 新增: 临时库存变化
            "stock_in": 0,
            "stock_out": 0,
            "adjustments": 0,
            "records": [],
        },
    }

    rows = read_wechat_pay_log(bill_csv_path, verbose=False)
    rows = sorted(
        rows, key=lambda x: datetime.datetime.strptime(x[0], "%Y-%m-%d %H:%M:%S")
    )
    if not rows:
        if verbose:
            print(f"未找到{date_str}的账单")
        return stats

    current_stock = init_stock
    for row in rows:
        order_id = row[6]
        trade_status = row[9]

        order = Order.query_by_trade_no(session, order_id)
        if not order:
            if verbose:
                print(f"订单不存在: {order_id}")
            continue

        cart_ids = order.cart_id
        items = Cart.query(session, cart_ids, order_id)

        # 检查订单中是否包含目标SKU
        sku_items = [
            cart
            for cart in items
            if int(json.loads(cart.cart_info)["productInfo"]["attrInfo"]["id"])
            == int(sku)
        ]

        if not sku_items:
            # if verbose:
            #     print(f"订单{order_id}中不包含SKU:{sku}")
            continue

        if trade_status == "SUCCESS":
            for cart in sku_items:
                num = get_cart_num(cart)
                stats["sale_count"] += num
                stats["stock_out"] += num
                stats["orders"]["success"].append(
                    {"order_id": order_id, "num": num, "time": row[0]}
                )
                if verbose and init_stock:
                    current_stock -= num
                    print(
                        f"订单{order_id}销售{num}件, 时间:{row[0]}, 库存:{current_stock}"
                    )

        elif trade_status == "REFUND":
            after_sales = AfterSales.get_refunded(session, order_id)
            items = AfterSalesItem.query(session, after_sales.id)

            # 检查订单中是否包含目标SKU
            sku_items = [
                cart
                for cart in items
                if int(json.loads(cart.cart_info)["productInfo"]["attrInfo"]["id"])
                == int(sku)
            ]

            for cart in sku_items:
                num = get_cart_num(cart)
                stats["refund_count"] += num
                stats["stock_in"] += num
                stats["orders"]["refund"].append(
                    {"order_id": order_id, "num": num, "time": row[0]}
                )
                if verbose and init_stock:
                    current_stock += num
                    print(
                        f"订单{order_id}退款{num}件, 时间:{row[0]}, 库存:{current_stock}"
                    )

    # 统计临时库存变化
    # 时间format 为 "%Y%m%d
    summary_date = date_str.replace("-", "")
    tmp_stocks = TmpStock.queryAll(session, summary_date, 664, str(1837099064898891776))
    if tmp_stocks:
        for stock in tmp_stocks:
            stats["tmp_stock"]["stock_in"] += stock.total_stock_in or 0
            stats["tmp_stock"]["stock_out"] += stock.total_stock_out or 0
            stats["tmp_stock"]["adjustments"] += stock.inventory_adjustments or 0
            stats["tmp_stock"]["records"].append(
                {
                    "stock_in": stock.total_stock_in,
                    "stock_out": stock.total_stock_out,
                    "adjustments": stock.inventory_adjustments,
                    "synced": stock.synced,
                    "order_id": stock.order_id,
                    "time": (
                        stock.ctime.strftime("%Y-%m-%d %H:%M:%S")
                        if stock.ctime
                        else None
                    ),
                }
            )
            if verbose:
                print(
                    f"临时库存: {stock.total_stock_in} 入库, {stock.total_stock_out} 出库, {stock.inventory_adjustments} 调整, 订单: {stock.order_id}, 时间: {stock.ctime.strftime('%Y-%m-%d %H:%M:%S') if stock.ctime else None}"
                )

    if verbose:
        print(f"=== {date_str} SKU:{sku} 统计 ===")
        print(f"销售数量: {stats['sale_count']}")
        print(f"退款数量: {stats['refund_count']}")
        print(f"出库数量: {stats['stock_out']}")
        print(f"入库数量: {stats['stock_in']}")
        print(f"净销售: {stats['sale_count'] - stats['refund_count']}")
        print(f"成功订单数: {len(stats['orders']['success'])}")
        print(f"退款订单数: {len(stats['orders']['refund'])}")
        # 显示临时库存变化
        print("\n临时库存变化:")
        print(f"入库数量: {stats['tmp_stock']['stock_in']}")
        print(f"出库数量: {stats['tmp_stock']['stock_out']}")
        print(f"库存调整: {stats['tmp_stock']['adjustments']}")
        # if stats['tmp_stock']['records']:
        #     print("\n临时库存记录:")
        #     for record in stats['tmp_stock']['records']:
        #         print(f"时间: {record['time']}")
        #         print(f"订单: {record['order_id']}")
        #         print(f"入库: {record['stock_in']} 出库: {record['stock_out']} 调整: {record['adjustments']}")
        #         print(f"已同步: {'是' if record['synced'] else '否'}")
        #         print("---")

        # 在统计完订单后，添加比对逻辑
        print("\n=== 订单与临时库存比对 ===")
        # 获取所有出库订单ID
        outbound_orders = {order["order_id"] for order in stats["orders"]["success"]}
        # 获取临时库存中的订单ID
        tmp_stock_orders = {
            record["order_id"]
            for record in stats["tmp_stock"]["records"]
            if record["stock_out"]
        }

        # 找出在订单中但不在临时库存中的订单
        missing_orders = outbound_orders - tmp_stock_orders
        if missing_orders:
            print("\n以下订单在临时库存中未找到对应出库记录：")
            for order_id in missing_orders:
                # 找到对应订单的详细信息
                order_detail = next(
                    (
                        order
                        for order in stats["orders"]["success"]
                        if order["order_id"] == order_id
                    ),
                    None,
                )
                if order_detail:
                    print(
                        f"订单号: {order_id}, 数量: {order_detail['num']}, 时间: {order_detail['time']}"
                    )
        else:
            print("\n所有出库订单都能在临时库存中找到对应记录")

        # 检查数量是否匹配
        total_order_out = stats["stock_out"]
        total_tmp_stock_out = stats["tmp_stock"]["stock_out"]
        if total_order_out != total_tmp_stock_out:
            print("\n出库数量不匹配：")
            print(f"订单总出库: {total_order_out}")
            print(f"临时库存总出库: {total_tmp_stock_out}")
            print(f"差异: {total_order_out - total_tmp_stock_out}")

        # 在原有的比对逻辑后添加入库比对
        print("\n=== 入库记录比对 ===")
        # 获取所有退款入库订单ID
        inbound_orders = {order["order_id"] for order in stats["orders"]["refund"]}
        # 获取临时库存中的入库订单ID
        tmp_stock_inbound_orders = {
            record["order_id"]
            for record in stats["tmp_stock"]["records"]
            if record["stock_in"]
        }

        # 找出在退款记录中但不在临时库存中的订单
        missing_inbound_orders = inbound_orders - tmp_stock_inbound_orders
        if missing_inbound_orders:
            print("\n以下退款订单在临时库存中未找到对应入库记录：")
            for order_id in missing_inbound_orders:
                # 找到对应订单的详细信息
                order_detail = next(
                    (
                        order
                        for order in stats["orders"]["refund"]
                        if order["order_id"] == order_id
                    ),
                    None,
                )
                if order_detail:
                    print(
                        f"订单号: {order_id}, 数量: {order_detail['num']}, "
                        f"时间: {order_detail['time']}"
                    )
        else:
            print("\n所有退款入库订单都能在临时库存中找到对应记录")

        # 检查入库数量是否匹配
        total_order_in = stats["stock_in"]
        total_tmp_stock_in = stats["tmp_stock"]["stock_in"]
        if total_order_in != total_tmp_stock_in:
            print("\n入库数量不匹配：")
            print(f"订单总入库: {total_order_in}")
            print(f"临时库存总入库: {total_tmp_stock_in}")
            print(f"差异: {total_order_in - total_tmp_stock_in}")

    return stats


def main():
    """可以用以下方式使用这个脚本：
    1. 查询单个SKU某天的情况：
    python report_check.py -d 2024-05-06 -s your-sku-here -v
    2. 查询单个SKU一段时间的情况：
    python report_check.py -s 311 --start=2024-10-22 --end=2024-10-22 -v
    python report_check.py -s 664 --start=2025-01-06 --end=2025-01-06 -v
    3. 保持原有的账单查询功能：
    python report_check.py -d 2024-05-06 -o your-order-number
    """
    import argparse

    parser = argparse.ArgumentParser(description="检查微信支付账单")
    parser.add_argument("-d", "--date", help="账单日期 (YYYY-MM-DD)")
    parser.add_argument("-o", "--order", help="商户订单号")
    parser.add_argument("-s", "--sku", help="SKU编号 (双面海报 311)")
    parser.add_argument("-i", "--init_stock", help="初始库存")
    parser.add_argument("--start", help="开始日期 (YYYY-MM-DD)")
    parser.add_argument("--end", help="结束日期 (YYYY-MM-DD)")
    parser.add_argument("-v", "--verbose", action="store_true", help="显示详细信息")

    args = parser.parse_args()

    try:
        if args.sku:
            if args.start and args.end:
                # 统计时间段内的SKU情况
                start_date = datetime.datetime.strptime(args.start, "%Y-%m-%d")
                end_date = datetime.datetime.strptime(args.end, "%Y-%m-%d")
                current_date = start_date

                total_stats = {
                    "sale_count": 0,
                    "refund_count": 0,
                    "stock_in": 0,
                    "stock_out": 0,
                }

                while current_date <= end_date:
                    date_str = current_date.strftime("%Y-%m-%d")
                    stats = stat_sku_daily(
                        date_str, args.sku, args.verbose, int(args.init_stock)
                    )

                    # 累计统计数据
                    for key in total_stats:
                        total_stats[key] += stats[key]

                    current_date += datetime.timedelta(days=1)

                print("\n=== 时间段汇总统计 ===")
                print(f"时间范围: {args.start} 至 {args.end}")
                print(f"SKU: {args.sku}")
                print(f"总销售数量: {total_stats['sale_count']}")
                print(f"总退款数量: {total_stats['refund_count']}")
                print(f"总出库数量: {total_stats['stock_out']}")
                print(f"总入库数量: {total_stats['stock_in']}")
                print(
                    f"净销售数量: {total_stats['sale_count'] - total_stats['refund_count']}"
                )

            elif args.date:
                # 统计单日SKU情况
                stats = stat_sku_daily(
                    args.date, args.sku, args.verbose, int(args.init_stock)
                )
            else:
                print(
                    "使用SKU统计时，需要提供日期参数(-d)或时间范围参数(--start和--end)"
                )
                return
        else:
            # 原有的账单查询功能
            results = get_daily_bill(args.date, args.order)
            if not results:
                print("未找到任何记录")
                return

            for record in results:
                print(json.dumps(record, ensure_ascii=False, indent=2))
            print(f"\n找到记录数: {len(results)}")

    except Exception as e:
        print(f"错误: {str(e)}")


if __name__ == "__main__":
    main()
