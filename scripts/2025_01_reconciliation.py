import csv
import datetime
import sys
import orjson as json
import logging
import os
from decimal import Decimal
from enum import IntEnum

CABLE_APP_APPLICANT = "1636528171API"

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额
# https://pay.weixin.qq.com/index.php/xphp/cfund_bill_nc/funds_bill_nc#/
# 微信支付-交易中心-资金账单

# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    SKU_DEFAULT,
    AfterSales,
    AfterSalesItem,
    Cart,
    Order,
    Product,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    download_bill,
    filter_special,
    format_date,
    get_cart_key,
    get_cart_num,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    read_csv,
    read_json,
    read_wechat_pay_log,
    read_wechat_pay_log_with_summary,
    write_csv,
)

# 创建一个 Session 实例
session = get_session()


MAYBE_DELETED_ORDER_IDS = set([])


# 微信支付原始数据
WECHAT_RAW_DATA_ROOT = "/data/wechat_pay/raw"

REPORT_ROOT = "/data/wechat_pay/report"


class WechatPayBillIndex(IntEnum):
    """微信支付账单字段索引枚举
    用于访问账单CSV文件中的列
    """

    TRADE_TIME = 0  # 交易时间
    APP_ID = 1  # 公众账号ID
    MCH_ID = 2  # 商户号
    SUB_MCH_ID = 3  # 特约商户号
    DEVICE_ID = 4  # 设备号
    TRANSACTION_ID = 5  # 微信订单号
    OUT_TRADE_NO = 6  # 商户订单号
    OPENID = 7  # 用户标识
    TRADE_TYPE = 8  # 交易类型
    TRADE_STATUS = 9  # 交易状态
    BANK_TYPE = 10  # 付款银行
    FEE_TYPE = 11  # 货币种类
    SETTLEMENT_TOTAL_FEE = 12  # 应结订单金额
    COUPON_FEE = 13  # 代金券金额
    REFUND_ID = 14  # 微信退款单号
    OUT_REFUND_NO = 15  # 商户退款单号
    REFUND_FEE = 16  # 退款金额
    COUPON_REFUND_FEE = 17  # 充值券退款金额
    REFUND_TYPE = 18  # 退款类型
    REFUND_STATUS = 19  # 退款状态
    PRODUCT_NAME = 20  # 商品名称
    ATTACH = 21  # 商户数据包
    SERVICE_FEE = 22  # 手续费
    RATE = 23  # 费率
    TOTAL_FEE = 24  # 订单金额
    APPLY_REFUND_FEE = 25  # 申请退款金额
    RATE_REMARK = 26  # 费率备注


class WechatPayBillLabel:
    """微信支付账单字段标签
    用于显示账单字段的中文名称
    """

    LABELS = {
        WechatPayBillIndex.TRADE_TIME: "交易时间",
        WechatPayBillIndex.APP_ID: "公众账号ID",
        WechatPayBillIndex.MCH_ID: "商户号",
        WechatPayBillIndex.SUB_MCH_ID: "特约商户号",
        WechatPayBillIndex.DEVICE_ID: "设备号",
        WechatPayBillIndex.TRANSACTION_ID: "微信订单号",
        WechatPayBillIndex.OUT_TRADE_NO: "商户订单号",
        WechatPayBillIndex.OPENID: "用户标识",
        WechatPayBillIndex.TRADE_TYPE: "交易类型",
        WechatPayBillIndex.TRADE_STATUS: "交易状态",
        WechatPayBillIndex.BANK_TYPE: "付款银行",
        WechatPayBillIndex.FEE_TYPE: "货币种类",
        WechatPayBillIndex.SETTLEMENT_TOTAL_FEE: "应结订单金额",
        WechatPayBillIndex.COUPON_FEE: "代金券金额",
        WechatPayBillIndex.REFUND_ID: "微信退款单号",
        WechatPayBillIndex.OUT_REFUND_NO: "商户退款单号",
        WechatPayBillIndex.REFUND_FEE: "退款金额",
        WechatPayBillIndex.COUPON_REFUND_FEE: "充值券退款金额",
        WechatPayBillIndex.REFUND_TYPE: "退款类型",
        WechatPayBillIndex.REFUND_STATUS: "退款状态",
        WechatPayBillIndex.PRODUCT_NAME: "商品名称",
        WechatPayBillIndex.ATTACH: "商户数据包",
        WechatPayBillIndex.SERVICE_FEE: "手续费",
        WechatPayBillIndex.RATE: "费率",
        WechatPayBillIndex.TOTAL_FEE: "订单金额",
        WechatPayBillIndex.APPLY_REFUND_FEE: "申请退款金额",
        WechatPayBillIndex.RATE_REMARK: "费率备注",
    }

    @classmethod
    def get_label(cls, index):
        """获取字段标签"""
        return cls.LABELS.get(index, "未知字段")


def prase_trx_with_balance(balance: Decimal, trx, verbose=False):
    """解析交易记录，返回交易记录和余额"""
    order_id = trx[WechatPayBillIndex.OUT_TRADE_NO]
    # 交易类型
    trade_status = trx[WechatPayBillIndex.TRADE_STATUS]
    order = Order.query_by_trade_no(session, order_id)
    if not order:
        print("订单不存在: ", order_id)
        raise Exception("订单不存在")

    cart_ids = order.cart_id
    items = Cart.query(session, cart_ids, order_id)
    # if order.order_id == "1848548975171379200":
    #     print("cart_ids", cart_ids)
    #     print("items", items)
    # 2024-05-06 15:19:13
    # trade 时间
    trade_time = trx[0]
    # 转为 datetime
    trade_time = datetime.datetime.strptime(trade_time, "%Y-%m-%d %H:%M:%S")

    if verbose:
        print("*" * 80)

    rows = _prase_trx_with_balance(
        balance, trade_time, trade_status, order, items, trx, verbose
    )

    if rows:
        end_balance = rows[-1][4]
    else:
        end_balance = balance

    if verbose:
        # 计算净收支
        b = Decimal(0)
        for r in rows:
            print("end balance", r[4], r)
            b += r[2]
        print(f"{order_id}净收支:", b)
        print("*" * 80)
    return rows, end_balance


def distribute_fee(
    total_fee, items, cart_details=None, is_refund=False, is_all_after_sales=None
):
    """分摊规则: 邮费、手续费等按照商品金额比例分配

    Args:
        total_fee: 需要分摊的总费用
        items: 购物车项目列表
        cart_details: 购物车详情列表，包含价格信息。如果为None则按数量平均分配
        is_refund: 是否为退款场景，用于处理部分退款

    Returns:
        list: 分配到每个商品的费用列表
    """
    # 如果只有一个商品，直接返回总费用
    if len(items) == 1:
        return [round(total_fee, 2)]

    if cart_details:
        # 按照商品金额比例分配
        total_amount = Decimal("0")
        item_amounts = []

        # 计算订单总金额和每个商品金额
        for detail in cart_details:
            if is_refund:
                # 退款场景使用退款数量
                num = detail["refund_num"] if detail["is_after_sales"] else Decimal("0")
                if is_all_after_sales:
                    num = detail["num"]
            else:
                num = detail["num"]
            amount = num * detail["price"]
            total_amount += amount
            item_amounts.append(amount)

        # 处理全部金额为0的情况(比如非退款商品)
        if total_amount == 0:
            return [Decimal("0")] * len(cart_details)

        # 按比例分配费用
        distributed_fees = []
        for amount in item_amounts:
            ratio = amount / total_amount
            distributed_fee = round(total_fee * ratio, 2)
            distributed_fees.append(distributed_fee)

        # 处理分配后的小数点误差
        total_distributed = sum(distributed_fees)
        remainder = round(total_fee - total_distributed, 2)

        if remainder != 0:
            # 将剩余金额加到金额最大的商品上
            max_amount_index = item_amounts.index(max(item_amounts))
            distributed_fees[max_amount_index] += remainder

        return distributed_fees

    else:
        # 按照商品数量平均分配(原有逻辑)
        if is_refund:
            goods_count_list = [
                get_cart_num(cart) if cart.is_after_sales else 0 for cart in items
            ]
        else:
            goods_count_list = [get_cart_num(cart) for cart in items]

        goods_count = sum(goods_count_list)

        if goods_count == 0:
            return [Decimal("0")] * len(items)

        # 计算每件商品的基本分摊费
        per_item_fee = total_fee / goods_count
        per_item_fee = round(per_item_fee, 2)

        # 计算总分摊的费用
        total_distributed = per_item_fee * goods_count
        remainder = round(total_fee - total_distributed, 2)

        # 创建分摊结果列表
        item_fees = []
        for i in range(len(items)):
            num = goods_count_list[i]
            item_fees.append(num * per_item_fee)

        # 分配余数部分
        i = 0
        while remainder > 0:
            if goods_count_list[i] > 0:  # 只给有数量的商品分配余数
                item_fees[i] = round(item_fees[i] + Decimal("0.01"), 2)
                remainder = round(remainder - Decimal("0.01"), 2)
            i = (i + 1) % len(items)

        return item_fees


def _prase_trx_with_balance(
    balance: Decimal, trade_time, trade_status, order, items, trx, verbose=False
):
    """解析交易记录，返回交易记录和余额

    Args:
        balance: 余额
        trade_time: 交易时间
        trade_status: 交易状态
        order: 订单
        items: 购物车项目列表
        trx: 交易记录
        verbose: 是否打印详细日志

    Returns:
        list: 交易记录列表
    """
    rows = []
    if trade_status not in ("SUCCESS", "REFUND"):
        print("未知交易状态", trade_status)
        raise Exception("未知交易状态")

    # 微信账单中的手续费(有正负号)
    fee_in_trx_record = trx[WechatPayBillIndex.SERVICE_FEE].strip("`")
    fee_in_trx_record = Decimal(fee_in_trx_record)

    # 1. 预处理 cart 信息
    cart_details = []
    for cart in items:
        cart_info = json.loads(cart.cart_info)
        product_info = cart_info.get("productInfo")
        cart_details.append(
            {
                "num": Decimal(int(cart_info.get("cartNum"))),
                "refund_num": Decimal(int(cart_info.get("refundNum", 0))),
                "name": product_info.get("storeName"),
                "price": round(
                    Decimal(product_info.get("attrInfo", {}).get("price")), 2
                ),
                "sku": product_info.get("attrInfo", {}).get("sku"),
                "is_after_sales": cart.is_after_sales,
            }
        )

    # 实付
    total_pay = Decimal(order.pay_price)

    # 时间	收支类型	收入(元）	账单类型	余额	商品信息	SKU	数量	订单号
    trade_status_cn = trade_status == "SUCCESS" and "收入" or "支出"
    bill_type_cn = "未知"

    # 退款金额
    refund_price = Decimal(trx[WechatPayBillIndex.REFUND_FEE])

    # 全退的时候，不用看is_after_sales
    is_all_after_sales = False
    if trade_status == "REFUND":
        if len(items) == 1:
            is_all_after_sales = True
        else:
            is_after_sales_count = sum([cart.is_after_sales for cart in items])
            if is_after_sales_count == 0:
                is_all_after_sales = True

    # 数量
    total_num = Decimal(str(order.total_num))
    # 邮费
    total_postage = order.total_postage
    postage_fees = distribute_fee(
        total_postage,
        items,
        cart_details,
        is_refund=trade_status == "REFUND",
        is_all_after_sales=is_all_after_sales,
    )

    # 微信手续费已经有正负号，不需要再处理正负
    wechat_fee = Decimal(trx[WechatPayBillIndex.SERVICE_FEE])
    # 分摊手续费到各个商品
    wechat_fees = distribute_fee(
        wechat_fee,
        items,
        cart_details,
        is_refund=trade_status == "REFUND",
        is_all_after_sales=is_all_after_sales,
    )

    # 1. 处理单独的快递费退款
    # 如果是退款，且退款金额正好是10元，且没有商品退款，则认为是快递费退款
    # 判断是否为快递费退款 10元
    names = []
    for detail in cart_details:
        if trade_status == "REFUND":
            if detail["is_after_sales"] or is_all_after_sales:
                names.append(detail["name"])
            # 处理部分退款情况
            else:
                if detail["refund_num"] > 0:
                    names.append(detail["name"])
        else:
            names.append(detail["name"])
    name = "、".join(names)

    calc_fee = Decimal("0")

    # 给用户退扣掉的那10块钱
    if trade_status == "REFUND" and refund_price.compare(Decimal("10.00")) == 0:
        balance = balance + wechat_fee.copy_abs()
        # 不再细分到每个商品
        row = [
            trade_time,
            "收入",
            wechat_fee.copy_abs(),  # 使用Decimal的copy_abs()方法获取绝对值
            "手续费退回",
            balance,
            name,
            "-",  # SKU
            1,  # 数量
            "`" + order.order_id,
        ]
        rows.append(row)
        calc_fee = calc_fee + wechat_fee.copy_abs()

        # 同时需要
        # 快递拦截收入 10块 退回
        income = Decimal("-10")
        # 按照商品数量和价格比例分配收入
        total_amount = Decimal("0")
        item_amounts = []

        # 计算订单总金额和每个商品金额
        for detail in cart_details:
            amount = detail["num"] * detail["price"]
            total_amount += amount
            item_amounts.append(amount)

        # 按比例分配快递拦截收入
        distributed_refunds = []
        for amount in item_amounts:
            ratio = amount / total_amount
            distributed_refund = round(income * ratio, 2)
            distributed_refunds.append(distributed_refund)

        # 处理分配后的小数点误差
        total_distributed = sum(distributed_refunds)
        remainder = round(income - total_distributed, 2)
        if remainder != 0:
            # 将剩余金额加到金额最大的商品上
            max_amount_index = item_amounts.index(max(item_amounts))
            distributed_refunds[max_amount_index] += remainder

        # 为每个商品生成对应的流水记录
        for i, detail in enumerate(cart_details):
            balance = balance + distributed_refunds[i]
            bill_type_cn = "快递拦截收入退回"
            row = [
                trade_time,
                "退回",
                distributed_refunds[i],  # 分配后的收入金额
                bill_type_cn,
                balance,
                detail["name"],
                detail["sku"],
                detail["num"],
                "`" + order.order_id,
            ]
            rows.append(row)
            if verbose:
                print("快递拦截收入分配", row)

        if verbose:
            print("单独快递费退款", row)

        if calc_fee != fee_in_trx_record.copy_abs():
            msg = f"快递拦截收入手续费退回计算错误: {calc_fee} != {fee_in_trx_record.copy_abs()}"
            raise Exception(msg)
        return rows
    # 其他
    # 扣除10块的全额退款情况下，的快递拦截收入
    elif (
        trade_status == "REFUND"
        and refund_price.compare(total_pay - Decimal("10.00")) == 0
    ):
        # 快递拦截收入 10块
        income = Decimal("10")
        # 按照商品数量和价格比例分配收入
        total_amount = Decimal("0")
        item_amounts = []

        # 计算订单总金额和每个商品金额
        for detail in cart_details:
            amount = detail["num"] * detail["price"]
            total_amount += amount
            item_amounts.append(amount)

        # 按比例分配快递拦截收入
        distributed_refunds = []
        for amount in item_amounts:
            ratio = amount / total_amount
            distributed_refund = round(income * ratio, 2)
            distributed_refunds.append(distributed_refund)

        # 处理分配后的小数点误差
        total_distributed = sum(distributed_refunds)
        remainder = round(income - total_distributed, 2)
        if remainder != 0:
            # 将剩余金额加到金额最大的商品上
            max_amount_index = item_amounts.index(max(item_amounts))
            distributed_refunds[max_amount_index] += remainder

        # 为每个商品生成对应的流水记录
        for i, detail in enumerate(cart_details):
            balance = balance + distributed_refunds[i]
            bill_type_cn = "快递拦截收入"
            row = [
                trade_time,
                "收入",
                distributed_refunds[i],  # 分配后的收入金额
                bill_type_cn,
                balance,
                detail["name"],
                detail["sku"],
                detail["num"],
                "`" + order.order_id,
            ]
            rows.append(row)
            if verbose:
                print("快递拦截收入分配", row)

    # 2. 原有的处理逻辑
    # 其他退款、其他支付
    for i, detail in enumerate(cart_details):
        # 2.1 货品
        balance_before = balance
        amount = detail["num"] * detail["price"]
        if trade_status == "REFUND":
            if detail["is_after_sales"] or is_all_after_sales:
                # 全部退款
                refund_amount = detail["num"] * detail["price"] * Decimal("-1")
                balance = balance + refund_amount
                bill_type_cn = "退款支出"
                row = [
                    trade_time,
                    trade_status_cn,
                    refund_amount,
                    bill_type_cn,
                    balance,
                    detail["name"],
                    detail["sku"],
                    detail["num"],
                    "`" + order.order_id,
                ]
                rows.append(row)
            else:
                # 部分退款（收到货后）
                refund_num = detail["refund_num"]
                if refund_num > 0:
                    refund_amount = refund_num * detail["price"] * Decimal("-1")
                    balance = balance + refund_amount
                    bill_type_cn = "退款支出"
                    row = [
                        trade_time,
                        trade_status_cn,
                        refund_amount,
                        bill_type_cn,
                        balance,
                        detail["name"],
                        detail["sku"],
                        refund_num,
                        "`" + order.order_id,
                    ]
                    rows.append(row)
                else:
                    msg = f"未知的退款情况: order: {order.order_id} {detail['name']} {detail['sku']} {detail['num']} {detail['refund_num']}"
                    raise Exception(msg)
        elif trade_status == "SUCCESS":
            balance = balance + amount
            bill_type_cn = "货款收入"
            row = [
                trade_time,
                trade_status_cn,
                amount,
                bill_type_cn,
                balance,
                detail["name"],
                detail["sku"],
                detail["num"],
                "`" + order.order_id,
            ]
            rows.append(row)
        if verbose:
            print(
                bill_type_cn,
                amount,
                "balance_before:",
                balance_before,
                "balance:",
                balance,
            )

        # 2.2 邮费
        postage = postage_fees[i]
        # 记录邮费收入、支出
        if postage > 0:
            if trade_status == "REFUND":
                balance = balance - postage
                bill_type_cn = "邮费退回"
                amount = postage * Decimal(-1)
                row = [
                    trade_time,
                    "支出",
                    amount,
                    bill_type_cn,
                    balance,
                    detail["name"],
                    detail["sku"],
                    detail["num"],
                    "`" + order.order_id,
                ]
                rows.append(row)
                if verbose:
                    print("邮费退回", row)
            elif trade_status == "SUCCESS":
                balance = balance + postage
                bill_type_cn = "邮费收入"
                row = [
                    trade_time,
                    "收入",
                    postage,
                    bill_type_cn,
                    balance,
                    detail["name"],
                    detail["sku"],
                    detail["num"],
                    "`" + order.order_id,
                ]
                rows.append(row)
                if verbose:
                    print("邮费收入", row)

        # 2.3 手续费，收款时候扣除微信支付手续费，退款时候退回
        w_fee = wechat_fees[i]
        if trade_status == "REFUND":
            if detail["is_after_sales"] or is_all_after_sales:
                # 退款时，手续费退回，所以是加上手续费的绝对值
                balance = balance + abs(w_fee)
                bill_type_cn = "手续费收入"
                row = [
                    trade_time,
                    "收入",
                    abs(w_fee),  # 使用绝对值
                    bill_type_cn,
                    balance,
                    detail["name"],
                    detail["sku"],
                    detail["num"],
                    "`" + order.order_id,
                ]
                rows.append(row)

                calc_fee = calc_fee + w_fee.copy_abs()
                if verbose:
                    print("手续费收入", row)
            else:
                # 部分退款（收到货后）
                refund_num = detail["refund_num"]
                if refund_num > 0:
                    # 按退款比例计算退回的手续费
                    refund_w_fee = abs(w_fee) * refund_num / detail["num"]
                    balance = balance + refund_w_fee
                    bill_type_cn = "手续费收入"
                    row = [
                        trade_time,
                        "收入",
                        refund_w_fee,
                        bill_type_cn,
                        balance,
                        detail["name"],
                        detail["sku"],
                        refund_num,
                        "`" + order.order_id,
                    ]
                    rows.append(row)
                    calc_fee = calc_fee + w_fee.copy_abs()
                    if verbose:
                        print("手续费收入", row)
                else:
                    # 不会出现
                    msg = f"未知的退款情况: order: {order.order_id} {detail['name']} {detail['sku']} {detail['num']} {detail['refund_num']}"
                    raise Exception(msg)
        elif trade_status == "SUCCESS":
            # 支付时，扣除手续费，所以是减去手续费的绝对值
            balance = balance - abs(w_fee)
            bill_type_cn = "手续费支出"
            row = [
                trade_time,
                "支出",
                -abs(w_fee),  # 使用负的绝对值
                bill_type_cn,
                balance,
                detail["name"],
                detail["sku"],
                detail["num"],
                "`" + order.order_id,
            ]
            rows.append(row)

            calc_fee = calc_fee + w_fee.copy_abs()
            if verbose:
                print("手续费支出", row)

    if calc_fee != fee_in_trx_record.copy_abs():
        msg = f"手续费计算错误: {calc_fee} != {fee_in_trx_record.copy_abs()}"
        raise Exception(msg)
    return rows


def check_balance(start, end, verbose=False):
    # 每日10点更新前一天，11日9:00 获取9日，11日10:00 获取10日
    # 检查start end是否合法
    if not start or not end:
        print("check_balance 请输入开始结束时间")
        return False

    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    # 前两天
    cur_valid = now - datetime.timedelta(days=2)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=1)

    if start <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("check_balance start 非法")
        return False
    if end <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("check_balance end 非法:", end, "valid:", cur_valid.strftime("%Y-%m-%d"))
        return False

    start_balance = Decimal("0")

    pre_day = datetime.datetime.strptime(start, "%Y-%m-%d") - datetime.timedelta(days=1)
    rows = read_csv(REPORT_ROOT + f"/流水数据{pre_day.strftime('%Y-%m-%d')}.csv", True)
    while not rows:
        pre_day = pre_day - datetime.timedelta(days=1)
        rows = read_csv(
            REPORT_ROOT + f"/流水数据{pre_day.strftime('%Y-%m-%d')}.csv", True
        )
    row = rows[-1]
    start_balance = Decimal(filter_special(row[4]))
    print(f"{pre_day} 期初余额", start_balance)

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date
    while cur_date <= end_date:
        rows = read_csv(
            REPORT_ROOT + f"/流水数据{cur_date.strftime('%Y-%m-%d')}.csv", True
        )
        if rows:
            row = rows[-1]
            end_balance = Decimal(filter_special(row[4]))
            print(f"{cur_date.strftime('%Y-%m-%d')} 日终余额", end_balance)
        else:
            print(f"{cur_date.strftime('%Y-%m-%d')} 余额无变动")
        cur_date = cur_date + datetime.timedelta(days=1)


def reconciliation(start, end, accuracy_start_balance=None, verbose=False):
    """对账

    start: 开始时间
    end: 结束时间
    示例： download_bill('2021-05-01', '2021-05-31')
    """
    # 每日10点更新前一天，11日9:00 获取9日，11日10:00 获取10日
    # 检查start end是否合法
    if not start or not end:
        print("gen_report 请输入开始结束时间")
        return False

    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    # 前两天
    cur_valid = now - datetime.timedelta(days=2)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=1)

    if start <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("gen_report start 非法")
        return False
    if end <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print(
            "gen_report end 非法:", end, "修正为valid:", cur_valid.strftime("%Y-%m-%d")
        )
        end = cur_valid.strftime("%Y-%m-%d")

    start_balance = Decimal("0")
    # 递归获取前一天的余额，最多迭代到2024-04-01
    pre_day = datetime.datetime.strptime(start, "%Y-%m-%d") - datetime.timedelta(days=1)
    rows = read_csv(REPORT_ROOT + f"/流水数据{pre_day.strftime('%Y-%m-%d')}.csv")
    while not rows:
        pre_day = pre_day - datetime.timedelta(days=1)
        rows = read_csv(REPORT_ROOT + f"/流水数据{pre_day.strftime('%Y-%m-%d')}.csv")
        print("获取前一天余额", pre_day.strftime("%Y-%m-%d"))
    row = rows[-1]
    start_balance = Decimal(filter_special(row[4]))

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date
    while cur_date <= end_date:
        # 1. 读取微信原始数据
        # 微信支付账单2024-05-06.csv
        s = cur_date.strftime("%Y-%m-%d")
        e = (cur_date + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        cur_date_str = cur_date.strftime("%Y-%m-%d")
        bill_csv_path = WECHAT_RAW_DATA_ROOT + f"/微信支付账单{cur_date_str}.csv"

        print(f"{s} 期初余额: {start_balance}")
        if not os.path.exists(bill_csv_path):
            print("账单数据不存在", bill_csv_path)
            raise Exception("账单数据不存在")

        rows, summary = read_wechat_pay_log_with_summary(bill_csv_path)
        # 按照第一列排序
        rows = sorted(rows, key=lambda x: x[0])

        total_income = Decimal(summary.get("净收入", "0"))
        total_fee_balance = Decimal(summary.get("手续费总金额", "0"))
        total_refund_balance = Decimal(summary.get("退款总金额", "0"))
        total_order_balance = Decimal(summary.get("订单总金额", "0"))

        # 检查total_income 是否等于 total_order_balance - total_fee_balance - total_refund_balance
        if (
            total_income
            != total_order_balance - total_fee_balance - total_refund_balance
        ):
            msg = f"净收入不等于订单总金额-手续费总金额-退款总金额: {total_income} != {total_order_balance} - {total_fee_balance} - {total_refund_balance}"
            raise Exception(msg)

        # 2. 解析微信支付账单
        print("解析微信支付账单中")
        flow_rows = []
        for idx, row in enumerate(rows):
            # if idx != (len(rows) - 1) and idx != (len(rows) - 2):
            #     # 跳过最后的合计的两行
            #     row_out_trade_no = row[WechatPayBillIndex.OUT_TRADE_NO]
            #     if out_trade_no and row_out_trade_no != out_trade_no:
            #         continue
            tmp_rows, start_balance = prase_trx_with_balance(
                start_balance, row, verbose
            )
            flow_rows.extend(tmp_rows)

        if verbose:
            print(f"{s} 未计算资金流水之前的日终余额: {start_balance}")

        # 检查
        fee_calc_total = Decimal("0")
        for flow_row in flow_rows:
            bill_type_cn = flow_row[3]
            if bill_type_cn == "手续费收入":
                fee_calc_total += flow_row[2]
            elif bill_type_cn == "手续费支出":
                fee_calc_total += flow_row[2]

        fee_calc_total = fee_calc_total * Decimal("-1")
        if fee_calc_total != total_fee_balance:
            msg = f"资金流水计算总手续费金额不等于手续费总金额: {fee_calc_total} != {total_fee_balance}"
            raise Exception(msg)

        # part 2 外部及系统资金变动
        # 资金账单中的提现操作进行处理
        fund_json_path = WECHAT_RAW_DATA_ROOT + f"/资金账单{cur_date_str}.json"
        if verbose:
            print(f"读取资金账单: {fund_json_path}")
        fund_data = read_json(fund_json_path)
        wx_pay_fund_flow_base_result_list = fund_data.get(
            "wxPayFundFlowBaseResultList", []
        )
        trx_fund_list = [
            f
            for f in wx_pay_fund_flow_base_result_list
            if f["bizName"] in ("交易", "退款")
        ]

        debug_date_str = "2025-01-06"
        # 若业务凭证号找不到对应的order，说明为小程序外部交易
        for f in trx_fund_list:
            bizName = f["bizName"]
            bizType = f["bizType"]
            bizVoucherId = f["bizVoucherId"]
            bizTransactionId = f["bizTransactionId"]
            fundFlowId = f["fundFlowId"]
            fundApplicant = f["fundApplicant"]

            memo = f["memo"]
            # 业务凭证号
            # 支付为订单号或扩展订单号
            # 退款为退款单号（随机生成）
            trade_no = bizVoucherId
            order = Order.query_by_trade_no(session, trade_no)
            if order:
                if trade_no in MAYBE_DELETED_ORDER_IDS:
                    if (
                        cur_date_str == debug_date_str
                        and trade_no in MAYBE_DELETED_ORDER_IDS
                    ):
                        print(
                            "跳过了已删除订单",
                            trade_no,
                            bizTransactionId,
                            fundFlowId,
                            memo,
                        )
                continue
            else:
                if (
                    bizName == "退款"
                    and bizType == "退款"
                    and (CABLE_APP_APPLICANT == fundApplicant or "退款总金额" in memo)
                ):
                    # 退款单号没存，但退款在支付账单中已计算，跳过
                    continue
                if cur_date_str == debug_date_str:
                    print(
                        "订单未找到，疑似外部交易",
                        trade_no,
                        bizTransactionId,
                        fundFlowId,
                        memo,
                    )
            if bizName == "交易":
                if bizType == "交易":
                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    start_balance += Decimal(financialFee)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        start_balance,
                        "外部收款",
                        "外部收款",
                        Decimal("1"),
                        "-",
                    ]
                    if cur_date_str == debug_date_str:
                        print("外部收款", row)
                    if verbose:
                        print("资金账单交易", row)
                    flow_rows.append(row)
                elif bizType == "扣除交易手续费":
                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    start_balance -= Decimal(financialFee)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        start_balance,
                        "外部交易手续费",
                        "外部交易手续费",
                        Decimal("1"),
                        "-",
                    ]
                    if cur_date_str == debug_date_str:
                        print("外部收款扣除交易手续费", row)
                    if verbose:
                        print("资金账单交易", row)
                    flow_rows.append(row)
                else:
                    msg = f"未知的资金账单类型: {bizName} {bizType}"
                    raise Exception(msg)
            elif bizName == "退款":
                if bizType == "退款":
                    # 先查询该批次的关联微信支付订单 4200002553202501103709602511
                    fundFlowId = f["fundFlowId"]
                    # 查询该批次下对应的支付订单
                    # flow_record_list = [f for f in trx_fund_list if f["bizName"] == "交易" and f["bizTransactionId"] == fundFlowId]

                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    start_balance -= Decimal(financialFee)

                    print("调试外部付款原始数据", f)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        start_balance,
                        "外部退款",
                        "外部退款",
                        Decimal("1"),
                        "-",
                    ]
                    if cur_date_str == debug_date_str:
                        print("外部退款", row)
                    if verbose:
                        print("资金账单交易", row)
                    flow_rows.append(row)
                else:
                    msg = f"未知的资金账单类型: {bizName} {bizType}"
                    raise Exception(msg)
            else:
                msg = f"未知的资金账单类型: {bizName} {bizType}"
                raise Exception(msg)

        other_fund_list = [
            f
            for f in wx_pay_fund_flow_base_result_list
            if f["bizName"] not in ("交易", "退款")
        ]
        for f in other_fund_list:
            bizName = f["bizName"]
            bizType = f["bizType"]
            if bizName == "充值/提现":
                if bizType == "提现":
                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    start_balance -= Decimal(financialFee)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        start_balance,
                        "系统",
                        "系统",
                        Decimal("1"),
                        "-",
                    ]
                    flow_rows.append(row)
                    if verbose:
                        print("资金账单交易", row)
                elif bizType == "充值":
                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    start_balance += Decimal(financialFee)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        start_balance,
                        "系统",
                        "系统",
                        Decimal("1"),
                        "-",
                    ]
                    if verbose:
                        print("资金账单交易", row)
                    flow_rows.append(row)
                else:
                    msg = f"未知的资金账单类型: {bizName} {bizType}"
                    raise Exception(msg)
            else:
                msg = f"未知的资金账单类型: {bizName} {bizType}"
                raise Exception(msg)

        # 提现后，可用余额减少
        if not flow_rows:
            # 余额不变化
            print(f"{s} 日终余额(无变化): {start_balance}")
            # 无变化时不出流水单
        else:
            print(f"{s} 日终余额: {start_balance}")
            report_csv_path = REPORT_ROOT + f"/流水数据{cur_date_str}.csv"
            if os.path.exists(report_csv_path):
                os.remove(report_csv_path)  # 删除旧文件
            write_csv(report_csv_path, flow_rows)

        print("=" * 80)
        cur_date = cur_date + datetime.timedelta(days=1)

    print("生成结束")
    return True


def stat(start, end, verbose=False):
    """统计已生成的流水
    start: 开始时间
    end: 结束时间
    示例： stat('2021-05-01', '2021-05-31')
    """
    # 每日10点更新前一天，11日9:00 获取9日，11日10:00 获取10日
    # 检查start end是否合法
    if not start or not end:
        print("stat 请输入开始结束时间")
        return False

    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    # 前两天
    cur_valid = now - datetime.timedelta(days=1)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=0)

    if start <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("start 非法")
        return False
    if end <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("end 非法:", end, "valid:", cur_valid.strftime("%Y-%m-%d"))
        return False

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date
    # 商品id-sku组成的键，值为净销售数量
    total_sale_map = {}
    total_refund_map = {}
    while cur_date < end_date:
        s = cur_date.strftime("%Y-%m-%d")
        e = (cur_date + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        # 微信支付账单2024-05-06.csv
        cur_date_str = cur_date.strftime("%Y-%m-%d")
        bill_csv_path = WECHAT_RAW_DATA_ROOT + f"/微信支付账单{cur_date_str}.csv"
        sale_map, refund_map = cal(cur_date_str, bill_csv_path)
        for key in sale_map:
            tmp = total_sale_map.get(key, 0)
            tmp += sale_map[key]
            total_sale_map[key] = tmp
        for key in refund_map:
            tmp = total_refund_map.get(key, 0)
            tmp += refund_map[key]
            total_refund_map[key] = tmp
        cur_date = cur_date + datetime.timedelta(days=1)

    print("=" * 80)
    print("=" * 80)
    print("=" * 80)
    print("总计")
    key_set = set()
    for key in total_sale_map:
        key_set.add(key)
    for key in total_refund_map:
        key_set.add(key)
    for key in sorted(key_set):
        total_sale_num = total_sale_map.get(key, 0)
        total_refund_num = total_refund_map.get(key, 0)
        pid, sku = key.split("--")
        p = Product.get(session, pid)
        name = "%s - %s" % (p.name, sku)
        print(
            f"{name} 售出数量: ",
            total_sale_num,
            "商品退款数量: ",
            total_refund_num,
            "净售出:",
            total_sale_num - total_refund_num,
        )
    print("=" * 80)
    print("=" * 80)
    print("=" * 80)


def cal(day, f_path):
    rows = read_wechat_pay_log(f_path, verbose=False)
    # 净卖出商品数量
    total_sale_map = {}
    total_refund_map = {}
    if not rows:
        return total_sale_map, total_refund_map

    key_set = set()
    for idx, row in enumerate(rows):
        # 输出处理进度 500个一次
        # if idx % 500 == 0:
        #     print(f_path, "处理进度: ", idx, "/", len(rows))
        order_id = row[6]
        # 交易类型
        trade_status = row[9]
        order = Order.query_by_trade_no(session, order_id)
        if not order:
            print("订单不存在: ", order_id)
            continue

        cart_ids = order.cart_id
        items = Cart.query(session, cart_ids, order_id)
        order.items = items
        for cart in items:
            key = get_cart_key(cart)
            key_set.add(key)

        # 2024-05-06 15:19:13
        # trade 时间
        trade_time = row[0]
        # 转为 datetime
        trade_time = datetime.datetime.strptime(trade_time, "%Y-%m-%d %H:%M:%S")

        if trade_status == "SUCCESS":
            succ_map = count_row(order, verbose=True)
            for key in succ_map:
                tmp = total_sale_map.get(key, 0)
                tmp += succ_map[key]
                total_sale_map[key] = tmp
        elif trade_status == "REFUND":
            refund_map = count_row(order, True, verbose=True)
            for key in refund_map:
                tmp = total_refund_map.get(key, 0)
                tmp += refund_map[key]
                total_refund_map[key] = tmp
        else:
            print("未知交易状态", trade_status)

    for key in sorted(key_set):
        total_sale_num = total_sale_map.get(key, 0)
        total_refund_num = total_refund_map.get(key, 0)
        if total_sale_num - total_refund_num == 0:
            # 当日无变动
            continue
        pid, sku = key.split("--")
        p = Product.get(session, pid)
        name = "%s - %s" % (p.name, sku)
        print(
            f"{day} {name} 售出数量: ",
            total_sale_num,
            "商品退款数量: ",
            total_refund_num,
            "净售出:",
            total_sale_num - total_refund_num,
        )
    print("*" * 80)
    return total_sale_map, total_refund_map


def count_row(order, is_refund=False, verbose=False):
    items = order.items

    is_all_after_sales = False
    if is_refund:
        if len(items) == 1:
            is_all_after_sales = True
        else:
            is_after_sales_count = sum([cart.is_after_sales for cart in items])
            if is_after_sales_count == 0:
                is_all_after_sales = True

    count_map = {}
    for cart in items:
        key = get_cart_key(cart)
        product_id, sku = key.split("--")
        if is_refund:
            if is_all_after_sales:
                p_num = get_certain_cart_sku_num(
                    cart, product_id, sku=sku, verbose=verbose
                )
                cur_num = count_map.get(key, 0)
                cur_num += p_num
                count_map[key] = cur_num
            elif cart.is_after_sales:
                p_num = get_certain_cart_sku_num(
                    cart, product_id, sku=sku, verbose=verbose
                )
                cur_num = count_map.get(key, 0)
                cur_num += p_num
                count_map[key] = cur_num
        else:
            p_num = get_certain_cart_sku_num(cart, product_id, sku=sku, verbose=verbose)
            cur_num = count_map.get(key, 0)
            cur_num += p_num
            count_map[key] = cur_num
    return count_map


def fix_daily_stock_data(date, start_stock):
    """修复每日库存数据，补充order_id"""
    print("date", date)
    pid = CD2_PID
    sku_unique = CD2_SKU_UNIQUE

    rows = read_csv(REPORT_ROOT + f"/流水数据{date}.csv")

    out_map = {}
    in_map = {}
    succ_order_count = 0
    refund_order_count = 0
    for r in rows:
        name = r[5]
        num = int(r[7])
        order_id = r[8].replace("`", "")
        kind = r[3]
        if name != "CD-《冀西南林路行》":
            # print("跳过", r)
            continue
        if kind == "货款收入":
            # print(r)
            out_map[order_id] = num
            succ_order_count += 1
        elif kind == "退款支出":
            # print(r)
            refund_order_count += 1
            in_map[order_id] = num

    stock_out = 0
    stock_in = 0
    skip_set = set()
    for r in rows:
        name = r[5]
        num = int(r[7])
        order_id = r[8].replace("`", "")
        kind = r[3]
        if name != "CD-《冀西南林路行》":
            continue
        if out_map.get(order_id) and in_map.get(order_id):
            if out_map[order_id] == in_map[order_id]:
                # print("当日本订单，退货数量等于出货数量，跳过出入库", order_id)
                skip_set.add(order_id)
                continue
            else:
                print("当日本订单，付款、退款库存出入数量不等", order_id)
                raise Exception("当日本订单，付款、退款库存出入数量不等")
        if kind == "货款收入":
            # print(r)
            stock_out += num
        elif kind == "退款支出":
            # print(r)
            stock_in += num

    print("下单后退款的订单数量:", len(skip_set))
    # 查询当日临时库存的出入库记录
    records = query_tmp_stock(date, pid, sku_unique, verbose=False)
    tmp_in = sum([r.total_stock_in for r in records])
    tmp_out = sum([r.total_stock_out for r in records])
    print("临时库存出库数量", tmp_out)
    print("临时库存入库数量", tmp_in)
    print("库存显示净出库量", tmp_out - tmp_in)

    print("*" * 80)

    print("出库数量", stock_out)
    print("入库数量", stock_in)

    print("净出库量", stock_out - stock_in)
    print("期初库存", start_stock)
    print("期末库存", start_stock - stock_out + stock_in)
    print("*" * 80)

    print("*" * 80)
    print("临时库存比对-- 取消、关闭的订单支付商品数量", tmp_out - stock_out)
    print("临时库存比对-- 取消、关闭的订单商品数量", tmp_in - stock_in)
    # !!!0506当日回滚时候多入库了9件商品
    # 查询当日取消、关闭的订单数量
    next_day = (
        datetime.datetime.strptime(date, "%Y-%m-%d") + datetime.timedelta(days=1)
    ).strftime("%Y-%m-%d")
    os = Order.query_range(session, date, next_day)
    cancel_close_os = [o for o in os if o.cancel_status not in (0,) and o.is_del == 0]

    print("*" * 80)
    print("付款订单数", succ_order_count)
    print("退款订单数", refund_order_count)
    print("订单比对-- 取消、关闭的订单数量", len(cancel_close_os))
    cancel_close_sku_count = 0
    for o in cancel_close_os:
        items = Cart.query(session, o.cart_id, o.order_id)
        num = sum([get_certain_cart_sku_num(c, pid, verbose=False) for c in items])
        cancel_close_sku_count += num
    print("订单比对-- 取消、关闭的商品数量", cancel_close_sku_count)

    delete_close_os = [o for o in os if o.is_del in (1,)]
    print("订单比对-- 删除的订单数量", len(delete_close_os))
    delete_close_sku_count = 0
    for o in delete_close_os:
        items = Cart.query(session, o.cart_id, o.order_id)
        num = sum([get_certain_cart_sku_num(c, pid, verbose=False) for c in items])
        delete_close_sku_count += num
    print("订单比对-- 删除的商品数量", delete_close_sku_count)
    print("*" * 80)

    print("=" * 80)
    print("=" * 80)
    print("=" * 80)


def query_tmp_stock(
    day="2024-05-06", pid=CD2_PID, sku_unique=CD2_SKU_UNIQUE, verbose=False
):
    q = day.replace("-", "")
    summary = TmpStockSummary.query(session, q, pid, sku_unique)
    if not summary:
        # print(f"{day} 库存无变化")
        # return []
        pass

    records = TmpStock.queryAll(session, q, pid, sku_unique)
    record_in = sum([r.total_stock_in for r in records])
    record_out = sum([r.total_stock_out for r in records])
    if verbose:
        if summary:
            print(
                f"{day}, 出库：{summary.total_stock_out}, 入库：{summary.total_stock_in}，库存调整：{summary.inventory_adjustments}, 同步: {summary.synced}"
            )
        print(f"{day} record_in: {record_in}, record_out: {record_out}")
    return records


if __name__ == "__main__":
    # 命令行参数，操作类型download_bill,gen_report; 日期范围start,end;verbose
    from optparse import OptionParser

    parser = OptionParser(usage="""微信账单和流水生成""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    parser.add_option(
        "-s", "--start", dest="start", help="start date", default="2025-01-06"
    )
    parser.add_option("-e", "--end", dest="end", help="end date", default=None)
    parser.add_option(
        "-t", "--test", dest="test", action="store_true", help="测试模式", default=False
    )
    parser.add_option(
        "-a",
        "--accuracy_start_balance",
        dest="accuracy_start_balance",
        help="对账日期初余额",
    )
    options, args = parser.parse_args()
    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    if options.test:
        wechat_fee = Decimal("2.52000")
        cart_details = [
            {
                "num": Decimal("2"),
                "refund_num": Decimal("0"),
                "name": "万能青年旅店《冀西南林路行》专辑双面海报",
                "price": Decimal("120.00"),
                "sku": "默认",
                "is_after_sales": True,
            },
            {
                "num": Decimal("1"),
                "refund_num": Decimal("0"),
                "name": "CD-《冀西南林路行》",
                "price": Decimal("180.00"),
                "sku": "默认",
                "is_after_sales": True,
            },
        ]
        cart_item_ids = ["82520", "82545"]
        cart_items = Cart.query(session, ",".join(cart_item_ids), "1876084734732312576")
        dfs = distribute_fee(wechat_fee, cart_items, cart_details, False, False)
        print("dfs", dfs)
        session.close()
        sys.exit(0)

    end = options.end
    if not end:
        end = options.start

    print(f"start: {options.start}, end: {end}")

    reconciliation(options.start, end, options.accuracy_start_balance, options.verbose)

    session.close()
