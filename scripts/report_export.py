import csv
import datetime
from typing import List
import orjson as json
import logging
import os
from decimal import Decimal
from enum import IntEnum

CABLE_APP_APPLICANT = "1636528171API"

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额
# https://pay.weixin.qq.com/index.php/xphp/cfund_bill_nc/funds_bill_nc#/
# 微信支付-交易中心-资金账单

# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    SKU_DEFAULT,
    AfterSales,
    AfterSalesItem,
    Cart,
    Order,
    Product,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    download_bill,
    filter_special,
    format_date,
    get_cart_key,
    get_cart_num,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    read_csv,
    read_json,
    read_wechat_pay_log,
    write_csv,
)

# 创建一个 Session 实例
session = get_session()


MAYBE_DELETED_ORDER_IDS = set([])


# 微信支付原始数据
WECHAT_RAW_DATA_ROOT = "/data/wechat_pay/raw"

REPORT_ROOT = "/data/wechat_pay/report"


class WechatPayBillIndex(IntEnum):
    """微信支付账单字段索引枚举
    用于访问账单CSV文件中的列
    """

    TRADE_TIME = 0  # 交易时间
    APP_ID = 1  # 公众账号ID
    MCH_ID = 2  # 商户号
    SUB_MCH_ID = 3  # 特约商户号
    DEVICE_ID = 4  # 设备号
    TRANSACTION_ID = 5  # 微信订单号
    OUT_TRADE_NO = 6  # 商户订单号
    OPENID = 7  # 用户标识
    TRADE_TYPE = 8  # 交易类型
    TRADE_STATUS = 9  # 交易状态
    BANK_TYPE = 10  # 付款银行
    FEE_TYPE = 11  # 货币种类
    SETTLEMENT_TOTAL_FEE = 12  # 应结订单金额
    COUPON_FEE = 13  # 代金券金额
    REFUND_ID = 14  # 微信退款单号
    OUT_REFUND_NO = 15  # 商户退款单号
    REFUND_FEE = 16  # 退款金额
    COUPON_REFUND_FEE = 17  # 充值券退款金额
    REFUND_TYPE = 18  # 退款类型
    REFUND_STATUS = 19  # 退款状态
    PRODUCT_NAME = 20  # 商品名称
    ATTACH = 21  # 商户数据包
    SERVICE_FEE = 22  # 手续费
    RATE = 23  # 费率
    TOTAL_FEE = 24  # 订单金额
    APPLY_REFUND_FEE = 25  # 申请退款金额
    RATE_REMARK = 26  # 费率备注


class WechatPayBillLabel:
    """微信支付账单字段标签
    用于显示账单字段的中文名称
    """

    LABELS = {
        WechatPayBillIndex.TRADE_TIME: "交易时间",
        WechatPayBillIndex.APP_ID: "公众账号ID",
        WechatPayBillIndex.MCH_ID: "商户号",
        WechatPayBillIndex.SUB_MCH_ID: "特约商户号",
        WechatPayBillIndex.DEVICE_ID: "设备号",
        WechatPayBillIndex.TRANSACTION_ID: "微信订单号",
        WechatPayBillIndex.OUT_TRADE_NO: "商户订单号",
        WechatPayBillIndex.OPENID: "用户标识",
        WechatPayBillIndex.TRADE_TYPE: "交易类型",
        WechatPayBillIndex.TRADE_STATUS: "交易状态",
        WechatPayBillIndex.BANK_TYPE: "付款银行",
        WechatPayBillIndex.FEE_TYPE: "货币种类",
        WechatPayBillIndex.SETTLEMENT_TOTAL_FEE: "应结订单金额",
        WechatPayBillIndex.COUPON_FEE: "代金券金额",
        WechatPayBillIndex.REFUND_ID: "微信退款单号",
        WechatPayBillIndex.OUT_REFUND_NO: "商户退款单号",
        WechatPayBillIndex.REFUND_FEE: "退款金额",
        WechatPayBillIndex.COUPON_REFUND_FEE: "充值券退款金额",
        WechatPayBillIndex.REFUND_TYPE: "退款类型",
        WechatPayBillIndex.REFUND_STATUS: "退款状态",
        WechatPayBillIndex.PRODUCT_NAME: "商品名称",
        WechatPayBillIndex.ATTACH: "商户数据包",
        WechatPayBillIndex.SERVICE_FEE: "手续费",
        WechatPayBillIndex.RATE: "费率",
        WechatPayBillIndex.TOTAL_FEE: "订单金额",
        WechatPayBillIndex.APPLY_REFUND_FEE: "申请退款金额",
        WechatPayBillIndex.RATE_REMARK: "费率备注",
    }

    @classmethod
    def get_label(cls, index):
        """获取字段标签"""
        return cls.LABELS.get(index, "未知字段")


def prase_trx_with_balance(balance: Decimal, trx, verbose=False, idx=0):
    """解析交易记录，返回交易记录和余额"""
    order_id = trx[WechatPayBillIndex.OUT_TRADE_NO]
    # 交易类型
    trade_status = trx[WechatPayBillIndex.TRADE_STATUS]
    order = Order.query_by_trade_no(session, order_id)
    if not order:
        print("订单不存在: ", order_id)
        raise Exception("订单不存在")

    cart_ids = order.cart_id
    order_cart_items = Cart.query(session, cart_ids, order_id)

    trade_time = trx[0]
    # 转为 datetime
    trade_time = datetime.datetime.strptime(trade_time, "%Y-%m-%d %H:%M:%S")

    if verbose:
        print(str(idx) * 80)
        print('-' * 80)

    rows = _prase_trx_with_balance(
        balance, trade_time, trade_status, order, order_cart_items, trx, verbose
    )

    if not rows:
        return [], balance

    # 期末余额
    last_row = rows[-1]
    end_balance = last_row[4]

    if verbose:
        # 计算净收支
        b = Decimal(0)
        for idx, r in enumerate(rows):
            print(f"trx {idx} end balance {r[4]} {r}")
            # 收入为正，支出为负
            b += r[2]

        print('\n')

        desc = "✅" if b >=0 else "❌"
        print(f"{order_id} {desc}净收支:", b) 

        print('\n')
        print(str(idx) * 80)
        print('-' * 80)
        print('\n\n')

    return rows, end_balance


def distribute_fee(
    total_fee, cart_details: List
):
    """分摊规则: 邮费、手续费等按照商品金额比例分配

    Args:
        total_fee: 需要分摊的总费用
        items: 购物车项目列表
        cart_details: 购物车详情列表，包含价格信息。如果为None则按数量平均分配

    Returns:
        list: 分配到每个商品的费用列表
    """
    # 如果只有一个商品，直接返回总费用
    if len(cart_details) == 1:
        return [round(total_fee, 2)]

    # 按照商品金额比例分配
    total_amount = Decimal("0")
    item_amounts = []

    # 计算订单总金额和每个商品金额
    for detail in cart_details:
        num = detail["num"]
        amount = num * detail["price"]
        total_amount += amount
        item_amounts.append(amount)

    # 处理全部金额为0的情况(比如非退款商品)
    if total_amount == 0:
        return [Decimal("0")] * len(cart_details)

    # 按比例分配费用
    distributed_fees = []
    for amount in item_amounts:
        ratio = amount / total_amount
        distributed_fee = round(total_fee * ratio, 2)
        distributed_fees.append(distributed_fee)

    # 处理分配后的小数点误差
    total_distributed = sum(distributed_fees)
    remainder = round(total_fee - total_distributed, 2)

    if remainder != 0:
        # 将剩余金额加到金额最大的商品上
        max_amount_index = item_amounts.index(max(item_amounts))
        distributed_fees[max_amount_index] += remainder

    return distributed_fees

  

def distribute_refund_fee(
    total_refund_fee, cart_details: List
):
    """
    # 退款存在部分退款，所以说手续费本身就是针对部分商品的
        分摊规则: 邮费、手续费等按照商品金额比例分配
        这里按照sku分摊计算，使用时候需要根据 refund num 再计算

    Args:
        total_fee: 需要分摊的总费用
        items: 购物车项目列表
        cart_details: 购物车详情列表，包含价格信息。如果为None则按数量平均分配

    Returns:
        list: 分配到每个商品的费用列表
    """
    # 如果只有一个商品，直接返回总费用
    if len(cart_details) == 1:
        return [round(total_refund_fee, 2)]

    # 按照商品金额比例分配
    item_refund_amounts = []
    total_refund_amount = Decimal("0")

    # 计算订单总金额和每个商品金额
    for detail in cart_details:
        refund_num = detail["refund_num"]
        refund_amount = refund_num * detail["price"]
        total_refund_amount += refund_amount
        item_refund_amounts.append(refund_amount)

    # 处理全部金额为0的情况(比如非退款商品)
    if total_refund_amount == 0:
        return [Decimal("0")] * len(cart_details)

    # 按比例分配费用
    distributed_fees = []
    for amount in item_refund_amounts:
        ratio = amount / total_refund_amount
        distributed_fee = round(total_refund_fee * ratio, 2)
        distributed_fees.append(distributed_fee)

    # # 处理分配后的小数点误差
    total_distributed = sum(distributed_fees)
    remainder = round(total_refund_fee - total_distributed, 2)

    if remainder != 0:
        # 将剩余金额加到金额最大的商品上
        max_amount_index = item_refund_amounts.index(max(item_refund_amounts))
        distributed_fees[max_amount_index] += remainder

    return distributed_fees
   

def _parse_cart_items(order_cart_items: List[Cart], is_refund=False):
    '''便于后续调用，不要过于复杂的数据结构'''
    cart_item_details = []
    for order_cart_item in order_cart_items:
        cart_info = json.loads(order_cart_item.cart_info)
        product_info = cart_info.get("productInfo")
        # print("product_info", product_info.get('id'))

        # 修正refund 值
        refund_num = Decimal(int(cart_info.get("refundNum", 0)))
        if refund_num == 0 and is_refund:
            # 二次检查
            after_sales = AfterSales.get_success_refund_after_sales(session, order_cart_item.order_id)
            if not after_sales:
                # 从微信支付进行的refund，导致没有after sales记录
                refund_num = Decimal(int(cart_info.get("cartNum")))
            else:
                after_sales_items = AfterSalesItem.query(session, after_sales.id)
                # 找是否存在该sku的售后
                refund_item_info = [after_sales_item for after_sales_item in after_sales_items if after_sales_item.product_id == product_info.get("id") and after_sales_item.cart_info_dict().get("productInfo", {}).get("attrInfo", {}).get("sku") == product_info.get("attrInfo", {}).get("sku")]
                if refund_item_info:
                    refund_item_info = refund_item_info[0]
                    refund_num = refund_item_info.num
                    # print("修正refund_num", refund_num)

        cart_item_details.append(
            {
                "num": Decimal(int(cart_info.get("cartNum"))),
                "refund_num": refund_num,
                "name": product_info.get("storeName"),
                "price": round(
                    Decimal(product_info.get("attrInfo", {}).get("price")), 2
                ),
                "sku": product_info.get("attrInfo", {}).get("sku"),
                "is_after_sales": refund_num > 0,
                "product_id": product_info.get("id"),
            }
        )
    
    # 处理全部退款的未记录的情况
    if is_refund:
        refund_num = sum([detail["refund_num"] for detail in cart_item_details])
        if refund_num == 0:
            # 全退
            for detail in cart_item_details:
                detail["refund_num"] = detail["num"]
                detail["is_after_sales"] = True

                print("修正全退refund_num", detail["refund_num"])
    
    return cart_item_details


def _prase_trx_with_balance(
    balance: Decimal, trade_time, trade_status, order: Order, order_cart_items:List[Cart], trx, verbose=False
):
    rows = []
    if trade_status not in ("SUCCESS", "REFUND"):
        print("未知交易状态", trade_status)
        raise Exception("未知交易状态")

    # 1. 预处理 购物车商品信息
    is_refund = trade_status == "REFUND"
    cart_item_details = _parse_cart_items(order_cart_items, is_refund)

    # 实付
    total_pay = Decimal(order.pay_price)

    # 时间	收支类型	收入(元）	账单类型	余额	商品信息	SKU	数量	订单号
    trade_status_cn = trade_status == "SUCCESS" and "收入" or "支出"
    bill_type_cn = "未知"

    # 退款金额
    refund_price = Decimal(trx[WechatPayBillIndex.REFUND_FEE])

    # 数量
    total_num = Decimal(str(order.total_num))

    # 邮费
    total_postage = order.total_postage
    if is_refund:
        postage_fees = distribute_refund_fee(
            total_postage,
            cart_item_details,
        )
    else:
        postage_fees = distribute_fee(
            total_postage,
            cart_item_details,
        )
    # if verbose:
    #     for i, fee in enumerate(postage_fees):
    #         print("邮费分摊", fee, cart_item_details[i].get('name'))

    # 微信手续费已经有正负号
    wechat_fee = Decimal(trx[WechatPayBillIndex.SERVICE_FEE])
    if is_refund:
        # 退款存在部分退款，所以说手续费本身就是针对部分商品的
        wechat_fees = distribute_refund_fee(
            wechat_fee,
            cart_item_details,
        )
    else:
        wechat_fees = distribute_fee(
            wechat_fee,
            cart_item_details,
        )
    if verbose:
        for i, fee in enumerate(wechat_fees):
            print("手续费分摊", fee, cart_item_details[i].get('name'))

    # 1. 处理单独的快递费退款
    # 如果是退款，且退款金额正好是10元，且没有商品退款，则认为是快递费退款
    # 判断是否为快递费退款 10元
    names = []
    for cart_item_detail in cart_item_details:
        if is_refund:
            if cart_item_detail["refund_num"] > 0:
                names.append(cart_item_detail["name"])
        else:
            names.append(cart_item_detail["name"])
    name = "、".join(names)

    if is_refund and refund_price.compare(Decimal("10.00")) == 0:
        balance = balance + wechat_fee.copy_abs()
        row = [
            trade_time,
            "收入",
            wechat_fee.copy_abs(),  # 使用Decimal的copy_abs()方法获取绝对值
            "手续费退回",
            balance,
            name,
            "-",  # SKU
            1,  # 数量
            "`" + order.order_id,
        ]
        rows.append(row)

        # 同时需要
        # 快递拦截收入 10块 退回
        income = Decimal("-10")
        # 按照商品数量和价格比例分配收入
        total_amount = Decimal("0")
        item_amounts = []

        # 计算订单总金额和每个商品金额
        for cart_item_detail in cart_item_details:
            amount = cart_item_detail["num"] * cart_item_detail["price"]
            total_amount += amount
            item_amounts.append(amount)

        # 按比例分配快递拦截收入
        distributed_refunds = []
        for amount in item_amounts:
            ratio = amount / total_amount
            distributed_refund = round(income * ratio, 2)
            distributed_refunds.append(distributed_refund)

        # 处理分配后的小数点误差
        total_distributed = sum(distributed_refunds)
        remainder = round(income - total_distributed, 2)
        if remainder != 0:
            # 将剩余金额加到金额最大的商品上
            max_amount_index = item_amounts.index(max(item_amounts))
            distributed_refunds[max_amount_index] += remainder

        # 为每个商品生成对应的流水记录
        for i, cart_item_detail in enumerate(cart_item_details):
            balance = balance + distributed_refunds[i]
            bill_type_cn = "快递拦截收入退回"
            row = [
                trade_time,
                "退回",
                distributed_refunds[i],  # 分配后的收入金额
                bill_type_cn,
                balance,
                cart_item_detail["name"],
                cart_item_detail["sku"],
                cart_item_detail["num"],
                "`" + order.order_id,
            ]
            rows.append(row)
            if verbose:
                print("快递拦截收入分配", row)

        if verbose:
            print("单独快递费退款", row)
        return rows

    # 其他
    # 2. 邮费扣除，拦截
    if (
        is_refund
        and refund_price.compare(total_pay - Decimal("10.00")) == 0
    ):
        # 快递拦截收入 10块
        income = Decimal("10")
        # 按照商品数量和价格比例分配收入
        total_amount = Decimal("0")
        item_amounts = []

        # 计算订单总金额和每个商品金额
        for cart_item_detail in cart_item_details:
            amount = cart_item_detail["num"] * cart_item_detail["price"]
            total_amount += amount
            item_amounts.append(amount)

        # 按比例分配快递拦截收入
        distributed_refunds = []
        for amount in item_amounts:
            ratio = amount / total_amount
            distributed_refund = round(income * ratio, 2)
            distributed_refunds.append(distributed_refund)

        # 处理分配后的小数点误差
        total_distributed = sum(distributed_refunds)
        remainder = round(income - total_distributed, 2)
        if remainder != 0:
            # 将剩余金额加到金额最大的商品上
            max_amount_index = item_amounts.index(max(item_amounts))
            distributed_refunds[max_amount_index] += remainder

        # 为每个商品生成对应的流水记录
        for i, cart_item_detail in enumerate(cart_item_details):
            balance = balance + distributed_refunds[i]
            bill_type_cn = "快递拦截收入"
            row = [
                trade_time,
                "收入",
                distributed_refunds[i],  # 分配后的收入金额
                bill_type_cn,
                balance,
                cart_item_detail["name"],
                cart_item_detail["sku"],
                cart_item_detail["num"],
                "`" + order.order_id,
            ]
            rows.append(row)
            if verbose:
                print("快递拦截收入分配", row)

    # 2. 原有的处理逻辑
    # 其他退款、其他支付
    for i, cart_item_detail in enumerate(cart_item_details):
        # 货品
        balance_before = balance
        amount = cart_item_detail["num"] * cart_item_detail["price"]
        if is_refund:
            refund_num = cart_item_detail["refund_num"]

            if refund_num > 0:
                refund_amount = refund_num * cart_item_detail["price"] * Decimal("-1")
                balance = balance + refund_amount
                bill_type_cn = "退款支出"
                row = [
                    trade_time,
                    trade_status_cn,
                    refund_amount,
                    bill_type_cn,
                    balance,
                    cart_item_detail["name"],
                    cart_item_detail["sku"],
                    refund_num,
                    "`" + order.order_id,
                ]
                rows.append(row)

        elif trade_status == "SUCCESS":
            balance = balance + amount
            bill_type_cn = "货款收入"
            row = [
                trade_time,
                trade_status_cn,
                amount,
                bill_type_cn,
                balance,
                cart_item_detail["name"],
                cart_item_detail["sku"],
                cart_item_detail["num"],
                "`" + order.order_id,
            ]
            rows.append(row)
        else:
            msg = f"未知的交易状态: {trade_status}"
            raise Exception(msg)
        if verbose:
            print(
                bill_type_cn,
                amount,
                "balance_before:",
                balance_before,
                "balance:",
                balance,
            )

        # 邮费
        postage = postage_fees[i]
        # 记录邮费收入、支出
        if postage > 0:
            if trade_status == "REFUND":
                bill_type_cn = "邮费退回"
                amount = postage * Decimal(-1)
                balance = balance + amount
                row = [
                    trade_time,
                    "支出",
                    amount,
                    bill_type_cn,
                    balance,
                    cart_item_detail["name"],
                    cart_item_detail["sku"],
                    cart_item_detail["num"],
                    "`" + order.order_id,
                ]
                rows.append(row)
                if verbose:
                    print("邮费退回", row)
            elif trade_status == "SUCCESS":
                balance = balance + postage
                bill_type_cn = "邮费收入"
                row = [
                    trade_time,
                    "收入",
                    postage,
                    bill_type_cn,
                    balance,
                    cart_item_detail["name"],
                    cart_item_detail["sku"],
                    cart_item_detail["num"],
                    "`" + order.order_id,
                ]
                rows.append(row)
                if verbose:
                    print("邮费收入", row)

        # 手续费，收款为正值，退款为负值
        w_fee = wechat_fees[i]
        if trade_status == "REFUND":
            if cart_item_detail["is_after_sales"]:
                # 分摊计算过了
                income_w_fee = w_fee * Decimal("-1")
                balance = balance + income_w_fee
                bill_type_cn = "手续费收入"
                row = [
                    trade_time,
                    "收入",
                    income_w_fee,
                    bill_type_cn,
                    balance,
                    cart_item_detail["name"],
                    cart_item_detail["sku"],
                    cart_item_detail["num"],
                    "`" + order.order_id,
                ]
                rows.append(row)
                if verbose:
                    print("手续费收入", row)
            else:
                print(f"order: {order.order_id} {cart_item_detail['name']} 没有退款，没有手续费变化")
                # # refundNum 是不是等于 cartNum
                # refund_num = cart_item_detail["refund_num"]

                # income_w_fee = w_fee * Decimal("-1")
                # if refund_num != cart_item_detail["num"]:
                #     income_w_fee = w_fee * refund_num / cart_item_detail["num"] * Decimal("-1")
                # balance = balance - income_w_fee
                # bill_type_cn = "手续费收入"
                # row = [
                #     trade_time,
                #     "收入",
                #     income_w_fee,
                #     bill_type_cn,
                #     balance,
                #     cart_item_detail["name"],
                #     cart_item_detail["sku"],
                #     cart_item_detail["num"],
                #     "`" + order.order_id,
                # ]
                # rows.append(row)
                # if verbose:
                #     print("手续费收入", row)
        elif trade_status == "SUCCESS":
            balance = balance - w_fee
            bill_type_cn = "手续费支出"
            if order.order_id == "1791691442545008640":
                print("w_fee", w_fee)
            row = [
                trade_time,
                "支出",
                w_fee * Decimal("-1"),
                bill_type_cn,
                balance,
                cart_item_detail["name"],
                cart_item_detail["sku"],
                cart_item_detail["num"],
                "`" + order.order_id,
            ]
            rows.append(row)
            if verbose:
                print("手续费支出", row)

    return rows


def check_balance(start, end, verbose=False):
    # 每日10点更新前一天，11日9:00 获取9日，11日10:00 获取10日
    # 检查start end是否合法
    if not start or not end:
        print("check_balance 请输入开始结束时间")
        return False

    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    # 前两天
    cur_valid = now - datetime.timedelta(days=2)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=1)

    if start <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("check_balance start 非法")
        return False
    if end <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("check_balance end 非法:", end, "valid:", cur_valid.strftime("%Y-%m-%d"))
        return False

    start_balance = Decimal("0")
    if start == "2024-04-01":
        # 小敏对账日的余额
        start_balance = Decimal("1327.56")
        print("2024-04-01 日终余额", start_balance)
    else:
        # 递归获取前一天的余额，最多迭代到2024-04-01
        pre_day = datetime.datetime.strptime(start, "%Y-%m-%d") - datetime.timedelta(
            days=1
        )
        rows = read_csv(
            REPORT_ROOT + f"/流水数据{pre_day.strftime('%Y-%m-%d')}.csv", True
        )
        while not rows:
            pre_day = pre_day - datetime.timedelta(days=1)
            rows = read_csv(
                REPORT_ROOT + f"/流水数据{pre_day.strftime('%Y-%m-%d')}.csv", True
            )
        row = rows[-1]
        start_balance = Decimal(filter_special(row[4]))
        print(f"{pre_day} 期初余额", start_balance)

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date
    while cur_date <= end_date:
        rows = read_csv(
            REPORT_ROOT + f"/流水数据{cur_date.strftime('%Y-%m-%d')}.csv", True
        )
        if rows:
            row = rows[-1]
            end_balance = Decimal(filter_special(row[4]))
            print(f"{cur_date.strftime('%Y-%m-%d')} 日终余额", end_balance)
        else:
            print(f"{cur_date.strftime('%Y-%m-%d')} 余额无变动")
        cur_date = cur_date + datetime.timedelta(days=1)


def gen_report(start, end, accuracy_start_balance=None, verbose=False):
    """生成流水报表
    start: 开始时间
    end: 结束时间
    示例： download_bill('2021-05-01', '2021-05-31')
    """
    # 每日10点更新前一天，11日9:00 获取9日，11日10:00 获取10日
    # 检查start end是否合法
    if not start or not end:
        print("gen_report 请输入开始结束时间")
        return False

    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    # 前两天
    cur_valid = now - datetime.timedelta(days=2)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=1)

    if start <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("gen_report start 非法")
        return False
    if end <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print(
            "gen_report end 非法:", end, "修正为valid:", cur_valid.strftime("%Y-%m-%d")
        )
        end = cur_valid.strftime("%Y-%m-%d")

    start_balance = Decimal("0")
    if start == "2024-04-01" or accuracy_start_balance:
        # 小敏对账日的余额
        start_balance = Decimal("1327.56")
        if accuracy_start_balance:
            start_balance = Decimal(accuracy_start_balance)
    else:
        # 递归获取前一天的余额，最多迭代到2024-04-01
        pre_day = datetime.datetime.strptime(start, "%Y-%m-%d") - datetime.timedelta(
            days=1
        )
        rows = read_csv(REPORT_ROOT + f"/流水数据{pre_day.strftime('%Y-%m-%d')}.csv")
        while not rows:
            pre_day = pre_day - datetime.timedelta(days=1)
            rows = read_csv(
                REPORT_ROOT + f"/流水数据{pre_day.strftime('%Y-%m-%d')}.csv"
            )
            print("获取前一天余额", pre_day.strftime("%Y-%m-%d"))
        row = rows[-1]
        start_balance = Decimal(filter_special(row[4]))

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date
    while cur_date <= end_date:
        # 1. 读取微信原始数据
        # 微信支付账单2024-05-06.csv
        s = cur_date.strftime("%Y-%m-%d")
        e = (cur_date + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        cur_date_str = cur_date.strftime("%Y-%m-%d")
        bill_csv_path = WECHAT_RAW_DATA_ROOT + f"/微信支付账单{cur_date_str}.csv"

        print(f"{s} 期初余额: {start_balance}")
        end_balance = start_balance
        if not os.path.exists(bill_csv_path):
            print("账单数据不存在", bill_csv_path)
            raise Exception("账单数据不存在")

        rows = read_wechat_pay_log(bill_csv_path)
        # 按照第一列排序
        rows = sorted(rows, key=lambda x: x[0])

        # 2. 解析微信支付账单
        flow_rows = []
        for idx, row in enumerate(rows):
            # if idx != (len(rows) - 1) and idx != (len(rows) - 2):
            #     # 跳过最后的合计的两行
            #     row_out_trade_no = row[WechatPayBillIndex.OUT_TRADE_NO]
            #     if out_trade_no and row_out_trade_no != out_trade_no:
            #         continue
            tmp_rows, end_balance = prase_trx_with_balance(
                end_balance, row, verbose, idx
            )
            flow_rows.extend(tmp_rows)

        if verbose:
            print(f"{s} 未计算资金流水(外部提现、外部交易手续费)之前的日终余额: {end_balance}")

        # 资金账单中的提现操作进行处理
        fund_json_path = WECHAT_RAW_DATA_ROOT + f"/资金账单{cur_date_str}.json"
        fund_data = read_json(fund_json_path)
        wx_pay_fund_flow_base_result_list = fund_data.get(
            "wxPayFundFlowBaseResultList", []
        )
        trx_fund_list = [
            f
            for f in wx_pay_fund_flow_base_result_list
            if f["bizName"] in ("交易", "退款")
        ]

        debug_date_str = "2024-04-16"
        # 若业务凭证号找不到对应的order，说明为小程序外部交易
        for f in trx_fund_list:
            bizName = f["bizName"]
            bizType = f["bizType"]
            bizVoucherId = f["bizVoucherId"]
            bizTransactionId = f["bizTransactionId"]
            fundFlowId = f["fundFlowId"]
            fundApplicant = f["fundApplicant"]

            memo = f["memo"]
            # 业务凭证号
            # 支付为订单号或扩展订单号
            # 退款为退款单号（随机生成）
            trade_no = bizVoucherId
            order = Order.query_by_trade_no(session, trade_no)
            if order:
                if trade_no in MAYBE_DELETED_ORDER_IDS:
                    if (
                        cur_date_str == debug_date_str
                        and trade_no in MAYBE_DELETED_ORDER_IDS
                    ):
                        print(
                            "跳过了已删除订单",
                            trade_no,
                            bizTransactionId,
                            fundFlowId,
                            memo,
                        )
                continue
            else:
                if (
                    bizName == "退款"
                    and bizType == "退款"
                    and (CABLE_APP_APPLICANT == fundApplicant or "退款总金额" in memo)
                ):
                    # 退款单号没存，但退款在支付账单中已计算，跳过
                    continue
                if cur_date_str == debug_date_str:
                    print(
                        "订单未找到，疑似外部交易",
                        trade_no,
                        bizTransactionId,
                        fundFlowId,
                        memo,
                    )
            if bizName == "交易":
                if bizType == "交易":
                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    end_balance += Decimal(financialFee)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        end_balance,
                        "外部收款",
                        "外部收款",
                        Decimal("1"),
                        "-",
                    ]
                    if cur_date_str == debug_date_str:
                        print("外部收款", row)
                    if verbose:
                        print("资金账单交易", row)
                    flow_rows.append(row)
                elif bizType == "扣除交易手续费":
                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    end_balance -= Decimal(financialFee)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        end_balance,
                        "外部交易手续费",
                        "外部交易手续费",
                        Decimal("1"),
                        "-",
                    ]
                    if cur_date_str == debug_date_str:
                        print("外部收款扣除交易手续费", row)
                    if verbose:
                        print("资金账单交易", row)
                    flow_rows.append(row)
                else:
                    msg = f"未知的资金账单类型: {bizName} {bizType}"
                    raise Exception(msg)
            elif bizName == "退款":
                if bizType == "退款":
                    # 先查询该批次的关联微信支付订单 4200002553202501103709602511
                    fundFlowId = f["fundFlowId"]
                    # 查询该批次下对应的支付订单
                    # flow_record_list = [f for f in trx_fund_list if f["bizName"] == "交易" and f["bizTransactionId"] == fundFlowId]

                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    end_balance -= Decimal(financialFee)

                    print("调试外部付款原始数据", f)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        end_balance,
                        "外部退款",
                        "外部退款",
                        Decimal("1"),
                        "-",
                    ]
                    if cur_date_str == debug_date_str:
                        print("外部退款", row)
                    if verbose:
                        print("资金账单交易", row)
                    flow_rows.append(row)
                else:
                    msg = f"未知的资金账单类型: {bizName} {bizType}"
                    raise Exception(msg)
            else:
                msg = f"未知的资金账单类型: {bizName} {bizType}"
                raise Exception(msg)

        other_fund_list = [
            f
            for f in wx_pay_fund_flow_base_result_list
            if f["bizName"] not in ("交易", "退款")
        ]
        for f in other_fund_list:
            bizName = f["bizName"]
            bizType = f["bizType"]
            if bizName == "充值/提现":
                if bizType == "提现":
                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    end_balance -= Decimal(financialFee)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        end_balance,
                        "系统",
                        "系统",
                        Decimal("1"),
                        "-",
                    ]
                    flow_rows.append(row)
                    if verbose:
                        print("资金账单交易", row)
                elif bizType == "充值":
                    financialFee = f["financialFee"]
                    billingTime = f["billingTime"]
                    # 收入、支出
                    financialType = f["financialType"]
                    end_balance += Decimal(financialFee)

                    row = [
                        billingTime,
                        financialType,
                        Decimal(financialFee),
                        bizType,
                        end_balance,
                        "系统",
                        "系统",
                        Decimal("1"),
                        "-",
                    ]
                    if verbose:
                        print("资金账单交易", row)
                    flow_rows.append(row)
                else:
                    msg = f"未知的资金账单类型: {bizName} {bizType}"
                    raise Exception(msg)
            else:
                msg = f"未知的资金账单类型: {bizName} {bizType}"
                raise Exception(msg)

        # 提现后，可用余额减少

        if not flow_rows:
            # 余额不变化
            print(f"{s} 日终余额(无变化): {end_balance}")
            # 无变化时不出流水单
        else:
            print(f"{s} 日终余额: {end_balance}, 净收支: {end_balance - start_balance}, 期初余额: {start_balance}")
            report_csv_path = REPORT_ROOT + f"/流水数据{cur_date_str}.csv"
            if os.path.exists(report_csv_path):
                os.remove(report_csv_path)  # 删除旧文件
            write_csv(report_csv_path, flow_rows)

        print("=" * 80)
        cur_date = cur_date + datetime.timedelta(days=1)
        start_balance = end_balance

    print("生成结束")
    return True



def query_tmp_stock(
    day="2024-05-06", pid=CD2_PID, sku_unique=CD2_SKU_UNIQUE, verbose=False
):
    q = day.replace("-", "")
    summary = TmpStockSummary.query(session, q, pid, sku_unique)
    if not summary:
        # print(f"{day} 库存无变化")
        # return []
        pass

    records = TmpStock.queryAll(session, q, pid, sku_unique)
    record_in = sum([r.total_stock_in for r in records])
    record_out = sum([r.total_stock_out for r in records])
    if verbose:
        if summary:
            print(
                f"{day}, 出库：{summary.total_stock_out}, 入库：{summary.total_stock_in}，库存调整：{summary.inventory_adjustments}, 同步: {summary.synced}"
            )
        print(f"{day} record_in: {record_in}, record_out: {record_out}")
    return records


def check_bill(start, end, verbose=False):
    """检查账单信息
    Args:
        start: 开始日期，格式：YYYY-MM-DD
        end: 结束日期，格式：YYYY-MM-DD
        verbose: 是否显示详细信息
    """

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date

    if verbose:
        print(f"开始日期: {start_date}, 结束日期: {end_date}")

    while cur_date <= end_date:
        cur_date_str = cur_date.strftime("%Y-%m-%d")
        bill_csv_path = WECHAT_RAW_DATA_ROOT + f"/微信支付账单{cur_date_str}.csv"

        if not os.path.exists(bill_csv_path):
            print(f"账单文件不存在: {bill_csv_path}")
            cur_date += datetime.timedelta(days=1)
            continue

        print(f"\n{'='*20} {cur_date_str} {'='*20}")
        rows = read_wechat_pay_log(bill_csv_path)

        # 按交易时间排序
        rows.sort(key=lambda x: x[WechatPayBillIndex.TRADE_TIME])

        for row in rows:
            # 提取关键字段
            trade_time = row[WechatPayBillIndex.TRADE_TIME]
            transaction_id = row[WechatPayBillIndex.TRANSACTION_ID]
            out_trade_no = row[WechatPayBillIndex.OUT_TRADE_NO]
            trade_type = row[WechatPayBillIndex.TRADE_TYPE]
            trade_status = row[WechatPayBillIndex.TRADE_STATUS]
            refund_id = row[WechatPayBillIndex.REFUND_ID]
            refund_fee = row[WechatPayBillIndex.REFUND_FEE]
            refund_type = row[WechatPayBillIndex.REFUND_TYPE]
            refund_status = row[WechatPayBillIndex.REFUND_STATUS]
            product_name = row[WechatPayBillIndex.PRODUCT_NAME]

            # 金额相关字段
            settlement_total_fee = Decimal(row[WechatPayBillIndex.SETTLEMENT_TOTAL_FEE])
            coupon_fee = Decimal(row[WechatPayBillIndex.COUPON_FEE])
            service_fee = Decimal(row[WechatPayBillIndex.SERVICE_FEE])
            total_fee = Decimal(row[WechatPayBillIndex.TOTAL_FEE])
            apply_refund_fee = Decimal(row[WechatPayBillIndex.APPLY_REFUND_FEE])

            # 打印交易信息
            print(f"\n交易时间: {trade_time}")
            print(f"微信订单号: {transaction_id}")
            print(f"商户订单号: {out_trade_no}")
            # print(f"交易类型: {trade_type}")
            print(f"交易状态: {trade_status}")
            print(f"商品名称: {product_name}")

            # 如果是退款交易，打印退款信息
            if trade_status == "REFUND":
                print(f"微信退款单号: {refund_id}")
                print(f"退款金额: {refund_fee}")
                # print(f"退款类型: {refund_type}")
                print(f"退款状态: {refund_status}")
                # print(f"申请退款金额: {apply_refund_fee}")
            elif trade_status == "SUCCESS":
                # print(f"应结订单金额: {settlement_total_fee}")
                print(f"订单金额: {total_fee}")

            # 手续费: 收款时候扣除微信支付手续费，退款时候退回
            print(f"手续费: {service_fee}")

            # 打印金额信息
            # if verbose:
            #     print(f"代金券金额: {coupon_fee}")
            #     print(f"费率: {row[WechatPayBillIndex.RATE]}")
            #     print(f"费率备注: {row[WechatPayBillIndex.RATE_REMARK]}")

            print("-" * 50)

        cur_date += datetime.timedelta(days=1)


def stat_sku(start, end, pid, sku, verbose=False):
    """统计指定SKU的销售情况

    Args:
        start: 开始时间 YYYY-MM-DD
        end: 结束时间 YYYY-MM-DD
        pid: 商品ID
        sku: SKU编码，如果为None则统计该商品所有SKU
        verbose: 是否显示详细信息
    """
    if not start or not end:
        print("请输入开始和结束时间")
        return False

    # 验证日期合法性
    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    cur_valid = now - datetime.timedelta(days=1)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=0)

    if start > cur_valid.strftime("%Y-%m-%d") or end > cur_valid.strftime("%Y-%m-%d"):
        print("日期范围非法，结束日期不能晚于", cur_valid.strftime("%Y-%m-%d"))
        return False

    # 初始化统计数据
    stats = {
        "total_sales": 0,  # 总销售数量
        "total_refunds": 0,  # 总退款数量
        "net_sales": 0,  # 净销售数量
        "total_amount": 0,  # 总销售金额
        "total_refund_amount": 0,  # 总退款金额
        "net_amount": 0,  # 净销售金额
        "daily_stats": {},  # 每日统计数据
        "orders": {"success": [], "refund": []},  # 订单详情  # 成功订单  # 退款订单
    }

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date

    # 获取商品信息
    product = Product.get(session, pid)
    if not product:
        print(f"商品不存在: {pid}")
        return stats

    print(f"统计商品: {product.name}")
    if sku:
        print(f"SKU: {sku}")

    # 遍历日期范围
    while cur_date <= end_date:
        cur_date_str = cur_date.strftime("%Y-%m-%d")
        bill_csv_path = WECHAT_RAW_DATA_ROOT + f"/微信支付账单{cur_date_str}.csv"

        if not os.path.exists(bill_csv_path):
            cur_date += datetime.timedelta(days=1)
            continue

        # 读取账单数据
        rows = read_wechat_pay_log(bill_csv_path)
        daily_stats = {"sales": 0, "refunds": 0, "amount": 0, "refund_amount": 0}

        for row in rows:
            order_id = row[WechatPayBillIndex.OUT_TRADE_NO]
            trade_status = row[WechatPayBillIndex.TRADE_STATUS]

            # 查询订单信息
            order = Order.query_by_trade_no(session, order_id)
            if not order:
                continue

            # 获取购物车信息
            cart_ids = order.cart_id
            items = Cart.query(session, cart_ids, order_id)
            refund_items = []
            if trade_status == "REFUND":
                after_sales = AfterSales.get_refunded(session, order_id)
                refund_items = AfterSalesItem.query(session, after_sales.id)

            # 处理每个购物车项
            for cart in items:
                cart_info = json.loads(cart.cart_info)
                product_info = cart_info.get("productInfo", {})
                cart_pid = str(product_info.get("id"))
                cart_sku = product_info.get("attrInfo", {}).get("sku")

                # 检查是否匹配目标商品/SKU
                if cart_pid != str(pid):
                    continue
                if sku and cart_sku != sku:
                    continue

                # 获取数量和金额
                num = int(cart_info.get("cartNum", 0))
                price = Decimal(product_info.get("attrInfo", {}).get("price", 0))
                amount = price * num

                if trade_status == "SUCCESS":
                    daily_stats["sales"] += num
                    daily_stats["amount"] += amount
                    stats["total_sales"] += num
                    stats["total_amount"] += amount
                    if verbose:
                        stats["orders"]["success"].append(
                            {
                                "date": cur_date_str,
                                "order_id": order_id,
                                "num": num,
                                "amount": amount,
                            }
                        )
                elif trade_status == "REFUND":
                    # 跳过10元的退款
                    if row[WechatPayBillIndex.REFUND_FEE] == 10:
                        continue
                    # 从退款项中找到对应的购物车项
                    refund_cart = None
                    for item in refund_items:
                        cart_info = json.loads(item.cart_info)
                        if cart_info.get("id") == cart.id:
                            refund_cart = item
                            break
                    refund_num = int(refund_cart["refundNum"]) if refund_cart else num
                    if refund_num == num:
                        if verbose:
                            print(
                                f"全部退款 - 订单号: {order_id}, 退款数量: {refund_num}"
                            )
                    else:
                        if verbose:
                            print(
                                f"部分退款 - 订单号: {order_id}, 退款数量: {refund_num}"
                            )

                    refund_amount = price * refund_num
                    daily_stats["refunds"] += refund_num
                    daily_stats["refund_amount"] += refund_amount
                    stats["total_refunds"] += refund_num
                    stats["total_refund_amount"] += refund_amount
                    if verbose:
                        print(
                            f"退款处理 - 订单号: {order_id}, 累计退款数量: {stats['total_refunds']}"
                        )

        # 保存每日统计
        if daily_stats["sales"] > 0 or daily_stats["refunds"] > 0:
            stats["daily_stats"][cur_date_str] = daily_stats

        cur_date += datetime.timedelta(days=1)

    # 计算净销售
    stats["net_sales"] = stats["total_sales"] - stats["total_refunds"]
    stats["net_amount"] = stats["total_amount"] - stats["total_refund_amount"]

    # 打印统计结果
    print("\n" + "=" * 50)
    print(f"商品统计 ({start} 至 {end})")
    print("=" * 50)

    # 获取商品信息
    product = Product.get(session, pid)
    if not product:
        print(f"商品不存在: {pid}")
        return stats

    print(f"商品名称: {product.name}")
    if sku:
        print(f"SKU: {sku}")
    print("-" * 50)

    print("\n销售统计:")
    print(f"总销售数量: {stats['total_sales']:>8} 件")
    print(f"退货数量  : {stats['total_refunds']:>8} 件")
    print(f"净销售数量: {stats['net_sales']:>8} 件")
    print("-" * 50)
    print(f"销售金额  : {stats['total_amount']:>8.2f} 元")
    print(f"退款金额  : {stats['total_refund_amount']:>8.2f} 元")
    print(f"净销售金额: {stats['net_amount']:>8.2f} 元")

    if verbose:
        print("\n每日明细:")
        print("-" * 50)
        print("日期         销售  退货  净销售  金额(元)")
        print("-" * 50)
        for date, daily in sorted(stats["daily_stats"].items()):
            net_sales = daily["sales"] - daily["refunds"]
            net_amount = daily["amount"] - daily["refund_amount"]
            print(
                f"{date}  {daily['sales']:>4}  {daily['refunds']:>4}  {net_sales:>6}  {net_amount:>8.2f}"
            )

    print("=" * 50)
    return stats


if __name__ == "__main__":
    # 命令行参数，操作类型download_bill,gen_report; 日期范围start,end;verbose
    from optparse import OptionParser

    parser = OptionParser(usage="""微信账单和流水生成""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    parser.add_option(
        "-s", "--start", dest="start", help="start date", default="2024-04-01"
    )
    # parser.add_option("-e", "--end", dest="end", help="end date", default="2024-05-01")
    parser.add_option("-e", "--end", dest="end", help="end date", default=None)
    parser.add_option("-o", "--operation", dest="operation", help="operation")
    parser.add_option("-t", "--trade_no", dest="trade_no", help="商户交易号")
    parser.add_option(
        "-a",
        "--accuracy_start_balance",
        dest="accuracy_start_balance",
        help="对账日期初余额",
    )
    parser.add_option("-p", "--pid", dest="pid", help="商品ID", default=CD2_PID)
    parser.add_option("-k", "--sku", dest="sku", help="SKU", default=SKU_DEFAULT)
    options, args = parser.parse_args()
    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    end = options.end
    if not end:
        end = options.start

    print(f"start: {options.start}, end: {end}")

    if options.operation == "check_bill":
        check_bill(options.start, end, options.verbose)
    elif options.operation == "stat_sku":
        """python report_export.py -s 2024-12-16 -e 2024-12-17 -o stat_sku -p 642 -k 默认"""
        stat_sku(options.start, end, options.pid, options.sku, options.verbose)
    elif options.operation == "download_bill":
        """python report_export.py -s 2024-12-16 -e 2024-12-17 -o download_bill"""
        download_bill(options.start, end, verbose=options.verbose)
    elif options.operation == "gen_report":
        gen_report(options.start, end, options.accuracy_start_balance, options.verbose)
    elif options.operation == "all_in_one":
        download_bill(options.start, end, options.verbose)
        gen_report(options.start, end, options.accuracy_start_balance, options.verbose)
        from check_inventory import gen_stock_report_by_date

        gen_stock_report_by_date(options.start, end, options.verbose)
    elif options.operation == "check_balance":
        check_balance(options.start, end, options.verbose)

    session.close()
