import csv
import datetime
from decimal import Decimal
import json
import os

import signal

import redis


# 线上
def get_redis_connection(port=6379, db=5):
    # 连接到Redis
    # 假设Redis运行在本地且端口为6666
    # decode_responses=True, 自动解码返回结果
    # redis_connection = redis.Redis(host='localhost', port=6666, db=0, decode_responses=True)
    redis_connection = redis.Redis(
        host="localhost", port=port, db=db, decode_responses=True
    )

    # # 设置键值对
    # redis_connection.set('test_key', 'Hello, Redis!')

    # # 获取并打印键值对
    # value = redis_connection.get('test_key')
    # print(value)  # 应输出: Hello, Redis!
    return redis_connection


import requests


# 使用命令行调用一个linux命令
def run_cmd(cmd):
    import subprocess

    p = subprocess.Popen(
        cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE
    )
    out, err = p.communicate()

    if err:
        print("Error: ", err)
        err = err.decode("utf-8")
    else:
        err = ""

    return out.decode("utf-8"), err


def download_bill(start, end, verbose=False):
    """下载账单
    start: 开始时间
    end: 结束时间
    示例： download_bill('2021-05-01', '2021-05-31')
    """
    # 每日10点更新前一天，11日9:00 获取9日，11日10:00 获取10日
    # 检查start end是否合法
    if not start or not end:
        print("download_bill 请输入开始结束时间")
        return False

    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    # 前两天
    cur_valid = now - datetime.timedelta(days=2)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=1)

    if start <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("download_bill start 非法")
        return False
    if end <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print(
            "download_bill end 非法:",
            end,
            "修正为valid:",
            cur_valid.strftime("%Y-%m-%d"),
        )
        end = cur_valid.strftime("%Y-%m-%d")

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date
    while cur_date <= end_date:
        s = cur_date.strftime("%Y-%m-%d")
        # e = (cur_date + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        e = s
        # 微信支付账单2024-05-06.csv
        base_path = "/data/wechat_pay/raw"
        t = cur_date.strftime("%Y-%m-%d")
        bill_csv_path = base_path + f"/微信支付账单{t}.csv"
        fund_json_path = base_path + f"/资金账单{t}.json"
        # 若存在，则跳过
        if os.path.exists(bill_csv_path) and os.path.exists(fund_json_path):
            print("账单数据已存在，跳过", bill_csv_path)
            cur_date = cur_date + datetime.timedelta(days=1)
            continue

        cmd = f"/usr/bin/java -jar /var/www/cable/le-scripts.jar --spring.profiles.active=prod --task=download_bill -s {s} -e {e} -d {base_path}"
        if verbose:
            print("=" * 30)
            print("执行", cur_date)
            print(cmd)
        out, err = run_cmd(cmd)
        empty_hint = f"{t} 交易账单 failed"
        if empty_hint in out:
            print("当日无支付账单", cur_date)
            write_csv(bill_csv_path, [])
        fund_empty_hint = f"{t} 资金账单 failed"
        if fund_empty_hint in out:
            print("当日无资金账单", cur_date)
            with open(fund_json_path, "w") as f:
                f.write("{}")

        if err:
            print("err:", err)
        if verbose:
            print("output:", out)
        print("=" * 30)
        if cur_date == end_date:
            break
        cur_date = cur_date + datetime.timedelta(days=1)

    print("获取成功")
    return True


def get_access_token():
    # redis_conn = get_redis_connection()
    # ac = redis_conn.get("stable_access_token")
    # print("access_token: ", ac)
    # if not ac:
    # ac, expire = get_stable_token()
    ac, expire = get_stable_token()
    return ac


def sec_2_human(sec):
    m, s = divmod(sec, 60)
    h, m = divmod(m, 60)
    if h == 0:
        return f"{m}分钟{s}秒"
    return f"{h}小时{m}分钟{s}秒"


def get_stable_token(
    appid="wxa39c8d97a73a512f",
    secret="e266147ec6773050849e3952e3238ecf",
    force_refresh=False,
):
    """
    获取微信公众平台的稳定凭证（access_token）
    :param appid: 账号唯一凭证
    :param secret: 账号唯一凭证密钥
    :param force_refresh: 是否强制刷新token，默认为False
    :return: 获取到的凭证或者错误信息
    """
    url = "https://api.weixin.qq.com/cgi-bin/stable_token"
    payload = {
        "grant_type": "client_credential",
        "appid": appid,
        "secret": secret,
        "force_refresh": force_refresh,
    }
    response = requests.post(url, json=payload)
    if response.status_code == 200:
        # 解析响应的JSON数据
        data = response.json()
        # 检查是否存在错误码
        if "access_token" in data:
            return data["access_token"], data["expires_in"]
        else:
            return "Error: " + data.get("errmsg", "Unknown error"), None
    else:
        return f"HTTP Error: {response.status_code}", None

    # # 调用函数示例
    # appid = "你的AppID"
    # secret = "你的AppSecret"

    # # 如果你想强制刷新token，则将force_refresh设为True
    # access_token, expires_in = get_stable_token(appid, secret, force_refresh=False)

    # if access_token:
    #     print("Access Token:", access_token)
    #     print("Expires In:", expires_in)
    # else:
    #     # 在这里处理错误
    #     print(expires_in)  # 这将打印出错误信息


def handler(signum, frame):
    print("程序退出")
    exit(0)


# 绑定键盘中断信号
signal.signal(signal.SIGINT, handler)

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额

# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)
# print(PROJ_DIR)
from conf import get_db_conf

# 二专
CD2_PID = 642
SKU_DEFAULT = "默认"
CD2_SKU_UNIQUE = "8f60540739a342028ef8f3cc5e8106f7"

# 苍蝇黑胶
VINYL_PID = 646
VINYL_SKU1 = "黑胶"
VINYL_SKU1_UNIQUE = "1784076987816488960"
VINYL_SKU2 = "黑胶+唱片袋"
VINYL_SKU2_UNIQUE = "1784076987820683264"


def filter_special(s):
    return s.replace("`", "").strip()


from sqlalchemy import (
    DECIMAL,
    TIMESTAMP,
    BigInteger,
    Boolean,
    Column,
    DateTime,
    Integer,
    Numeric,
    String,
    create_engine,
    or_,
    text,
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

Base = declarative_base()


class User(Base):
    __tablename__ = "yx_user"

    uid = Column(Integer, primary_key=True)
    username = Column(String)
    real_name = Column(String)
    wxMiniProgramID = Column(String)
    integral = Column(Numeric)

    def __repr__(self):
        return (
            f"<User(name={self.username}, uid={self.uid}, real_name={self.real_name})>"
        )

    # 分页获取
    @classmethod
    def query_page(cls, session, page, page_size):
        return session.query(cls).limit(page_size).offset((page - 1) * page_size).all()


class Order(Base):
    __tablename__ = "yx_store_order"

    id = Column(Integer, primary_key=True)
    order_id = Column(String)
    extend_order_id = Column(String)
    uid = Column(Integer)
    real_name = Column(String)
    user_phone = Column(String)
    user_address = Column(String)
    delivery_id = Column(String)
    id_card = Column(String)
    cart_id = Column(String)  # 逗号分割
    total_num = Column(Integer)  # 商品总数
    total_price = Column(Numeric)  # 商品总价
    total_postage = Column(Numeric)  # 邮费
    pay_price = Column(Numeric)  # 支付金额 = 商品总价 + 邮费
    gain_integral = Column(Numeric)  # 获得积分

    refund_price = Column(Numeric)  # 退款金额
    paid = Column(Boolean)  # 是否付款

    status = Column(Integer)
    refund_status = Column(Integer)
    refund_suspend_status = Column(Integer)
    cancel_status = Column(Integer)
    delivery_status = Column(Integer)
    wechat_order_status = Column(Integer)

    create_time = Column(DateTime)
    pay_time = Column(DateTime)
    delivery_time = Column(DateTime)
    finish_time = Column(DateTime)

    is_del = Column(Boolean, name="is_del")

    def __init__(self, items=[]):
        super().__init__()
        self.items = items

    def __repr__(self):
        return f"<Order(order_id={self.order_id}, ctime={self.create_time} 快递={self.delivery_id}, uid={self.uid}, 总价/实付={self.total_price+self.total_postage}/{self.pay_price})>"

    @classmethod
    def query(cls, session, order_id):
        return session.query(cls).filter(cls.order_id == order_id).first()

    @classmethod
    def query_by_trade_no(cls, session, trade_no):
        # trade_no 为 order_id 或 extend_order_id
        return (
            session.query(cls)
            .filter(or_(cls.order_id == trade_no, cls.extend_order_id == trade_no))
            .first()
        )

    @classmethod
    def query_range(cls, session, start, end):
        """时间范围 [start, end)"""
        return (
            session.query(cls)
            .filter(cls.create_time >= start, cls.create_time < end)
            .all()
        )

    @classmethod
    def query_by_pay_time_range(cls, session, start, end):
        """时间范围 [start, end)"""
        return (
            session.query(cls)
            .filter(cls.pay_time >= start, cls.pay_time < end, cls.paid == 1)
            .all()
        )

    @classmethod
    def query_by_user_id(cls, session, uid):
        return session.query(cls).filter(cls.uid == uid).all()


class Product(Base):
    __tablename__ = "yx_store_product"

    id = Column(Integer, primary_key=True)
    name = Column(String, name="store_name")
    price = Column(Numeric)
    onsale = Column(Boolean, name="is_show")
    is_del = Column(Boolean, name="is_del")

    def __init__(self, id, name, price):
        """
        id: 商品id
        name: 商品名称
        price: 商品价格
        """
        super().__init__()
        self.id = id
        self.name = name
        self.price = price
        self.sku_list = []

    @classmethod
    def get(cls, session, id):
        return session.query(cls).filter(cls.id == id).first()

    @classmethod
    def all(cls, session, is_del=False):
        return session.query(cls).filter(cls.is_del == is_del).all()
        # return session.query(cls).filter(cls.id == ids).all()

    def fetch_sku_list(self, session):
        sku_list = Sku.all(session, self.id)
        return sku_list


class NoticeSub(Base):
    __tablename__ = "yx_store_product_relation"
    id = Column(Integer, primary_key=True)
    uid = Column(Integer, primary_key=True)
    noticed = Column(Boolean, name="status")
    type = Column(String)  # want
    product_id = Column(Integer)
    sku_unique = Column(String, name="sku")
    is_del = Column(Integer)

    create_time = Column(DateTime)
    update_time = Column(DateTime)

    @classmethod
    def query_range(cls, session, start, end):
        """时间范围 [start, end)"""
        return (
            session.query(cls)
            .filter(cls.create_time >= start, cls.create_time < end)
            .all()
        )


class Sku(Base):
    __tablename__ = "yx_store_product_attr_value"

    id = Column(Integer, primary_key=True)
    product_id = Column(Integer)
    sku = Column(String)
    unique = Column(String)
    sale_limit = Column(Integer, name="stock")
    sales = Column(Integer)
    price = Column(Numeric)

    @classmethod
    def all(cls, session, product_id):
        return session.query(cls).filter(cls.product_id == product_id).all()

    @classmethod
    def get_by_unique(cls, session, product_id, unique):
        return (
            session.query(cls)
            .filter(cls.product_id == product_id, cls.unique == unique)
            .first()
        )

    @classmethod
    def get_by_sku(cls, session, product_id, sku):
        return (
            session.query(cls)
            .filter(cls.product_id == product_id, cls.sku == sku)
            .first()
        )

    @classmethod
    def get_by_id(cls, session, id):
        return session.query(cls).filter(cls.id == id).first()


class Cart(Base):
    __tablename__ = "yx_store_order_cart_info"

    id = Column(Integer, primary_key=True)
    oid = Column(Integer)
    cart_id = Column(Integer)
    order_id = Column(String)
    product_id = Column(Integer)
    cart_info = Column(String)  # 具体商品内容
    is_after_sales = Column(Boolean)

    def __repr__(self):
        return f"<Cart(id={self.id}, oid={self.oid}, cart_id={self.cart_id}, order_id={self.order_id}, product_id={self.product_id}, cart_info={self.get_simple_saved_cart_info()}, is_after_sales={self.is_after_sales})>"
    
    @property
    def cart_info_dict(self):
        return json.loads(self.cart_info)
    
    def get_simple_saved_cart_info(self):
        cart_info = self.cart_info_dict
        return {
            "refundNum": cart_info.get("refundNum"),
            "uid": cart_info.get("uid"),
            "productAttrUnique": cart_info.get("productAttrUnique"),
            "productId": cart_info.get("productId"),
            "cartNum": cart_info.get("cartNum"),
        }

    # 按照id逗号分隔查询cart
    @classmethod
    def query(cls, session, ids, order_id):
        assert ids
        assert order_id
        query = session.query(cls)
        if "," in ids:
            query = query.filter(cls.cart_id.in_(ids.split(",")))
        else:
            query = query.filter(cls.cart_id == ids)

        query = query.filter(cls.order_id == order_id)

        return query.all()

    @classmethod
    def query_range(cls, session, product_id, start, end):
        """时间范围 [start, end)"""
        return (
            session.query(cls)
            .filter(
                product_id == product_id,
                cls.create_time >= start,
                cls.create_time < end,
            )
            .all()
        )


class AfterSales(Base):
    __tablename__ = "yx_store_after_sales"
    id = Column(Integer, primary_key=True, name="id")
    order_id = Column(String, name="order_code")
    loss_status = Column(Integer, name="loss_status")
    # 售后流程状态 0. 已申请售后等待平台审核 1. 平台已审核,等待用户发货/退款 2. 用户已发货 3. 已完成(已退款) 4. 已完成，商品无法二次销售 5. 已完成，换货完成
    state = Column(Integer)
    # 售后关闭状态(0正常、1用户取消、2商家拒绝、3系统关闭)
    sales_state = Column(Integer)

    def __repr__(self):
        return f"<AfterSales(id={self.id}, order_id={self.order_id}, state={self.state}, sales_state={self.sales_state})>"

    @classmethod
    def get(cls, session, id):
        return session.query(cls).filter(cls.id == id).first()

    @classmethod
    def get_refunded(cls, session, order_id):
        return (
            session.query(cls)
            .filter(cls.order_id == order_id, cls.state.in_(set([3, 4, 5])))
            .first()
        )

    @classmethod
    def loss_list(cls, session):
        return session.query(cls).filter(cls.loss_status == 1).all()

    @classmethod
    def get_wait_send_back_sales(cls, session, sales_state=0):
        """等待用户寄回"""
        return (
            session.query(cls)
            .filter(cls.state == 1, cls.sales_state == sales_state)
            .all()
        )
    
    @classmethod
    def get_success_refund_after_sales(cls, session, order_id):
        return (
            session.query(cls)
            .filter(cls.order_id == order_id, cls.state.in_(set([3, ])))
            .first()
        )


class AfterSalesItem(Base):
    __tablename__ = "yx_store_after_sales_item"
    id = Column(Integer, primary_key=True, name="id")
    sales_id = Column(Integer, name="store_after_sales_id")
    product_id = Column(Integer, name="product_id")
    sku_unique = Column(String, name="product_attr_unique")
    cart_info = Column(String)
    num = Column(Integer) # 退货数量

    def __repr__(self):
        return f"<AfterSalesItem(id={self.id}, sales_id={self.sales_id}, product_id={self.product_id}, sku_unique={self.sku_unique}, cart_info={self.get_simple_cart_info()}, num={self.num})>"
    
    def cart_info_dict(self):
        return json.loads(self.cart_info)
    
    def get_simple_cart_info(self):
        cart_info = self.cart_info_dict()
        return {
            "refundNum": cart_info.get("refundNum"), # 这个值不一定牢靠
            "uid": cart_info.get("uid"),
            "productAttrUnique": cart_info.get("productAttrUnique"),
            "productId": cart_info.get("productId"),
            "cartNum": cart_info.get("cartNum"),
        }

    @classmethod
    def query(cls, session, sales_id):
        return session.query(cls).filter(cls.sales_id == sales_id).all()


class AfterSalesStatus(Base):
    __tablename__ = "yx_store_after_sales_status"
    id = Column(Integer, primary_key=True, name="id")
    sales_id = Column(Integer, name="store_after_sales_id")
    change_message = Column(String)
    change_type = Column(Integer)
    change_time = Column(DateTime)

    def __repr__(self):
        return f"<AfterSalesStatus(sales_id={self.sales_id}, change_type={self.change_type}, change_message={self.change_message} change_time={self.change_time})>"

    @classmethod
    def get_by_sales(cls, session, sales_id):
        return session.query(cls).filter(cls.sales_id == sales_id).all()

    @classmethod
    def get_by_sales_and_change_type(cls, session, sales_id, change_type=1):
        return (
            session.query(cls)
            .filter(cls.sales_id == sales_id, cls.change_type == change_type)
            .first()
        )

    # 根据时间和类型筛选
    @classmethod
    def query(cls, session, change_type, start, end):
        return (
            session.query(cls)
            .filter(
                cls.change_type == change_type,
                cls.change_time >= start,
                cls.change_time < end,
            )
            .all()
        )

    # 根据时间和类型筛选
    @classmethod
    def query2(cls, session, start, end):
        return (
            session.query(cls)
            .filter(
                cls.change_time >= start,
                cls.change_time < end,
            )
            .all()
        )


class StockRecord(Base):
    __tablename__ = "inventory_record"
    id = Column(Integer, primary_key=True, name="inventory_id")
    spu_id = Column(Integer)
    sku_id = Column(Integer)
    stock = Column(Integer, name="current_quantity")

    def __repr__(self):
        return f"<StockRecord(spu_id={self.spu_id}, sku_id={self.sku_id}, stock={self.stock})>"

    @classmethod
    def query(cls, session, spu_id, sku_id):
        return (
            session.query(cls)
            .filter(cls.spu_id == spu_id, cls.sku_id == sku_id)
            .first()
        )


class InventoryTransaction(Base):
    __tablename__ = "inventory_transaction"

    transaction_id = Column(BigInteger, primary_key=True, autoincrement=True)
    inventory_id = Column(BigInteger, nullable=False)
    operator_id = Column(BigInteger, nullable=False)
    batch_number = Column(String, nullable=False)
    cost = Column(DECIMAL(18, 2), nullable=True)

    transaction_type = Column(String(50), nullable=False)
    quantity = Column(Integer, nullable=False)
    # transaction_date = Column(TIMESTAMP(timezone=False), nullable=False)
    transaction_date = Column(DateTime(timezone=False), nullable=False)

    remarks = Column(String(255))

    @classmethod
    def query_by_id(cls, session, inventoryId):
        return (
            session.query(cls)
            .filter(
                cls.inventory_id == inventoryId,
            )
            .all()
        )

    @classmethod
    def query_by_range(cls, session, inventoryId, start, end):
        return (
            session.query(cls)
            .filter(
                cls.inventory_id == inventoryId,
                cls.transaction_date >= start,
                cls.transaction_date < end,
            )
            .all()
        )


class TmpStock(Base):
    __tablename__ = "tmp_inventory"
    id = Column(Integer, primary_key=True)
    spu_id = Column(Integer)
    sku_unique = Column(String, name="sku_id")
    order_id = Column(Integer)
    summary_date = Column(String)
    total_stock_in = Column(Integer)
    total_stock_out = Column(Integer)
    inventory_adjustments = Column(Integer)
    synced = Column(Boolean)
    ctime = Column(DateTime)

    def __repr__(self):
        return f"<TmpStock(date={self.summary_date} spu_id={self.spu_id}, sku_unique={self.sku_unique}, total_stock_in={self.total_stock_in}, total_stock_out={self.total_stock_out}, inventory_adjustments={self.inventory_adjustments})>"

    @classmethod
    def query_by_date(cls, session, summary_date):
        return session.query(cls).filter(cls.summary_date == summary_date).all()

    @classmethod
    def queryAll(cls, session, summary_date, spu_id, sku_unique, synced=None):
        if synced is not None:
            return (
                session.query(cls)
                .filter(
                    cls.summary_date == summary_date,
                    cls.spu_id == spu_id,
                    cls.sku_unique == sku_unique,
                    cls.synced == synced,
                )
                .all()
            )
        return (
            session.query(cls)
            .filter(
                cls.summary_date == summary_date,
                cls.spu_id == spu_id,
                cls.sku_unique == sku_unique,
            )
            .all()
        )


class TmpStockSummary(Base):
    __tablename__ = "tmp_inventory_summary"
    id = Column(Integer, primary_key=True)
    spu_id = Column(Integer)
    sku_id = Column(String)
    summary_date = Column(String)
    total_stock_in = Column(Integer)
    total_stock_out = Column(Integer)
    inventory_adjustments = Column(Integer)
    synced = Column(Boolean)

    @classmethod
    def query(cls, session, summary_date, spu_id, sku_id):
        return (
            session.query(cls)
            .filter(
                cls.summary_date == summary_date,
                cls.spu_id == spu_id,
                cls.sku_id == sku_id,
            )
            .first()
        )


def get_session(read_only=True):
    # 查询京东订单号、订单状态、退款状态、售后等信息
    # 创建数据库引擎
    # conn_url = "mysql+pymysql://eye:sauron@localhost/cable_dev"
    # 0505的备份
    # conn_url = "mysql+pymysql://eye:sauron@localhost/cable0506"

    # conn_url = "mysql+pymysql://eye:sauron@************/cable_prod"
    # conn_url = "mysql+pymysql://eye:sauron@localhost/cable_prod"
    conn_url = get_db_conf()
    engine = create_engine(conn_url)

    # 创建 session 类型
    Session = sessionmaker(bind=engine)
    session = Session()

    # 执行只读事务
    if read_only:
        session.execute(text("SET TRANSACTION READ ONLY"))
    return session


# 查询所有用户
# all_users = session.query(User).all()
# print(all_users)


def format_date(t):
    return datetime.datetime.strftime(t, "%Y-%m-%d")


def format_2_date_time(t):
    return datetime.datetime.strftime(t, "%Y-%m-%d %H:%M:%S")


def get_cart_sku_id(cart, product_id, sku="默认"):
    cart_info = json.loads(cart.cart_info)
    pid = cart_info.get("productId")
    if pid != product_id:
        return 0
    num = cart_info.get("cartNum")
    product_info = cart_info.get("productInfo")
    name = product_info.get("storeName")
    attr = product_info.get("attrInfo")
    unique = attr.get("unique")
    item_sku = attr.get("sku")
    if item_sku != sku:
        return 0
    return unique


def get_cart_key(cart) -> String:
    cart_info = json.loads(cart.cart_info)
    pid = cart_info.get("productId")
    num = cart_info.get("cartNum")
    product_info = cart_info.get("productInfo")
    name = product_info.get("storeName")
    attr = product_info.get("attrInfo")
    item_sku = attr.get("sku")
    sku_unique = attr.get("unique")
    return "%d--%s" % (pid, item_sku)


def get_cart_num(cart, verbose=False) -> int:
    cart_info = json.loads(cart.cart_info)
    # pid = cart_info.get("productId")
    num = cart_info.get("cartNum")
    # product_info = cart_info.get("productInfo")
    # name = product_info.get("storeName")
    # attr = product_info.get("attrInfo")
    # item_sku = attr.get("sku")
    return int(num)


def get_certain_cart_sku_num(cart, product_id, sku="默认", verbose=False) -> int:
    cart_info = json.loads(cart.cart_info)
    pid = cart_info.get("productId")
    if int(pid) != int(product_id):
        if verbose:
            print("pid not match", pid, product_id)
        return 0
    num = cart_info.get("cartNum")
    product_info = cart_info.get("productInfo")
    name = product_info.get("storeName")
    attr = product_info.get("attrInfo")
    item_sku = attr.get("sku")
    if item_sku != sku:
        if verbose:
            print("sku not match", item_sku, sku)
        return 0
    sku_unique = attr.get("unique")
    return int(num)


def get_certain_cart_sku_refund_num(
    order, cart, product_id, sku="默认", verbose=False
) -> int:
    cart_info = json.loads(cart.cart_info)
    pid = cart_info.get("productId")
    if pid != product_id:
        return 0
    num = cart_info.get("cartNum")
    product_info = cart_info.get("productInfo")
    name = product_info.get("storeName")
    attr = product_info.get("attrInfo")
    item_sku = attr.get("sku")
    if item_sku != sku:
        return 0
    sku_unique = attr.get("unique")
    # 退款记录
    refundNum = cart_info.get("refundNum")
    # print("退款数量", refundNum)
    # 没写就是全部退
    return int(refundNum) or num


def read_json(file_path):
    with open(file_path, "r") as f:
        return json.loads(f.read())


def read_csv(file_path, ignore_existed=False):
    try:
        with open(file_path, "r") as f:
            reader = csv.reader(f)
            return [row for row in reader]
    except Exception as e:
        if ignore_existed:
            return []
        raise e


def write_csv(file_path, rows):
    with open(file_path, "w") as f:
        writer = csv.writer(f)
        for row in rows:
            writer.writerow(row)


def read_wechat_pay_log(f_path, verbose=True):
    rows, summary = read_wechat_pay_log_with_summary(f_path, verbose=verbose)
    return rows


def read_wechat_pay_log_with_summary(f_path, verbose=True):
    """解析微信支付账单"""
    rows = []
    with open(f_path, "r") as f:
        reader = csv.reader(f)
        rows = []
        count = 0
        for row in reader:
            rows.append(row)
    if len(rows) < 2:
        print("当日无交易", f_path)
        return [], {}

    header = rows[0]
    # 去掉行首
    rows = rows[1:]

    # summay header
    summary_header = rows[-2]
    summary_header = [x for x in summary_header if x]
    # summay data
    summary_data = rows[-1]
    summary_data = [filter_special(x) for x in summary_data if x]

    if verbose:
        print("*" * 30)
    # 应结订单总金额
    should_pay = round(Decimal(summary_data[1]), 2)
    should_refund = round(Decimal(summary_data[2]), 2)
    # 手续费总金额
    fee = round(Decimal(summary_data[4]), 2)
    summary = {}
    if verbose:
        print("净收入", ":", should_pay - should_refund - fee)
        summary["净收入"] = should_pay - should_refund - fee
        for i in range(len(summary_header)):
            print(summary_header[i], ":", summary_data[i])
            summary[summary_header[i]] = summary_data[i]

    rows = rows[:-2]
    # print("总交易单数(包含退款交易): ", len(rows))

    rows = [[filter_special(r) for r in x] for x in rows]
    return rows, summary


# 库存 = 库存记录 + 临时库存(未同步的,可为负值）
def all_products(session, verbose=False, onsale_only=True):
    ps = Product.all(session)
    ret = []
    for p in ps:
        sku_list = p.fetch_sku_list(session)
        p.sku_list = sku_list
        if onsale_only:
            if not p.onsale:
                continue
        ret.append(p)
        if verbose:
            for sku in sku_list:
                print(
                    f"{p.name}({p.id}) {sku.sku}, 销售限额: {sku.sale_limit}, 销量: {sku.sales}, unique: {sku.unique}"
                )
            print("*" * 30)
    return ret
