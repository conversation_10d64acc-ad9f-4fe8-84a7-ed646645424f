import csv
import datetime
from decimal import Decimal
import json
import logging
import os

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    SKU_DEFAULT,
    AfterSales,
    AfterSalesItem,
    AfterSalesStatus,
    Cart,
    InventoryTransaction,
    Order,
    Product,
    Sku,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    all_products,
    format_date,
    get_cart_sku_id,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    write_csv,
)

TMP_ORDERS = []

session = get_session(read_only=False)

# 订单状态常量定义
SHIPPED = "SHIPPED"
AFTER_SALE = "AFTER_SALE"
REFUNDED = "REFUNDED"
COMPLETED = "COMPLETED"

ORDER_STATUS = {
    SHIPPED: "已发货",
    AFTER_SALE: "售后中",
    REFUNDED: "已退款",
    COMPLETED: "已完成",
}


def check_stock_report_by_date(
    checked_pid, checked_sku, cur_date, end_day, verbose=False
):
    """商品维度
    统计指定商品SKU的销售情况和累计数据
    """
    ps = all_products(session, verbose=False)

    # 用于累计统计
    total_sold = 0
    total_refund = 0
    total_net = 0

    print(f"\n统计期间: {cur_date} 至 {end_day}")
    print("-" * 50)

    # 使用常量定义状态计数器
    order_count_map = {SHIPPED: 0}

    count_map = {
        SHIPPED: 0,
        AFTER_SALE: 0,
        REFUNDED: 0,
        COMPLETED: 0,
    }

    while cur_date <= end_day:
        for p in ps:
            if checked_pid and p.id != int(checked_pid):
                continue

            for sku in p.sku_list:
                if checked_sku and sku.sku != checked_sku:
                    continue

                # 获取当日支付的数据
                next_date = (
                    datetime.datetime.strptime(cur_date, "%Y-%m-%d")
                    + datetime.timedelta(days=1)
                ).strftime(
                    "%Y-%m-%d"
                )  # 获取下一天的日期
                order_list = Order.query_by_pay_time_range(session, cur_date, next_date)

                # if verbose:
                print(
                    f"\n{cur_date} 商品: {p.id} - {p.name} - {sku.sku}, 订单数: {len(order_list)}"
                )
                for order in order_list:
                    cart_list = Cart.query(session, order.cart_id, order.order_id)
                    is_refund = order.refund_status != 0
                    refund_items = []
                    if is_refund:
                        after_sales = AfterSales.get_refunded(session, order.order_id)
                        if after_sales:
                            refund_items = AfterSalesItem.query(session, after_sales.id)

                    for cart in cart_list:
                        sold_num = get_certain_cart_sku_num(
                            cart, checked_pid, checked_sku
                        )
                        if sold_num == 0:
                            # 不是这个sku的订单，跳过
                            continue

                        refund_num = 0
                        if is_refund:
                            refund_cart = None
                            for item in refund_items:
                                cart_info = json.loads(item.cart_info)
                                if cart_info.get("id") == cart.id:
                                    refund_cart = item
                                    break
                            refund_num = (
                                int(refund_cart["refundNum"])
                                if refund_cart
                                else sold_num
                            )

                        if order.status == 1:
                            if order.refund_status == 0:
                                if order.delivery_status == 2:
                                    count_map[SHIPPED] += sold_num
                                    order_count_map[SHIPPED] += 1
                                    if order.order_id not in TMP_ORDERS:
                                        # print(f'{cur_date} 订单不在导出中: {order.order_id}')
                                        pass
                        elif order.status == 2:
                            if order.refund_status == 0:
                                count_map[COMPLETED] += sold_num
                        elif order.status == -1:
                            if order.refund_status == 2:
                                count_map[REFUNDED] += refund_num
                            else:
                                count_map[AFTER_SALE] += refund_num

                        if verbose:
                            # print(f"sold num: {sold_num}, refund num: {refund_num}")
                            pass

                        net_sales = sold_num - refund_num

                        # 更新累计数据
                        total_sold += sold_num
                        total_refund += refund_num
                        total_net += net_sales

        # 只在有数据时显示当日统计
        print(f"\n{cur_date} 当日统计:")
        print(f"销售数量: {total_sold}")
        print(f"退货数量: {total_refund}")
        print(f"净销售: {total_net}")
        print("-" * 30)

        # 更新到下一天
        cur_date = (
            datetime.datetime.strptime(cur_date, "%Y-%m-%d")
            + datetime.timedelta(days=1)
        ).strftime("%Y-%m-%d")

    # 输出累计统计数据
    print("\n累计统计:")
    print(f"总销售数量: {total_sold}")
    print(f"总退货数量: {total_refund}")
    print(f"总净销售: {total_net}")

    print(f"已发货商品数: {count_map[SHIPPED]}, 订单数： {order_count_map[SHIPPED]}")
    print(f"售后中商品数: {count_map[AFTER_SALE]}")
    print(f"已退款商品数: {count_map[REFUNDED]}")
    print(f"已完成商品数: {count_map[COMPLETED]}")
    print("-" * 50)


if __name__ == "__main__":
    from optparse import OptionParser

    parser = OptionParser(usage="""检查库存""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    parser.add_option(
        "-s", "--start", dest="start", help="start date", default="2024-04-01"
    )
    # parser.add_option("-e", "--end", dest="end", help="end date", default="2024-05-01")
    parser.add_option("-e", "--end", dest="end", help="end date", default=None)
    parser.add_option("-o", "--operation", dest="operation", help="operation")

    parser.add_option("-p", "--pid", dest="pid", help="商品ID", default=CD2_PID)
    parser.add_option("-k", "--sku", dest="sku", help="SKU", default=SKU_DEFAULT)

    options, args = parser.parse_args()

    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    end = options.end
    if not end:
        end = options.start

    """超售检测"""
    """python check_for_xx.py -o show_sku_stock -v -s 2024-12-16 -e 2024-12-17 -p 642 -k 默认"""
    """python check_for_xx.py -o show_sku_stock -v -s 2025-01-06 -e 2025-01-06 -p 664 -k 默认"""
    check_stock_report_by_date(
        options.pid, options.sku, options.start, end, verbose=options.verbose
    )

    session.close()
