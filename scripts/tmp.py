stock_data = """-1	`1796590044211879936
-1	`1796722269704200192
-3	`1796730345756860416
-1	`1796754379001344000
-1	`1796770466019454976
-1	`1796791497497030656
-1	`1796797137552453632
-1	`1796801594449244160
-1	`1796802294365335552
-1	`1796804064474865664
-1	`1796818179406733312
-1	`1796827798418006016
1	`1796827798418006016
-1	`1796865144177860608
-1	`1796915506066796544
-1	`1796926876208373760
-1	`1796943386331099136
-1	`1796953683594883072
-1	`1797186889124454400
-1	`1797215759491964928
-2	`1797242224421937152
-1	`1797258849074323456
-1	`1797292619122909184
-1	`1797294693369815040
-1	`1797308281660682240
2	`1797242224421937152
-1	`1797444308257775616
-1	`1797444748928135168
-1	`1797462991856250880
-1	`1797475869225168896
-1	`1797538638175707136
-1	`1797570296228655104
-1	`1797601412134252544
-1	`1797651647103541248
-1	`1797660050198671360
-1	`1797661827853754368
-1	`1797670928084148224
-1	`1797679232172732416
-1	`1797781393963524096
-1	`1797788577254383616
-1	`1797815840226189312
-1	`1797823746287906816
-1	`1797860206336581632
-1	`1797892079024644096
-1	`1797935827452473344
-1	`1797938895602626560
-1	`1797985940753391616
-1	`1797988011288338432
-3	`1797991647003320320
-1	`1797992376451506176
-1	`1798024803777556480
-1	`1798039366535262208
-2	`1798168976442830848
-1	`1798172214755897344
-1	`1798184452069826560
-1	`1798184819146928128
-1	`1798194032967462912
-1	`1798206696905023488
-1	`1798208670522187776
-1	`1798212258292084736
-1	`1798212814234492928
-1	`1798213729087696896
-1	`1798215141662171136
-1	`1798251734418628608
-1	`1798258970633216000
-1	`1798260894438825984
-1	`1798263171434192896
-1	`1798269397907841024
-1	`1798293097877118976
-1	`1798301424887574528
-3	`1798343202978181120
-1	`1798363898424893440
-1	`1798444143035326464
-1	`1798524255063814144
-1	`1798546204649369600
-1	`1798564401108787200
-1	`1798574779838537728
-1	`1798575877072662528
-1	`1798586313952243712
-1	`1798615711166279680
-1	`1798630897835089920
-1	`1798635285647503360
-1	`1798638734405574656
-1	`1798653394328526848
-1	`1798658881593847808
-3	`1798659090440826880
-1	`1798667968452796416
-1	`1798696945305296896
-1	`1798740730563305472
-1	`1798750664826855424
1	`1798667968452796416
-1	`1798900649551765504
-1	`1798916172553433088
-1	`1798936883506487296
-2	`1798974549165907968
-1	`1798985058468536320
-1	`1798998133548556288
-1	`1799038892695924736
-1	`1799068992820781056
-1	`1799081364377612288
-1	`1799102906943709184
-1	`1799104624658001920
-1	`1799271226414243840
-1	`1799416482124312576
-1	`1799432726198132736
-1	`1799497342546649088
-1	`1799604198992556032
-1	`1799678403654234112
-1	`1799680905120358400
-1	`1799693499751247872
-2	`1799711116847259648
-1	`1799711410482094080
-1	`1799721339012034560
-1	`1799731202874191872
-1	`1799752703488860160
-1	`1799763103852834816
-1	`1799771182241718272
-1	`1799790987896205312
-2	`1799809187119669248
-1	`1799813206001098752
-1	`1799825653260922880
-1	`1799871311711870976
-1	`1799979788014755840
2	`1799711116847259648
-1	`1800036121858580480
-1	`1800052468336340992
-1	`1800081768410296320
-1	`1800090712415449088
-1	`1800195895837372416
1	`1800195895837372416
-1	`1800288686596726784
-1	`1800389157927821312
-1	`1800405461971804160
-1	`1800412334431182848
-1	`1800449244964892672
-1	`1800458278782676992
-1	`1800470696774639616
-1	`1800488988159553536
-1	`1800505833365217280
-1	`1800713021987794944
-1	`1800728775718117376
-1	`1800776167649746944
-1	`1800790220669497344
-1	`1800807045495042048
-1	`1800849554527002624
-1	`1800858616354353152
-1	`1800882117580730368
-1	`1800893318733803520
-1	`1800909152176873472
-1	`1801067040035872768
-1	`1801067063523975168
-1	`1801067511261736960
-1	`1801067523454578688
-1	`1801067561652101120
-1	`1801067832763523072
-1	`1801068606159626240
-1	`1801068695204704256
-1	`1801068940043005952
-2	`1801068944308613120
-1	`1801069187175587840
-1	`1801069232381796352
-1	`1801069447516037120
-1	`1801069516608806912
-1	`1801069691276402688
-1	`1801069887943127040
-1	`1801071123207593984
-1	`1801071663056461824
-1	`1801071930200076288
-1	`1801072113629573120
-1	`1801072974414000128
-1	`1801073561587200000
-1	`1801074053159628800
-1	`1801074992872464384
-1	`1801075476664459264
-1	`1801076294377578496
-1	`1801077101986623488
-1	`1801077491297722368
-1	`1801078928488570880
-1	`1801078992309100544
-1	`1801079362913611776
-1	`1801080439943766016
-1	`1801080768114499584
-3	`1801082072358498304
-3	`1801082332107546624
-1	`1801083326363119616
-1	`1801083933069193216
-1	`1801084743920427008
-1	`1801084755702226944
-1	`1801085116248797184
-1	`1801086261218287616
-1	`1801087726250930176
-1	`1801087775722745856
-2	`1801088099791450112
-1	`1801089100850176000
-1	`1801089665403494400
-1	`1801089903862263808
-1	`1801090947166019584
-1	`1801091433931776000
-1	`1801092525054799872
-2	`1801093175469715456
-1	`1801093219379884032
1	`1801090947166019584
-1	`1801094545190989824
-1	`1801094767900139520
-1	`1801095406390648832
-1	`1801096322732830720
-1	`1801097336626126848
-1	`1801098129886457856
-1	`1801100383154647040
-1	`1801100644329762816
-1	`1801100827864113152
1	`1801094767900139520
-1	`1801108506502279168
-1	`1801108803605803008
-1	`1801109492025303040
-1	`1801109681691729920
-1	`1801110064371638272
-1	`1801110078103793664
-1	`1801110841144160256
-1	`1801110850073829376
-1	`1801113605609926656
-1	`1801115445730783232
-3	`1801116757562269696
-1	`1801118040713109504
-1	`1801119714001334272
-3	`1801121053196460032
-2	`1801124101276872704
-1	`1801127177966624768
-1	`1801152468084633600
-1	`1801156174284890112
-1	`1801170513230340096
-1	`1801171964761513984
-1	`1801183996135124992
-1	`1801185767179988992
-1	`1801189862863314944
-1	`1801228300174012416
-1	`1801231858827042816
-1	`1801271342578839552
-1	`1801274333465714688
-1	`1801315280773750784
-1	`1801429307231346688
-1	`1801465160011915264
-1	`1801468479593816064
-1	`1801469120928063488
-1	`1801475401105580032
-1	`1801476611741102080
-1	`1801511418470641664
-1	`1801523889096925184
-1	`1801527519585804288
-1	`1801531153983774720
1	`1801531153983774720
-1	`1801546268057579520
-1	`1801546962147782656
-1	`1801603203305877504
-1	`1801607429494972416
-1	`1801619704083685376
-1	`1801631171956744192
-1	`1801633519814877184
-1	`1801652820257058816
-1	`1801829110016679936
-1	`1801835129094578176
-1	`1801871872850173952
-1	`1801878878415794176
-1	`1801881187107516416
-1	`1802006635862859776
-1	`1802007906476924928
-1	`1802024727162691584
-1	`1802029270776717312
-1	`1802044821972496384
-2	`1802151485287473152
-1	`1802266097790005248
-1	`1802301489478152192
-1	`1802312806951854080
-1	`1802335518315290624
-1	`1802446877220708352
-1	`1802527040646193152
-1	`1802537721890119680
-1	`1802550338985697280
-1	`1802562037147017216
-1	`1802646459254874112
-1	`1802657389799841792
-1	`1802679048762077184
-3	`1802691056559497216"""


stock_record = []
for line in stock_data.split("\n"):
    stock_change, order_id = line.split("\t")
    order_id = order_id.strip("`")
    stock_record.append((int(stock_change), order_id))

total_stock_change = sum([stock_change for stock_change, _ in stock_record])

print("Total stock change: ", total_stock_change)
print(
    "Total stock out: ",
    sum([stock_change for stock_change, _ in stock_record if stock_change < 0]),
)
print(
    "Total stock in: ",
    sum([stock_change for stock_change, _ in stock_record if stock_change > 0]),
)


funds_in_data = """1	`1796590044211879936
1	`1796722269704200192
3	`1796730345756860416
1	`1796754379001344000
1	`1796770466019454976
1	`1796791497497030656
1	`1796797137552453632
1	`1796801594449244160
1	`1796802294365335552
1	`1796804064474865664
1	`1796818179406733312
1	`1796827798418006016
1	`1796865144177860608
1	`1796915506066796544
1	`1796926876208373760
1	`1796943386331099136
1	`1796953683594883072
1	`1797186889124454400
1	`1797215759491964928
2	`1797242224421937152
1	`1797258849074323456
1	`1797292619122909184
1	`1797294693369815040
1	`1797308281660682240
1	`1797444308257775616
1	`1797444748928135168
1	`1797462991856250880
1	`1797475869225168896
1	`1797538638175707136
1	`1797570296228655104
1	`1797601412134252544
1	`1797651647103541248
1	`1797660050198671360
1	`1797661827853754368
1	`1797670928084148224
1	`1797679232172732416
1	`1797781393963524096
1	`1797788577254383616
1	`1797815840226189312
1	`1797823746287906816
1	`1797860206336581632
1	`1797892079024644096
1	`1797935827452473344
1	`1797938895602626560
1	`1797985940753391616
1	`1797988011288338432
3	`1797991647003320320
1	`1797992376451506176
1	`1798024803777556480
1	`1798039366535262208
2	`1798168976442830848
1	`1798172214755897344
1	`1798184452069826560
1	`1798184819146928128
1	`1798194032967462912
1	`1798206696905023488
1	`1798208670522187776
1	`1798212258292084736
1	`1798212814234492928
1	`1798213729087696896
1	`1798215141662171136
1	`1798251734418628608
1	`1798258970633216000
1	`1798260894438825984
1	`1798263171434192896
1	`1798269397907841024
1	`1798293097877118976
1	`1798301424887574528
3	`1798343202978181120
1	`1798363898424893440
1	`1798444143035326464
1	`1798524255063814144
1	`1798546204649369600
1	`1798564401108787200
1	`1798574779838537728
1	`1798575877072662528
1	`1798586313952243712
1	`1798615711166279680
1	`1798630897835089920
1	`1798635285647503360
1	`1798638734405574656
1	`1798653394328526848
1	`1798658881593847808
3	`1798659090440826880
1	`1798667968452796416
1	`1798696945305296896
1	`1798740730563305472
1	`1798750664826855424
1	`1798900649551765504
1	`1798916172553433088
1	`1798936883506487296
2	`1798974549165907968
1	`1798985058468536320
1	`1798998133548556288
1	`1799038892695924736
1	`1799068992820781056
1	`1799081364377612288
1	`1799102906943709184
1	`1799104624658001920
1	`1799271226414243840
1	`1799416482124312576
1	`1799432726198132736
1	`1799497342546649088
1	`1799604198992556032
1	`1799678403654234112
1	`1799680905120358400
1	`1799693499751247872
2	`1799711116847259648
1	`1799711410482094080
1	`1799721339012034560
1	`1799731202874191872
1	`1799752703488860160
1	`1799763103852834816
1	`1799771182241718272
1	`1799790987896205312
2	`1799809187119669248
1	`1799813206001098752
1	`1799825653260922880
1	`1799871311711870976
1	`1799979788014755840
1	`1800036121858580480
1	`1800052468336340992
1	`1800081768410296320
1	`1800090712415449088
1	`1800288686596726784
1	`1800389157927821312
1	`1800405461971804160
1	`1800412334431182848
1	`1800449244964892672
1	`1800458278782676992
1	`1800470696774639616
1	`1800488988159553536
1	`1800505833365217280
1	`1800713021987794944
1	`1800728775718117376
1	`1800776167649746944
1	`1800790220669497344
1	`1800807045495042048
1	`1800849554527002624
1	`1800858616354353152
1	`1800882117580730368
1	`1800893318733803520
1	`1800909152176873472
1	`1801067063523975168
1	`1801067040035872768
1	`1801067523454578688
1	`1801067561652101120
1	`1801067511261736960
1	`1801067832763523072
1	`1801068695204704256
1	`1801068940043005952
1	`1801068606159626240
2	`1801068944308613120
1	`1801069187175587840
1	`1801069232381796352
1	`1801069447516037120
1	`1801069516608806912
1	`1801069691276402688
1	`1801069887943127040
1	`1801071123207593984
1	`1801071663056461824
1	`1801071930200076288
1	`1801072113629573120
1	`1801072974414000128
1	`1801073561587200000
1	`1801074053159628800
1	`1801074992872464384
1	`1801075476664459264
1	`1801076294377578496
1	`1801077101986623488
1	`1801077491297722368
1	`1801078928488570880
1	`1801079362913611776
1	`1801080439943766016
1	`1801080768114499584
1	`1801078992309100544
3	`1801082072358498304
3	`1801082332107546624
1	`1801083326363119616
1	`1801083933069193216
1	`1801084743920427008
1	`1801084755702226944
1	`1801085116248797184
1	`1801086261218287616
1	`1801087726250930176
1	`1801087775722745856
2	`1801088099791450112
1	`1801089100850176000
1	`1801089665403494400
1	`1801089903862263808
1	`1801090947166019584
1	`1801091433931776000
1	`1801092525054799872
2	`1801093175469715456
1	`1801093219379884032
1	`1801094767900139520
1	`1801094545190989824
1	`1801095406390648832
1	`1801096322732830720
1	`1801097336626126848
1	`1801098129886457856
1	`1801100383154647040
1	`1801100644329762816
1	`1801100827864113152
1	`1801108506502279168
1	`1801108803605803008
1	`1801109492025303040
1	`1801109681691729920
1	`1801110064371638272
1	`1801110078103793664
1	`1801110841144160256
1	`1801110850073829376
1	`1801113605609926656
1	`1801115445730783232
3	`1801116757562269696
1	`1801118040713109504
1	`1801119714001334272
3	`1801121053196460032
2	`1801124101276872704
1	`1801127177966624768
1	`1801152468084633600
1	`1801156174284890112
1	`1801170513230340096
1	`1801171964761513984
1	`1801183996135124992
1	`1801185767179988992
1	`1801189862863314944
1	`1801228300174012416
1	`1801231858827042816
1	`1801271342578839552
1	`1801274333465714688
1	`1801315280773750784
1	`1801429307231346688
1	`1801465160011915264
1	`1801468479593816064
1	`1801469120928063488
1	`1801475401105580032
1	`1801476611741102080
1	`1801511418470641664
1	`1801523889096925184
1	`1801527519585804288
1	`1801531153983774720
1	`1801546268057579520
1	`1801546962147782656
1	`1801603203305877504
1	`1801607429494972416
1	`1801619704083685376
1	`1801631171956744192
1	`1801633519814877184
1	`1801652820257058816
1	`1801829110016679936
1	`1801835129094578176
1	`1801871872850173952
1	`1801878878415794176
1	`1801881187107516416
1	`1802006635862859776
1	`1802007906476924928
1	`1802024727162691584
1	`1802029270776717312
1	`1802044821972496384
2	`1802151485287473152
1	`1802266097790005248
1	`1802301489478152192
1	`1802312806951854080
1	`1802335518315290624
1	`1802446877220708352
1	`1802527040646193152
1	`1802537721890119680
1	`1802550338985697280
1	`1802562037147017216
1	`1802646459254874112
1	`1802657389799841792
1	`1802679048762077184
3	`1802691056559497216"""

funds_out_data = """1	`1796827798418006016
2	`1797242224421937152
1	`1798667968452796416
2	`1799711116847259648
1	`1799068992820781056
1	`1801090947166019584
1	`1801094767900139520
1	`1801531153983774720"""

funds_record = []
for line in funds_in_data.split("\n"):
    stock_change, order_id = line.split("\t")
    order_id = order_id.strip("`")
    funds_record.append((-int(stock_change), order_id))


for line in funds_out_data.split("\n"):
    stock_change, order_id = line.split("\t")
    order_id = order_id.strip("`")
    funds_record.append((int(stock_change), order_id))

print(
    "Total funds stock change: ",
    sum([stock_change for stock_change, _ in funds_record]),
)
print(
    "Total funds stock out: ",
    sum([stock_change for stock_change, _ in funds_record if stock_change < 0]),
)
print(
    "Total funds stock in: ",
    sum([stock_change for stock_change, _ in funds_record if stock_change > 0]),
)


for stock_change, order_id in stock_record:
    found = False
    if stock_change < 0:
        for i, (funds_change, funds_order_id) in enumerate(funds_record):
            if funds_change == stock_change and funds_order_id == order_id:
                found = True
                break
        if not found:
            print("Stock out not covered: ", stock_change, order_id)
    else:
        for i, (funds_change, funds_order_id) in enumerate(funds_record):
            if funds_change == stock_change and funds_order_id == order_id:
                found = True
                break
        if not found:
            print("Stock in not covered: ", stock_change, order_id)

stock_out_orders = set(
    [order_id for stock_change, order_id in stock_record if stock_change < 0]
)
stock_out_in_funds_flow_orders = set(
    [order_id for stock_change, order_id in funds_record if stock_change < 0]
)

# 找到问题单
found = stock_out_orders - stock_out_in_funds_flow_orders
print("found", found)


stock_in_orders = set(
    [order_id for stock_change, order_id in stock_record if stock_change > 0]
)
stock_in_in_funds_flow_orders = set(
    [order_id for stock_change, order_id in funds_record if stock_change > 0]
)

print(
    '资金流水不存在:',
    stock_in_orders - stock_in_in_funds_flow_orders,
    '库存流水不存在:',
    stock_in_in_funds_flow_orders - stock_in_orders,
)
