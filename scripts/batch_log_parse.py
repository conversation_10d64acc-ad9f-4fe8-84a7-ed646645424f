log_dir = "/Users/<USER>/workspace/shop/temple/backend/bug_logs"
order_set = set()
jd_set = set()
merge_map = {}
rows = []
with open(log_dir + "/jd.dat", "r") as f:
    lines = f.readlines()
    for line in lines:
        s = line.split("京东运单号：")
        s = s[1].split(", 订单号: ")
        jd_id = s[0]
        order_id = s[1].strip()
        order_set.add(order_id)
        jd_set.add(jd_id)
        order_ids = merge_map.get(jd_id, [])
        order_ids.append(order_id)
        merge_map[jd_id] = order_ids
        rows.append((order_id, jd_id))

print("面单共: ", len(jd_set))

merge_set = set()
with open(log_dir + "/merge.dat", "r") as f:
    lines = f.readlines()
    for line in lines:
        s = line.split("京东合单: ")
        s = s[1].split(", 面单：")
        order_id = s[0]
        s = s[1].split(",")
        jd_id = s[0]
        order_set.add(order_id)
        merge_set.add(order_id)
        # print(order_id, jd_id)
        rows.append((order_id, jd_id))
        # print("合单信息正常", order_id, jd_id, len(merge_map[jd_id]) + 1)


# 输出order_id，jd_id
for row in rows:
    r = ",".join(row)
    print('"%s",' % r)

print("订单共: ", len(order_set))

print("订单去除合单正常的订单: ", len(order_set) - len(merge_set))