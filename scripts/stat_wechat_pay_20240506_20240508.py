import os
import csv

# 应结订单总金额 不包含退款
total_income = 944950.0
total_refund = 40790.0

fee = 1808.35
fond_refund = 8.74

not_no2_amount = 2220

special_postage = 28 + 24 + 3 * 6

total = total_income - not_no2_amount - special_postage - total_refund
print("总收入", total)

no2_num = total / 180.0
print("二专数量", no2_num)

order_id_set = set()
refund_id_set = set()
# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
print(PROJ_DIR)
with open(PROJ_DIR + "/bug_logs/微信支付订单汇总024-05-02_2024-05-08.csv", "r") as f:
    reader = csv.reader(f)
    rows = []
    count = 0
    for row in reader:
        if count != 0 and count < 4297:
            rows.append(row)

            order_id = row[6].strip("`")
            # print(order_id)
            status = row[9]
            order_id_set.add(order_id)
            if status == "`REFUND":
                refund_id_set.add(order_id)
        count += 1
    print("rows: ", len(rows))
    print("商家订单数量", order_id_set.__len__())
    print("商家除去退款订单数量", order_id_set.__len__() - refund_id_set.__len__())
