import csv
import datetime
from decimal import Decimal
import json
import logging
import os

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    SKU_DEFAULT,
    AfterSales,
    AfterSalesItem,
    AfterSalesStatus,
    Cart,
    Order,
    Product,
    Sku,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    all_products,
    format_date,
    get_cart_sku_id,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    write_csv,
)


G_FIX_MODE = True


def query_tmp_stock(
    session, day="2024-05-06", pid=CD2_PID, sku_unique=CD2_SKU_UNIQUE, verbose=False
):
    query_day = day.replace("-", "")
    summary = TmpStockSummary.query(session, query_day, pid, sku_unique)
    if not summary:
        # print(f"{day} 库存无变化")
        # return []
        pass

    records = TmpStock.queryAll(session, query_day, pid, sku_unique)
    record_in = sum([r.total_stock_in for r in records])
    record_out = sum([r.total_stock_out for r in records])
    if verbose:
        if summary:
            print(
                f"{day}, 出库：{summary.total_stock_out}, 入库：{summary.total_stock_in}，库存调整：{summary.inventory_adjustments}, 同步: {summary.synced}"
            )
        print(f"{day} record_in: {record_in}, record_out: {record_out}")
    return records
    # for r in records:
    #     print("out", r.total_stock_out, r.summary_date, r.spu_id, r.sku_id)


def check(start_date, verbose=False):
    cur_date = start_date
    end_day = datetime.datetime.now().strftime("%Y-%m-%d")

    while cur_date != end_day:
        has = fix_stock(cur_date, verbose=verbose)
        if has:
            print(f"{cur_date} 需要修复")
        else:
            print(f"{cur_date} 无需修复")
        next_day_time = datetime.datetime.strptime(
            cur_date, "%Y-%m-%d"
        ) + datetime.timedelta(days=1)
        cur_date = next_day_time.strftime("%Y-%m-%d")

    print("库存检查完成")


def fix_stock(start_date, fix_mode=G_FIX_MODE, verbose=True):
    """库存基准到
    2024-05-06 15:00:00
    05-06 下单时间：2024-05-06 15:01:45
    2024-05-05 03:00"""
    """2024-05-06 14:44:05"""

    session = get_session(read_only=False)
    ps = all_products(session, verbose=False)
    session.close()

    # 05-06 3:00 到 05-10 卖出的商品
    check_pids = [
        642,  # 二专 5014，在05-05日是库存12799，作为基准线
        # 647,  # 薄薄红 1 无销量
        # 646,  # LP-苍蝇乐队《宿寺：苍蝇》内含CD： 黑胶+唱片袋 2件(06,07各一件)、黑胶 1件(0506)
        # 656,  # 铁盒 2 2024-05-06  已确认
        # 657,  # 苍蝇海报 1 2024-05-06 已确认
        # 641,  # 苍蝇拨片 1，从04-22开始，卖到05-23，15个
    ]
    for p in ps:
        if p.id not in check_pids:
            continue
        if verbose:
            print(start_date, "商品：", p.id, p.name)
        for sku in p.sku_list:
            # 只有一个
            return _fix_stock(start_date, p, sku, fix_mode, verbose=verbose)


def check_valid():
    session = get_session(read_only=True)
    records = (
        session.query(TmpStock)
        .filter(
            TmpStock.spu_id == 642,
            TmpStock.sku_unique == "8f60540739a342028ef8f3cc5e8106f7",
        )
        .all()
    )

    loss_list = AfterSales.loss_list(session)
    loss_order_id_set = set()
    loss_num_map = {}
    for a in loss_list:
        items = AfterSalesItem.query(session, a.id)
        # 临时认为是一个
        num = sum(
            [i.num for i in items if i.sku_unique == "8f60540739a342028ef8f3cc5e8106f7"]
        )
        if num == 0:
            # 不是二专
            continue
        change_sts_list = AfterSalesStatus.get_by_sales(session, a.id)
        s = [s for s in change_sts_list if s.change_type == 3][0]
        print("报损：order_id", a.order_id, "num", num, s)
        loss_num_map[a.order_id] = num
        loss_order_id_set.add(a.order_id)
    # print("loss_order_id_set", loss_order_id_set)

    records = [r for r in records if r.summary_date >= "20240401"]

    o_map = {}
    for r in records:
        if not r.order_id:
            continue
        r_list = o_map.get(r.order_id, [])
        r_list.append(r)
        o_map[r.order_id] = r_list

    succ_count = 0
    for k, v in o_map.items():
        if len(v) > 2:
            print("异常", k, len(v))
            err_records = session.query(TmpStock).filter(TmpStock.order_id == k).all()
            for r in err_records:
                print(r.id, r.summary_date, r.inventory_adjustments)
            print("===")
        if len(v) == 2:
            oid = v[0].order_id
            r = v[0]
            o = Order.query(session, oid)
            if o.order_id in loss_order_id_set:
                # 报损的情况
                print(
                    "已报损，但有回滚记录",
                    o.id,
                    o.order_id,
                    o.create_time,
                    r.summary_date,
                )

        elif len(v) == 1:
            # 只有售出
            oid = v[0].order_id
            r = v[0]
            o = Order.query(session, oid)
            if o.paid == 0:
                if r.inventory_adjustments > 0:
                    print(
                        "未支付，但没有入账记录",
                        o.id,
                        o.order_id,
                        o.create_time,
                        r.summary_date,
                        r.inventory_adjustments,
                        "refund",
                        o.refund_status == 2,
                        o.pay_time,
                        o.finish_time,
                    )
                else:
                    print(
                        "未支付，但没有回滚",
                        o.id,
                        o.order_id,
                        o.create_time,
                        r.summary_date,
                        r.inventory_adjustments,
                        "refund",
                        o.refund_status == 2,
                        o.pay_time,
                        o.finish_time,
                    )
                # tmp_sum = TmpStockSummary.query(session, r.summary_date, 642, "8f60540739a342028ef8f3cc5e8106f7")
                continue
            if o.refund_status == 2:
                if r.inventory_adjustments > 0:
                    print(
                        "已退款，但没有入账记录",
                        o.id,
                        o.order_id,
                        o.create_time,
                        r.summary_date,
                        r.inventory_adjustments,
                        o.pay_time,
                        o.finish_time,
                    )
                else:
                    if o.order_id in loss_order_id_set:
                        # 报损的情况
                        print(
                            "[跳过]报损商品，有一条出库记录",
                            o.id,
                            o.order_id,
                            o.create_time,
                            r.summary_date,
                            r.inventory_adjustments,
                            "refund",
                            o.refund_status == 2,
                        )
                    else:
                        # 查询售后
                        a = AfterSales.get_refunded(session, o.order_id)
                        items = AfterSalesItem.query(session, a.id)
                        num = items[0].num
                        print(
                            "已退款，但未回滚",
                            o.id,
                            o.order_id,
                            o.create_time,
                            r.summary_date,
                            r.inventory_adjustments,
                            "需要回滚数量",
                            num,
                            o.pay_time,
                            o.finish_time,
                        )

                continue
            if o.cancel_status != 0:
                if r.inventory_adjustments > 0:
                    print(
                        "已关闭或取消，但没有入账记录",
                        o.id,
                        o.order_id,
                        o.create_time,
                        r.summary_date,
                        r.inventory_adjustments,
                        o.pay_time,
                        o.finish_time,
                    )
                else:
                    print(
                        "已关闭或取消，但未回滚",
                        o.id,
                        o.order_id,
                        o.create_time,
                        r.summary_date,
                        r.inventory_adjustments,
                        "refund",
                        o.refund_status == 2,
                        o.pay_time,
                        o.finish_time,
                    )
                continue

    session.close()


def check_daily_stock_sum():
    session = get_session(read_only=True)

    date = "20240506"
    date = "20240422"

    records = (
        session.query(TmpStock)
        .filter(
            TmpStock.summary_date == date,
            TmpStock.spu_id == 642,
            TmpStock.sku_unique == "8f60540739a342028ef8f3cc5e8106f7",
        )
        .all()
    )

    summary = TmpStockSummary.query(
        session, date, 642, "8f60540739a342028ef8f3cc5e8106f7"
    )
    print(
        "summary",
        summary.total_stock_in,
        summary.total_stock_out,
        summary.inventory_adjustments,
    )

    o_map = {}
    for r in records:
        if not r.order_id:
            continue
        r_list = o_map.get(r.order_id, [])
        r_list.append(r)
        o_map[r.order_id] = r_list

    for k, v in o_map.items():
        if len(v) > 2:
            print("异常", k, len(v))
            err_records = session.query(TmpStock).filter(TmpStock.order_id == k).all()
            for r in err_records:
                print(r.id, r.summary_date, r.inventory_adjustments)
            print("===")

    total_adjust = sum([r.inventory_adjustments for r in records])
    print("total_adjust", total_adjust)

    total_adjust = sum([r.inventory_adjustments for r in records if r.order_id])
    print("total_adjust", total_adjust)

    session.close()


def _fix_stock(start_date, p, sku, fix_mode=False, verbose=True):
    if verbose:
        print("修复模式", fix_mode)
    session = get_session(read_only=not fix_mode)

    cur_date = start_date

    records = query_tmp_stock(session, cur_date, p.id, sku.unique, verbose=False)

    next_day = (
        datetime.datetime.strptime(cur_date, "%Y-%m-%d") + datetime.timedelta(days=1)
    ).strftime("%Y-%m-%d")

    has_adjust = len(records)
    if not has_adjust:
        d = datetime.datetime.strptime(cur_date, "%Y-%m-%d")
        cur_date = d - datetime.timedelta(days=1)
        cur_date = cur_date.strftime("%Y-%m-%d")
        if verbose:
            print(start_date, "无库存变化记录")
        session.close()
        return False

    o_list = Order.query_range(session, cur_date, next_day)
    related_os = []
    for o in o_list:
        items = Cart.query(session, o.cart_id, o.order_id)
        num = sum([get_certain_cart_sku_num(c, p.id, verbose=False) for c in items])
        o.items = items
        if num > 0:
            related_os.append(o)

    if verbose:
        for o in related_os:
            print(o)

    tmp_stock_out = sum([r.total_stock_out for r in records])
    tmp_stock_in = sum([r.total_stock_in for r in records])

    used_out_order = {}
    cancel_or_close_order = {}
    if verbose:
        print("比对库存变化记录开始")

    o_p_map = {}
    o_p_refund_map = {}
    for o in related_os:
        num = sum(get_certain_cart_sku_num(c, p.id) for c in o.items)
        o_p_map[o.order_id] = num
        num = sum(get_certain_cart_sku_refund_num(o, c, p.id) for c in o.items)
        o_p_refund_map[o.order_id] = num

    for r in records:
        if r.total_stock_out > 0:
            # 找到对应数量的支付订单
            vs = used_out_order.values()
            used_out_order_id_set = set([v.order_id for v in vs])
            for o in related_os:
                if o.order_id in used_out_order_id_set:
                    continue
                num = o_p_map[o.order_id]
                if num == r.total_stock_out:
                    used_out_order[r.id] = o
                    used_out_order_id_set.add(o.order_id)
                    break
            if r.id not in used_out_order:
                print(f"{start_date} 未找到对应的支付订单", r.id, r.total_stock_out)
    if verbose:
        print("比对库存变化记录完成")

    cancel_or_close_order_list = [
        o for o in related_os if (o.cancel_status != 0 or o.refund_status == 2)
    ]

    if verbose:
        print("处理退款记录")
    for r in records:
        if r.total_stock_in > 0:
            # 退款
            vs = cancel_or_close_order.values()
            cancel_order_id_set = set([v.order_id for v in vs])
            for o in cancel_or_close_order_list:
                if o.order_id in cancel_order_id_set:
                    continue
                num = o_p_map[o.order_id]
                if o.refund_status == 2:
                    # 处理部分退款
                    is_all_after_sales = False
                    items = o.items
                    if len(items) == 1:
                        is_all_after_sales = True
                    else:
                        is_after_sales_count = sum(
                            [cart.is_after_sales for cart in items]
                        )
                        if is_after_sales_count == 0:
                            is_all_after_sales = True
                    if is_all_after_sales:
                        if num == r.total_stock_in:
                            cancel_or_close_order[r.id] = o
                            cancel_order_id_set.add(o.order_id)
                            break
                    else:
                        num = o_p_refund_map[o.order_id]
                        if num == r.total_stock_in:
                            cancel_or_close_order[r.id] = o
                            cancel_order_id_set.add(o.order_id)
                            break
                else:
                    if num == r.total_stock_in:
                        cancel_or_close_order[r.id] = o
                        cancel_order_id_set.add(o.order_id)
                        break

            if r.id not in cancel_or_close_order:
                print(
                    f"{start_date} 未找到对应的退款订单, tmp inventory:",
                    r.id,
                    r.summary_date,
                    "数量:",
                    r.total_stock_in,
                )

    if verbose:
        print("处理退款记录完成")

    if len(records) != len(used_out_order) + len(cancel_or_close_order):
        # 查询未匹配的库存变化记录
        vs = used_out_order.values()
        used_out_order_id_set = set([v.order_id for v in vs])
        vs = cancel_or_close_order.values()
        cancel_order_id_set = set([v.order_id for v in vs])
        for o in related_os:
            if o.order_id in used_out_order_id_set:
                continue
            if o.order_id in cancel_order_id_set:
                continue
            print("未匹配订单", o.order_id, o.status, o.cancel_status)
        if verbose:
            print(
                "数量不匹配",
                len(records),
                len(used_out_order),
                len(cancel_or_close_order),
            )
        # if fix_mode:
        #     raise Exception("库存变化记录未匹配")
        # else:
        print("!!!!!库存变化记录未匹配")

    if fix_mode:
        r_map = {}
        for r in records:
            r_map[r.id] = r
        # 赋值
        for id in used_out_order:
            o = used_out_order[id]
            r = r_map[id]
            if not r.order_id:
                r.ctime = o.create_time
                r.order_id = o.order_id
                session.commit()
                print("库存减少修复", r.id, r.total_stock_in, o.order_id)

        for id in cancel_or_close_order:
            o = cancel_or_close_order[id]
            r = r_map[id]
            if not r.order_id:
                r.ctime = o.create_time
                r.order_id = o.order_id
                session.commit()
                print("库存回滚修复", r.id, r.total_stock_in, o.order_id)

    print(
        cur_date,
        "used_out_order",
        len(used_out_order),
        "cancel num",
        len(cancel_or_close_order),
    )

    print(
        f"{cur_date} 订单数:",
        len(related_os),
        "临时出库:",
        tmp_stock_out,
        "临时入库:",
        tmp_stock_in,
    )

    session.close()
    return True


if __name__ == "__main__":
    from optparse import OptionParser

    parser = OptionParser(usage="""检查库存""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    parser.add_option(
        "-s", "--start", dest="start", help="start date", default="2024-04-01"
    )
    parser.add_option("-o", "--operation", dest="operation", help="operation")

    options, args = parser.parse_args()

    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    if options.operation == "fix_stock":
        fix_stock(options.start, verbose=options.verbose)
    if options.operation == "check":
        check(options.start, verbose=options.verbose)
    if options.operation == "check_valid":
        check_valid()
        # check_daily_stock_sum()
