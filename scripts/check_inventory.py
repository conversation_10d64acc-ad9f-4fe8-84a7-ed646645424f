import csv
import datetime
from decimal import Decimal
import json
import logging
import os

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    SKU_DEFAULT,
    AfterSales,
    AfterSalesStatus,
    Cart,
    InventoryTransaction,
    Order,
    Product,
    Sku,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    all_products,
    format_date,
    get_cart_sku_id,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    write_csv,
)

session = get_session(read_only=False)


def daily_query_order(day="2024-05-06", pid=CD2_PID, sku=SKU_DEFAULT):
    start = datetime.datetime.strptime(day, "%Y-%m-%d")
    end = start + datetime.timedelta(days=1)
    return query_order(start, end, pid, sku)


def query_order(start, end, pid=CD2_PID, sku=SKU_DEFAULT):
    o_list = Order.query_range(session, start, end)
    order_items = [Cart.query(session, o.cart_id, o.order_id) for o in o_list]
    # 展开 order_items
    items = [item for sublist in order_items for item in sublist]
    p_num = sum([get_certain_cart_sku_num(cart, pid, sku=sku) for cart in items])

    sku_ids = [get_cart_sku_id(cart, pid, sku=sku) for cart in items]

    print(format_date(start), "订单总数", len(o_list))
    print(format_date(start), "商品总数", p_num)


def query_tmp_stock(
    day="2024-05-06", pid=CD2_PID, sku_unique=CD2_SKU_UNIQUE, verbose=False
):
    q = day.replace("-", "")
    summary = TmpStockSummary.query(session, q, pid, sku_unique)
    if not summary:
        # print(f"{day} 库存无变化")
        # return []
        pass

    records = TmpStock.queryAll(session, q, pid, sku_unique)
    record_in = sum([r.total_stock_in for r in records])
    record_out = sum([r.total_stock_out for r in records])
    if verbose:
        if summary:
            print(
                f"{day}, 出库：{summary.total_stock_out}, 入库：{summary.total_stock_in}，库存调整：{summary.inventory_adjustments}, 同步: {summary.synced}"
            )
        print(f"{day} record_in: {record_in}, record_out: {record_out}")
    return records
    # for r in records:
    #     print("out", r.total_stock_out, r.summary_date, r.spu_id, r.sku_id)


def query_month(month="2024-05"):
    # 获取月份起始
    start = datetime.datetime.strptime(month, "%Y-%m")
    # 获取月份
    month = start.month
    # 获取下月
    end = start.replace(month=month + 1)
    for i in range(1, 32):
        day = start + datetime.timedelta(days=i)
        if day >= end:
            break
        d = day.strftime("%Y-%m-%d")
        daily_query_order(d)
        query_tmp_stock(d)
        print("*" * 30)


def fix_stock(fix_mode=False):
    """库存基准到
    2024-05-06 15:00:00
    05-06 下单时间：2024-05-06 15:01:45
    2024-05-05 03:00"""
    """2024-05-06 14:44:05"""

    ps = all_products(session, verbose=False)

    # 05-06 3:00 到 05-10 卖出的商品
    check_pids = [
        642,  # 二专 5014，在05-05日是库存12799，作为基准线
        647,  # 薄薄红 1 无销量
        646,  # LP-苍蝇乐队《宿寺：苍蝇》内含CD： 黑胶+唱片袋 2件(06,07各一件)、黑胶 1件(0506)
        656,  # 铁盒 2 2024-05-06  已确认
        657,  # 苍蝇海报 1 2024-05-06 已确认
        641,  # 苍蝇拨片 1，从04-22开始，卖到05-23，15个
    ]
    for p in ps:
        # if p.id not in check_pids:
        #     continue
        if p.id == 645:
            continue
        print("商品：", p.id, p.name)
        for sku in p.sku_list:
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            # today = "2024-05-08"
            cur_date = today
            # end_day = "2024-04-30"
            # today = "2024-05-08"
            end_day = "2024-03-31"
            # 日期	商品信息	SKU	库存余量 当时销量
            # 期末库存
            # !! 需要注意record 更新的时间是否已经同步过临时库存
            # 库存同步原先在3点12，mysql在3点08
            # Dump completed on 2024-05-05  3:08:07
            # 同步的是前一天的库存
            record = StockRecord.query(session, p.id, sku.id)
            rows = []

            last_stock = record.stock  # 库存
            last_sales = sku.sales  # 销量

            first_sale = False
            while cur_date != end_day:
                records = query_tmp_stock(cur_date, p.id, sku.unique, verbose=False)

                next_day = (
                    datetime.datetime.strptime(cur_date, "%Y-%m-%d")
                    + datetime.timedelta(days=1)
                ).strftime("%Y-%m-%d")

                has_adjust = len(records)
                if not has_adjust:
                    d = datetime.datetime.strptime(cur_date, "%Y-%m-%d")
                    cur_date = d - datetime.timedelta(days=1)
                    cur_date = cur_date.strftime("%Y-%m-%d")
                    continue

                o_list = Order.query_range(session, cur_date, next_day)
                related_os = []
                for o in o_list:
                    items = Cart.query(session, o.cart_id, o.order_id)
                    num = sum(
                        [
                            get_certain_cart_sku_num(c, p.id, verbose=False)
                            for c in items
                        ]
                    )
                    o.items = items
                    if num > 0:
                        related_os.append(o)
                for o in related_os:
                    print(o)

                tmp_stock_out = sum([r.total_stock_out for r in records])
                tmp_stock_in = sum([r.total_stock_in for r in records])

                o_p_map = {}
                o_p_refund_map = {}
                for o in related_os:
                    num = sum(get_certain_cart_sku_num(c, p.id) for c in o.items)
                    o_p_map[o.order_id] = num
                    num = sum(
                        get_certain_cart_sku_refund_num(o, c, p.id) for c in o.items
                    )
                    o_p_refund_map[o.order_id] = num

                used_out_order = {}
                cancel_or_close_order = {}
                for r in records:
                    if r.total_stock_out > 0:
                        # 找到对应数量的支付订单
                        vs = used_out_order.values()
                        used_out_order_id_set = set([v.order_id for v in vs])
                        for o in related_os:
                            if o.order_id in used_out_order_id_set:
                                continue
                            num = o_p_map[o.order_id]
                            if num == r.total_stock_out:
                                used_out_order[r.id] = o
                                used_out_order_id_set.add(o.order_id)
                                break
                        if r.id not in used_out_order:
                            print("未找到对应的支付订单", r.id, r.total_stock_out)

                cancel_or_close_order_list = [
                    o
                    for o in related_os
                    if (o.cancel_status != 0 or o.refund_status == 2)
                ]
                for r in records:
                    if r.total_stock_in > 0:
                        # 退款
                        vs = cancel_or_close_order.values()
                        cancel_order_id_set = set([v.order_id for v in vs])
                        for o in cancel_or_close_order_list:
                            if o.order_id in cancel_order_id_set:
                                continue
                            num = o_p_map[o.order_id]
                            if o.refund_status == 2:
                                # 处理部分退款
                                is_all_after_sales = False
                                items = o.items
                                if len(items) == 1:
                                    is_all_after_sales = True
                                else:
                                    is_after_sales_count = sum(
                                        [cart.is_after_sales for cart in items]
                                    )
                                    if is_after_sales_count == 0:
                                        is_all_after_sales = True
                                if is_all_after_sales:
                                    if num == r.total_stock_in:
                                        cancel_or_close_order[r.id] = o
                                        used_out_order_id_set.add(o.order_id)
                                        break
                                else:
                                    num = o_p_refund_map[o.order_id]
                                    if num == r.total_stock_in:
                                        cancel_or_close_order[r.id] = o
                                        used_out_order_id_set.add(o.order_id)
                                        break
                            else:
                                if num == r.total_stock_in:
                                    cancel_or_close_order[r.id] = o
                                    break
                        if r.id not in cancel_or_close_order:
                            print("未找到对应的退款订单", r.id, r.total_stock_in)

                if len(records) != len(used_out_order) + len(cancel_or_close_order):
                    # 查询未匹配的库存变化记录
                    vs = used_out_order.values()
                    used_out_order_id_set = set([v.order_id for v in vs])
                    vs = cancel_or_close_order.values()
                    cancel_order_id_set = set([v.order_id for v in vs])
                    for o in related_os:
                        if o.order_id in used_out_order_id_set:
                            continue
                        if o.order_id in cancel_order_id_set:
                            continue
                        print("未匹配订单", o.order_id, o.status, o.cancel_status)
                    print(
                        "数量不匹配",
                        len(records),
                        len(used_out_order),
                        len(cancel_or_close_order),
                    )
                    if fix_mode:
                        raise Exception("库存变化记录未匹配")
                    else:
                        print("库存变化记录未匹配")

                if fix_mode:
                    # 赋值
                    for id in used_out_order:
                        o = used_out_order[id]
                        r = [r for r in records if r.id == id][0]
                        r.ctime = o.create_time
                        r.order_id = o.order_id
                        # session.commit()
                        print("库存减少修复", r.id, r.total_stock_in, o.order_id)

                    for id in cancel_or_close_order:
                        o = cancel_or_close_order[id]
                        r = [r for r in records if r.id == id][0]
                        r.ctime = o.create_time
                        r.order_id = o.order_id
                        # session.commit()
                        print("库存回滚修复", r.id, r.total_stock_in, o.order_id)

                print(
                    "used_out_order",
                    len(used_out_order),
                    "cancel num",
                    len(cancel_or_close_order),
                )

                print(
                    f"{cur_date} 订单数:",
                    len(related_os),
                    "临时出库:",
                    tmp_stock_out,
                    "临时入库:",
                    tmp_stock_in,
                )

                if last_sales <= 0:
                    first_sale = True

                if today == cur_date:
                    # 当日要加
                    last_stock += sum([r.inventory_adjustments for r in records])
                    last_sales -= sum([r.inventory_adjustments for r in records])

                    print(
                        f"{cur_date} {sku.sku} 日终库存:",
                        last_stock,
                        "记录数:",
                        len(records),
                        "日终累积销量:",
                        last_sales,
                        "当前销售额:",
                        record.stock,
                    )
                else:
                    # 其他日减
                    last_stock -= sum([r.inventory_adjustments for r in records])
                    last_sales += sum([r.inventory_adjustments for r in records])

                    print(
                        f"{cur_date} {sku.sku} 日终库存:",
                        last_stock,
                        "记录数:",
                        len(records),
                        "日终累积销量:",
                        last_sales,
                    )

                # 日期，商品信息，SKU，库存余量，当时销量
                row = [cur_date, p.name, sku.sku, last_stock, last_sales]
                rows.append(row)

                d = datetime.datetime.strptime(cur_date, "%Y-%m-%d")
                cur_date = d - datetime.timedelta(days=1)
                cur_date = cur_date.strftime("%Y-%m-%d")
                if first_sale:
                    break

            print("*" * 30)

            # 输出csv
            f_path = os.path.join(
                PROJ_DIR, "scripts", "stock", f"{p.id} {p.name}_{sku.sku}.csv"
            )
            write_csv(f_path, rows)

        print("=" * 30)
        print("=" * 30)
        print("=" * 30)


def check_tmp_inventory(start, end, verbose=False):
    if not start or not end:
        print("请输入开始结束时间")
        return False

    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    # 前两天
    cur_valid = now - datetime.timedelta(days=1)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=0)

    if start <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("start 非法")
        return False
    if end <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("end 非法:", end, "valid:", cur_valid.strftime("%Y-%m-%d"))
        return False

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date
    # 商品id-sku组成的键，值为净销售数量
    total_out_map = {}
    total_in_map = {}
    sku_map = {}
    while cur_date < end_date:
        cur_date_str = cur_date.strftime("%Y%m%d")

        daily_out_map = {}
        daily_in_map = {}
        tmp_record_list = TmpStock.query_by_date(session, cur_date_str)
        for record in tmp_record_list:
            product_id = record.spu_id
            sku_unique = record.sku_unique
            order_id = record.order_id
            sku_key = f"{product_id}-{sku_unique}"
            sku = sku_map.get(sku_key)
            if not sku:
                sku = Sku.get_by_unique(session, product_id, sku_unique)
                sku_map[sku_key] = sku

            key = f"{product_id}--{sku.sku}"

            daily_out_map[key] = daily_out_map.get(key, 0) + record.total_stock_out
            daily_in_map[key] = daily_in_map.get(key, 0) + record.total_stock_in

            total_in_map[key] = total_in_map.get(key, 0) + record.total_stock_in
            total_out_map[key] = total_out_map.get(key, 0) + record.total_stock_out

        # 1. 获取当日付款的订单
        s = cur_date
        e = cur_date + datetime.timedelta(days=1)
        paid_order_list = Order.query_by_pay_time_range(session, s, e)
        # 2. 当日退款打款的订单
        refund_list = AfterSalesStatus.query(session, 3, s, e)
        cs_list = [AfterSales.get(session, s.sales_id) for s in refund_list]
        refund_order_id_list = [cs.order_id for cs in cs_list]
        refund_order_list = [
            Order.query(session, order_id) for order_id in refund_order_id_list
        ]

        # 3. 求出对应的货品的库存变化
        paid_out_map = {}
        paid_in_map = {}
        for order in paid_order_list:
            cart_list = Cart.query(session, order.cart_id, order.order_id)
            for cart in cart_list:
                product_id = cart.product_id
                cart_info = json.loads(cart.cart_info)
                product_info = cart_info.get("productInfo")
                name = product_info.get("storeName")
                attr = product_info.get("attrInfo")
                sku_unique = attr.get("unique")
                num = Decimal(int(cart_info.get("cartNum")))
                sku = Sku.get_by_unique(session, product_id, sku_unique)
                key = f"{product_id}--{sku.sku}"
                paid_out_map[key] = paid_out_map.get(key, 0) + num

        for order in refund_order_list:
            cart_list = Cart.query(session, order.cart_id, order.order_id)
            is_all_after_sales = False
            if len(cart_list) == 1:
                is_all_after_sales = True
            else:
                is_after_sales_count = sum([cart.is_after_sales for cart in cart_list])
                if is_after_sales_count == 0:
                    is_all_after_sales = True
            for cart in cart_list:
                product_id = cart.product_id
                cart_info = json.loads(cart.cart_info)
                product_info = cart_info.get("productInfo")
                name = product_info.get("storeName")
                attr = product_info.get("attrInfo")
                sku_unique = attr.get("unique")
                num = int(cart_info.get("refundNum")) or int(cart_info.get("cartNum"))
                if cart.is_after_sales or is_all_after_sales:
                    sku = Sku.get_by_unique(session, product_id, sku_unique)
                    key = f"{product_id}--{sku.sku}"
                    paid_in_map[key] = paid_in_map.get(key, 0) + num

        # 日汇总输出
        if daily_out_map or daily_in_map:
            if verbose:
                print("*" * 80)
                print(
                    f"{cur_date_str} 付款订单数:",
                    len(paid_order_list),
                    "退款订单数:",
                    len(refund_order_list),
                )
                key_set = set(daily_out_map.keys())
                key_set.update(daily_in_map.keys())

                for key in sorted(key_set):
                    product_id, sku = key.split("--")
                    p = Product.get(session, product_id)
                    sku = Sku.get_by_sku(session, product_id, sku)
                    print(
                        f"{cur_date_str} {p.name} - {sku.sku} 净出库: {daily_out_map.get(key, 0)-daily_in_map.get(key, 0)}, 出库: {daily_out_map.get(key, 0)}, 入库: {daily_in_map.get(key, 0)}, 付款出库: {paid_out_map.get(key, 0)}, 退款入库: {paid_in_map.get(key, 0)}"
                    )
                print("*" * 80)

        cur_date = cur_date + datetime.timedelta(days=1)

    print("=" * 80)
    print("=" * 80)
    print("=" * 80)
    print("总计")
    key_set = set()
    for key in total_out_map:
        key_set.add(key)
    for key in total_in_map:
        key_set.add(key)
    for key in sorted(key_set):
        total_out_num = total_out_map.get(key, 0)
        total_in_num = total_in_map.get(key, 0)
        product_id, sku_unique = key.split("--")
        p = Product.get(session, product_id)
        sku = Sku.get_by_sku(session, product_id, sku_unique)
        if not sku:
            print("sku not found", key, p.id, p.name)
            continue
        name = "%s - %s" % (p.name, sku.sku)
        print(
            f"{name} 出库数量: ",
            total_out_num,
            "入库数量: ",
            total_in_num,
            "净出库:",
            total_out_num - total_in_num,
        )
    print("=" * 80)
    print("=" * 80)
    print("=" * 80)


def show_sku_stock(verbose=False):
    """商品维度"""
    ps = all_products(session, verbose=False)

    # 05-06 3:00 到 05-10 卖出的商品
    check_pids = [
        642,  # 二专 5014，在05-05日是库存12799，作为基准线
        647,  # 薄薄红 1 无销量
        646,  # LP-苍蝇乐队《宿寺：苍蝇》内含CD： 黑胶+唱片袋 2件(06,07各一件)、黑胶 1件(0506)
        656,  # 铁盒 2 2024-05-06  已确认
        657,  # 苍蝇海报 1 2024-05-06 已确认
        641,  # 苍蝇拨片 1，从04-22开始，卖到05-23，15个
    ]
    for p in ps:
        # if p.id not in check_pids:
        #     continue
        if p.id == 645:
            continue
        for sku in p.sku_list:
            print("商品：", p.id, p.name, sku.sku)
            today = datetime.datetime.now().strftime("%Y-%m-%d")
            # today = "2024-05-08"
            cur_date = today
            # end_day = "2024-04-30"
            # today = "2024-05-08"
            end_day = "2024-03-31"
            # 日期	商品信息	SKU	库存余量 当时销量
            # 期末库存
            # !! 需要注意record 更新的时间是否已经同步过临时库存
            # 库存同步原先在3点12，mysql在3点08
            # Dump completed on 2024-05-05  3:08:07
            # 同步的是前一天的库存
            record = StockRecord.query(session, p.id, sku.id)
            rows = []

            daily_start_stock = record.stock  # 库存
            last_sales = sku.sales  # 销量

            first_sale = False
            while cur_date != end_day:
                records = query_tmp_stock(cur_date, p.id, sku.unique, verbose=False)

                has_adjust = len(records)

                # 查询手工操作
                record = StockRecord.query(session, p.id, sku.id)
                next_date = datetime.datetime.strptime(
                    cur_date, "%Y-%m-%d"
                ) + datetime.timedelta(days=1)
                manual_trx_list = InventoryTransaction.query_by_range(
                    session, record.id, cur_date, next_date
                )
                manual_trx_list = [r for r in manual_trx_list if r.operator_id != 0]
                manual_stock_out = 0
                manual_stock_in = 0
                manual_adjust = 0
                if manual_trx_list:
                    manual_stock_out += sum(
                        [
                            -r.quantity
                            for r in manual_trx_list
                            if r.transaction_type == "出库"
                            or r.transaction_type == "报损"
                        ]
                    )
                    manual_stock_in += sum(
                        [
                            r.quantity
                            for r in manual_trx_list
                            if r.transaction_type == "入库"
                        ]
                    )
                    manual_adjust += manual_stock_in - manual_stock_out
                    if manual_adjust != 0:
                        has_adjust = True
                        print(
                            f"{cur_date} 手工操作",
                            manual_adjust,
                            "out",
                            manual_stock_out,
                            "in",
                            manual_stock_in,
                        )

                if not has_adjust:
                    d = datetime.datetime.strptime(cur_date, "%Y-%m-%d")
                    cur_date = d - datetime.timedelta(days=1)
                    cur_date = cur_date.strftime("%Y-%m-%d")
                    continue

                tmp_stock_out = sum([r.total_stock_out for r in records])
                tmp_stock_in = sum([r.total_stock_in for r in records])
                daily_adjust = sum([r.inventory_adjustments for r in records])
                assert daily_adjust == tmp_stock_in - tmp_stock_out

                if last_sales <= 0:
                    first_sale = True

                if today == cur_date:
                    # 当日，尚未同步要加
                    daily_start_stock += sum([r.inventory_adjustments for r in records])
                    last_sales -= sum([r.inventory_adjustments for r in records])

                    print(
                        f"{cur_date} {sku.sku} 日终库存:",
                        daily_start_stock,
                        "记录数:",
                        len(records),
                        "日终累积销量:",
                        last_sales,
                        "当前销售额:",
                        record.stock,
                    )
                else:
                    daily_end_stock = daily_start_stock
                    # 其他日，已同步，需要减
                    daily_start_stock -= sum([r.inventory_adjustments for r in records])
                    last_sales += sum([r.inventory_adjustments for r in records])

                    daily_start_stock -= manual_adjust
                    last_sales += manual_adjust

                    print(
                        f"{cur_date} {sku.sku}",
                        "日终库存",
                        daily_end_stock,
                        "日初库存:",
                        daily_start_stock,
                        "记录数:",
                        len(records),
                        "日终累积销量:",
                        last_sales,
                    )

                # 日期，商品信息，SKU，库存余量，当时销量
                row = [cur_date, p.name, sku.sku, daily_start_stock, last_sales]
                rows.append(row)

                d = datetime.datetime.strptime(cur_date, "%Y-%m-%d")
                cur_date = d - datetime.timedelta(days=1)
                cur_date = cur_date.strftime("%Y-%m-%d")
                if first_sale:
                    break

            print("*" * 30)

            # 输出csv
            f_path = os.path.join(
                PROJ_DIR, "scripts", "stock", f"{p.id} {p.name}_{sku.sku}.csv"
            )
            write_csv(f_path, rows)

        print("=" * 30)
        print("=" * 30)
        print("=" * 30)


def gen_stock_report_by_date(cur_date, end_day, checked_pid=0, checked_sku=None, verbose=False):
    """商品维度
    !!! 顺序是倒着的
    """

    ps = all_products(session, verbose=False)

    # today = datetime.datetime.now().strftime("%Y-%m-%d")
    # today = "2024-05-08"
    # cur_date = today
    # end_day = "2024-04-30"
    # today = "2024-05-08"
    # end_day = "2024-03-31"
    # 日期	商品信息	SKU	库存余量 当时销量
    # 期末库存
    # !! 需要注意record 更新的时间是否已经同步过临时库存
    # 库存同步原先在3点12，mysql在3点08
    # Dump completed on 2024-05-05  3:08:07
    # 同步的是前一天的库存

    while cur_date <= end_day:
        # 05-06 3:00 到 05-10 卖出的商品
        rows = []
        rows.append(["日期", "商品信息", "SKU", "类型", "数量", "订单号", "备注"])
        # check_pids = [
        #     642,  # 二专 5014，在05-05日是库存12799，作为基准线
        #     647,  # 薄薄红 1 无销量
        #     646,  # LP-苍蝇乐队《宿寺：苍蝇》内含CD： 黑胶+唱片袋 2件(06,07各一件)、黑胶 1件(0506)
        #     656,  # 铁盒 2 2024-05-06  已确认
        #     657,  # 苍蝇海报 1 2024-05-06 已确认
        #     641,  # 苍蝇拨片 1，从04-22开始，卖到05-23，15个
        # ]
        for p in ps:
            # if p.id not in check_pids:
            #     continue
            if checked_pid:
                if p.id != int(checked_pid):
                    continue

            for sku in p.sku_list:
                if checked_sku:
                    if sku.sku != checked_sku:
                        continue

                record = StockRecord.query(session, p.id, sku.id)

                if verbose:
                    print(f"{cur_date}商品：", p.id, p.name, sku.sku)

                # 获取对应sku的库存变化记录
                records = query_tmp_stock(cur_date, p.id, sku.unique, verbose=False)
                out_map = {}
                in_map = {}
                for r in records:
                    if r.total_stock_out > 0:
                        out_map[r.order_id] = r.total_stock_out
                    elif r.total_stock_in > 0:
                        in_map[r.order_id] = r.total_stock_in

                for r in records:
                    if out_map.get(r.order_id) and in_map.get(r.order_id):
                        # 某个订单出入库库存数量相等，则跳过
                        if out_map[r.order_id] == in_map[r.order_id]:
                            o = Order.query(session, r.order_id)
                            if o.paid:
                                # 已支付的订单，继续处理
                                pass
                            else:
                                # 出库入库相等，不处理
                                if verbose:
                                    print("当日进行了等量出入库, 跳过", r.order_id)
                                continue
                    adjust = r.inventory_adjustments
                    kind = r.inventory_adjustments > 0 and "入库" or "出库"
                    remark = "系统生成"
                    row = [
                        cur_date,
                        p.name,
                        sku.sku,
                        kind,
                        adjust,
                        "`" + r.order_id,
                        remark,
                    ]
                    rows.append(row)

                next_date = datetime.datetime.strptime(
                    cur_date, "%Y-%m-%d"
                ) + datetime.timedelta(days=1)

                cur_date_init_time = datetime.datetime.strptime(cur_date, "%Y-%m-%d")

                # 库存交易
                manual_trx_list = InventoryTransaction.query_by_range(
                    session, record.id, cur_date, next_date.strftime("%Y-%m-%d")
                )
                # manual_trx_list = [r for r in manual_trx_list if r.operator_id != 0]
                manual_trx_list = [
                    r
                    for r in manual_trx_list
                    if r.transaction_date >= cur_date_init_time
                    and r.transaction_date < next_date
                ]
                # if record.id == 1000045:
                #     print(
                #         f"查询库存交易：{record.id} {cur_date_init_time} {next_date}, len: {len(manual_trx_list)}"
                #     )

                for r in manual_trx_list:
                    # if r.transaction_date < cur_date_init_time or r.transaction_date >= next_date:
                    #     continue
                    if "根据订单销售系统自动" in (r.remarks or ""):
                        # if record.id == 1000045:
                        #     print("跳过自动", r.transaction_id, r.inventory_id, 'transaction_date', r.transaction_date)
                        continue
                    adjust = r.quantity
                    kind = r.transaction_type
                    remark = r.remarks
                    row = [cur_date, p.name, sku.sku, kind, adjust, "手动", remark]
                    rows.append(row)

        # 输出csv
        if checked_pid and checked_sku:
            # 直接输出控制台结果
            print(f"\n{checked_pid} {checked_sku} 库存变动明细:")
            print("-" * 80)
            print("{:<12} {:<30} {:<10} {:<6} {:<8} {:<15} {:<20}".format(
                "日期", "商品信息", "SKU", "类型", "数量", "订单号", "备注"
            ))
            print("-" * 80)

            # 跳过表头，添加安全转换
            for row in rows[1:]:
                # 确保所有值都是字符串，并处理 None 值
                formatted_row = [
                    str(row[0] or ''),  # 日期
                    str(row[1] or '')[:28],  # 商品信息
                    str(row[2] or ''),  # SKU
                    str(row[3] or ''),  # 类型
                    str(row[4] or ''),  # 数量
                    str(row[5] or ''),  # 订单号
                    str(row[6] or '')   # 备注
                ]
                
                print("{:<12} {:<30} {:<10} {:<6} {:<8} {:<15} {:<20}".format(*formatted_row))
            print("-" * 80)
            return

        report_base_path = "/data/wechat_pay/report"
        f_path = report_base_path + f"/库存数据{cur_date}.csv"
        if os.path.exists(f_path):
            os.remove(f_path)
        if len(rows) > 1:
            write_csv(f_path, rows)

        d = datetime.datetime.strptime(cur_date, "%Y-%m-%d")
        cur_date = d + datetime.timedelta(days=1)
        cur_date = cur_date.strftime("%Y-%m-%d")

        print("库存报表生成完成:", f_path)
        print("*" * 30)


if __name__ == "__main__":
    from optparse import OptionParser

    parser = OptionParser(usage="""检查库存""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    parser.add_option(
        "-s", "--start", dest="start", help="start date", default="2024-04-01"
    )
    # parser.add_option("-e", "--end", dest="end", help="end date", default="2024-05-01")
    parser.add_option("-e", "--end", dest="end", help="end date", default=None)
    parser.add_option("-o", "--operation", dest="operation", help="operation")

    parser.add_option("-p", "--pid", dest="pid", help="商品ID", default=0)
    parser.add_option("-k", "--sku", dest="sku", help="SKU", default=None)

    options, args = parser.parse_args()

    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    end = options.end
    if not end:
        end = options.start

    if options.operation == "show_sku_stock":
        # fix_stock()
        # show_sku_stock(options.verbose)
        '''python check_inventory.py -o show_sku_stock -v -s 2024-12-16 -p 642 -k 默认'''
        # 超售检查
        '''python check_inventory.py -o show_sku_stock -v -s 2025-01-06 -p 664 -k 默认'''
        gen_stock_report_by_date(options.start, end, verbose=options.verbose, checked_pid=options.pid, checked_sku=options.sku)
    elif options.operation == "check_tmp_inventory":
        check_tmp_inventory(options.start, end, options.verbose)

    session.close()
