import csv
import datetime
import json
import logging
import os

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    Order,
    get_session,
    User,
)


def fix(dry_run=True):
    print("dry_run: ", dry_run)
    session = get_session(read_only=dry_run)

    page = 1
    page_size = 1000
    count = 0
    fix = 0

    # 分页获取用户到结束
    while True:
        users = User.query_page(session, page, page_size)
        if not users:
            break
        count += len(users)
        for user in users:
            o_list = Order.query_by_user_id(session, user.uid)
            o_list = (o for o in o_list if o.paid)

            # 确认收货，获得积分。
            # 用户从已发货进入已完成时才会有积分
            integral = sum(
                [o.total_price - o.refund_price for o in o_list if o.status == 2]
            )

            # 退款中的
            integral += sum(
                [
                    o.total_price - o.refund_price
                    for o in o_list
                    if o.status == -1 and o.refund_status == 2
                ]
            )
            if user.integral != integral:
                print(
                    "user: ",
                    user.uid,
                    "uname:",
                    user.username,
                    "realname:",
                    user.real_name,
                    "待修复积分: ",
                    user.integral,
                    ", 实际应得积分：",
                    integral,
                )
                fix += 1
                if not dry_run:
                    user.integral = integral
                    session.commit()
                    print("积分修复完成:", integral)

        print("page: ", page)
        print("=" * 30)
        page += 1

    print("总计用户数: ", count)
    print("共修复积分: ", fix)
    session.close()


if __name__ == "__main__":
    from optparse import OptionParser, OptionGroup

    parser = OptionParser(usage="""检查积分""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    parser.add_option(
        "-s", "--start", dest="start", help="start date", default="2024-04-01"
    )
    parser.add_option("-e", "--end", dest="end", help="end date", default="2024-05-01")
    parser.add_option("-o", "--operation", dest="operation", help="operation")
    # 创建互斥组
    dry_run_group = OptionGroup(parser, "Dry Run Options")
    dry_run_group.add_option(
        "-d",
        "--dry-run",
        action="store_true",
        dest="dry_run",
        default=True,
        help="dry run",
    )
    dry_run_group.add_option(
        "--no-dry-run",
        action="store_false",
        dest="dry_run",
        help="do not perform a dry run",
    )
    parser.add_option_group(dry_run_group)

    options, args = parser.parse_args()
    print(f"Dry run: {options.dry_run}")

    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    fix(dry_run=options.dry_run)
