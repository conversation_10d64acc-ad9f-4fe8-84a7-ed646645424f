import csv
import datetime
from decimal import Decimal
import json
import logging
import os


# 脚本用途
# 查询售后状态变化


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    SKU_DEFAULT,
    AfterSales,
    AfterSalesStatus,
    Cart,
    Order,
    Product,
    Sku,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    all_products,
    format_date,
    get_cart_sku_id,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    read_csv,
    read_wechat_pay_log,
    write_csv,
)
from check_inventory import query_tmp_stock

session = get_session(read_only=False)

def tmp_check():
    data = '''1787378456225427456 2024-05-07
1787379796544626688 2024-05-07
1787380373815078912 2024-05-08
1787380610495459328 2024-05-08
1787380565498961920 2024-05-07
1787381120803844096 2024-05-18
1787381172490248192 2024-05-07
1787381296075415552 2024-05-07
1787381644173291520 2024-05-07
1787381814101323776 2024-05-08'''
    lines = data.split("\n")
    for line in lines:
        order_id, date = line.split()
        o = Order.query(session, order_id)
        items = Cart.query(session, o.cart_id, o.order_id)
        num = sum([get_certain_cart_sku_refund_num(o, c, 642, verbose=False) for c in items])
        print(o.order_id, o.create_time, o.finish_time, 'refund num', num)

def check():
    # s = "2024-05-06"
    # e = "2024-05-07"

    s = "2024-05-07"
    e = "2024-05-08"

    # s = "2024-04-23"
    # e = "2024-04-24"

    # s = "2024-03-25"
    # e = "2024-03-26"

    # s = "2024-03-28"
    # e = "2024-03-29"
    # s = "2024-03-29"
    # e = "2024-03-30"
    base_path = "/data/wechat_pay/raw"
    bill_csv_path = base_path + f"/微信支付账单{s}.csv"
    rows = read_wechat_pay_log(bill_csv_path, verbose=False)
    rows = sorted(rows, key=lambda x: x[0])
    succ_order_list = []
    refund_order_list = []
    succ_product_num = 0
    refund_product_num = 0
    pay_or_refund_in_today_map = {}
    for row in rows:
        order_id = row[6]
        trade_status = row[9]
        if trade_status == "REFUND":
            o = Order.query(session, order_id)
            items = Cart.query(session, o.cart_id, o.order_id)
            num = sum(
                [
                    get_certain_cart_sku_refund_num(o, c, 642, verbose=False)
                    for c in items
                ]
            )
            if num:
                refund_order_list.append(order_id)
                refund_product_num += num
                o.items = items
                pay_or_refund_in_today_map[order_id] = o
            else:
                # print("退款订单未找到商品", order_id, o.create_time, 'ptime', o.pay_time, 'ftime', o.finish_time)
                pass
        elif trade_status == "SUCCESS":
            o = Order.query(session, order_id)
            items = Cart.query(session, o.cart_id, o.order_id)
            num = sum([get_certain_cart_sku_num(c, 642, verbose=False) for c in items])
            if num:
                o.items = items
                pay_or_refund_in_today_map[order_id] = o
                succ_order_list.append(order_id)
                succ_product_num += num
            else:
                # print("支付订单未找到商品", order_id, o.create_time, 'ptime', o.pay_time, 'ftime', o.finish_time)
                pass

    # 查询当日的出入库记录，看是否有遗漏
    sku_unique = CD2_SKU_UNIQUE
    records = query_tmp_stock(s, 642, sku_unique, verbose=False)
    summary = sum([r.inventory_adjustments for r in records])
    stock_out = sum([r.total_stock_out for r in records])
    stock_in = sum([r.total_stock_in for r in records])
    print("cal summary stock adjust", summary, stock_out, stock_in)

    # summary2 = sum([r.inventory_adjustments for r in records if r.order_id])
    # print("summary stock adjust with order id only", summary2)

    query_day = s.replace("-", "")
    summary = TmpStockSummary.query(session, query_day, CD2_PID, CD2_SKU_UNIQUE)
    print("db summary stock adjust", summary.inventory_adjustments)

    # 05-06
    # 1787382788081627136 360退款
    # 1787385277854035968 360退款

    print(
        "售出订单:",
        len(succ_order_list),
        "数量:",
        succ_product_num,
        "退款订单：",
        len(refund_order_list),
        "数量:",
        refund_product_num,
        "净销售商品数量:",
        succ_product_num - refund_product_num,
        "库存变化记录数:",
        len(records),
    )
    r_map = {}
    for r in records:
        oid = r.order_id
        if oid not in r_map:
            r_map[oid] = []
        r_map[oid].append(r)

    adjust = 0
    stock_out = 0
    stock_in = 0
    for oid in succ_order_list:
        rs = r_map.get(oid, [])
        rs = [r for r in rs if r.total_stock_out > 0]
        o = Order.query(session, oid)
        items = Cart.query(session, o.cart_id, o.order_id)
        if len(rs) == 0:
            print(
                "当日微信支付订单未出库",
                oid,
                o.create_time,
                "ptime",
                o.pay_time,
                "ftime",
                o.finish_time,
            )
        else:
            r = rs[0]
            adjust += r.inventory_adjustments
            stock_out += r.total_stock_out
            stock_in += r.total_stock_in
            if r.inventory_adjustments == r.total_stock_in - r.total_stock_out:
                pass
            else:
                print("库存变化异常", r.id, r)
            num = sum([get_certain_cart_sku_num(c, 642, verbose=False) for c in items])
            r = rs[0]
            if r.total_stock_out != num:
                print("出库数量不一致", oid, r.total_stock_out, "订单二转数量:", num)

    print(f"{s}付款订单库存变化", adjust, stock_out, stock_in)
    print("===")

    adjust = 0
    stock_out = 0
    stock_in = 0
    for oid in refund_order_list:
        rs = r_map.get(oid, [])
        rs = [r for r in rs if r.total_stock_in > 0]
        o = Order.query(session, oid)
        items = Cart.query(session, o.cart_id, o.order_id)
        if len(rs) == 0:
            print(
                "当日微信支付退款未回滚",
                oid,
                o.create_time,
                "ptime",
                o.pay_time,
                "ftime",
                o.finish_time,
            )
        else:
            r = rs[0]
            adjust += r.inventory_adjustments
            stock_out += r.total_stock_out
            stock_in += r.total_stock_in
            if r.inventory_adjustments == r.total_stock_in - r.total_stock_out:
                pass
            else:
                print("库存变化异常", r.id, r)
            num = sum(
                [
                    get_certain_cart_sku_refund_num(o, c, 642, verbose=False)
                    for c in items
                ]
            )
            r = rs[0]
            if r.total_stock_in != num:
                print("回滚数量不一致", oid, r.total_stock_in, "退款二转数量", num)

    print(f"{s}退款订单库存变化", adjust, stock_out, stock_in)

    not_today_pay_or_refund_order_map = {}
    for r in records:
        o = pay_or_refund_in_today_map.get(r.order_id)
        if not o:
            o = Order.query(session, r.order_id)
            items = Cart.query(session, o.cart_id, o.order_id)
            if o.paid:
                print("异常订单", r.order_id, r.total_stock_out, r.total_stock_in)
            num = sum([get_certain_cart_sku_num(c, 642, verbose=False) for c in items])
            # print("未支付订单", r.order_id, r.total_stock_out, r.total_stock_in, num)
            unpay_list = not_today_pay_or_refund_order_map.get(r.order_id, [])
            unpay_list.append(r)
            not_today_pay_or_refund_order_map[r.order_id] = unpay_list

    print("不在微信支付账单中的订单数量:", len(not_today_pay_or_refund_order_map))
    adjust = 0
    stock_out = 0
    stock_in = 0
    for oid, rs in not_today_pay_or_refund_order_map.items():
        o = Order.query(session, oid)
        if not o:
            print("订单不存在", oid)
        # if o.is_del:
        #     print("已删除订单", oid, o.create_time, "ptime", o.pay_time, "ftime", o.finish_time)
        if o.refund_status == 2:
            print(
                "已退款订单",
                oid,
                o.create_time,
                "ptime",
                o.pay_time,
                "ftime",
                o.finish_time,
            )
        if o.paid:
            print(
                "已支付订单",
                oid,
                o.create_time,
                "ptime",
                o.pay_time,
                "ftime",
                o.finish_time,
            )
        for r in rs:
            adjust += r.inventory_adjustments
            stock_out += r.total_stock_out
            stock_in += r.total_stock_in
            if r.inventory_adjustments == r.total_stock_in - r.total_stock_out:
                pass
            else:
                print("库存变化异常", r.id, r)
        if len(rs) == 1:
            print(
                "非当日结束",
                oid,
                len(rs),
                o.create_time,
                "ptime",
                o.pay_time,
                "ftime",
                o.finish_time,
            )
        elif len(rs) == 2:
            if not o.finish_time:
                # 没问题
                continue
            if o.finish_time > datetime.datetime.strptime(
                s, "%Y-%m-%d"
            ) + datetime.timedelta(days=1):
                items = Cart.query(session, o.cart_id, o.order_id)
                num = sum(
                    [get_certain_cart_sku_num(c, 642, verbose=False) for c in items]
                )
                # print("未支付订单出入库记录不全", oid, len(rs), o.create_time, "ptime", o.pay_time, "ftime", o.finish_time)
                print(
                    f"订单回滚不应该出现在{s}",
                    oid,
                    len(rs),
                    o.create_time,
                    "ptime",
                    o.pay_time,
                    "ftime",
                    o.finish_time,
                )

    print("非当日付款退款-订单的库存变化", adjust, stock_out, stock_in)

    adjust = 0
    stock_out = 0
    stock_in = 0
    not_refund_today_order_map = {}
    for r in records:
        if r.order_id in succ_order_list and (r.order_id not in refund_order_list):
            adjust += r.inventory_adjustments
            stock_out += r.total_stock_out
            stock_in += r.total_stock_in
            if r.inventory_adjustments > 0:
                o = Order.query(session, r.order_id)
                print(
                    "库存变化异常",
                    r.id,
                    r.order_id,
                    o.create_time,
                    o.pay_time,
                    o.finish_time,
                )

    print("非当日付款未当日退款-订单的库存变化", adjust, stock_out, stock_in)
    return

    s = datetime.datetime.strptime(s, "%Y-%m-%d")
    e = datetime.datetime.strptime(e, "%Y-%m-%d")
    refund_list = AfterSalesStatus.query2(session, s, e)
    for refund in refund_list:
        print(refund.sales_id)
        cs = AfterSales.get(session, refund.sales_id)
        if cs:
            o = Order.query(session, cs.order_id)
            items = Cart.query(session, o.cart_id, o.order_id)
            num = sum([get_certain_cart_sku_num(c, 642, verbose=False) for c in items])
            if num:
                print("售后订单", o, o.is_del)
        else:
            print("售后不存在", refund.sales_id)

    print("===")
    order_list = Order.query_range(session, s, e)
    for o in order_list:
        items = Cart.query(session, o.cart_id, o.order_id)
        num = sum([get_certain_cart_sku_num(c, 642, verbose=False) for c in items])
        if num:
            print(o, o.is_del)

    print("done")


if __name__ == "__main__":
    from optparse import OptionParser

    parser = OptionParser(usage="""检查库存""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    parser.add_option(
        "-s", "--start", dest="start", help="start date", default="2024-04-01"
    )
    parser.add_option("-e", "--end", dest="end", help="end date", default="2024-05-01")
    parser.add_option("-o", "--operation", dest="operation", help="operation")

    options, args = parser.parse_args()

    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    if options.operation == "check":
        check()
        # tmp_check()

    session.close()
