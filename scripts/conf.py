# 读取yml中数据库连接配置
import yaml
import os

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


def get_db_conf():
    with open(PROJ_DIR + "/scripts/conf.yml", "r") as f:
        conf = yaml.safe_load(f)
    # 示例：
    # db: mysql://root:123456@localhost:3306/test
    return conf["db"]
