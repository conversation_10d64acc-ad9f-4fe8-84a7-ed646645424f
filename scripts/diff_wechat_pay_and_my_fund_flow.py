# 脚本用途
# 对比微信支付和我的资金流


# 1. 平台流水资金流csv
# 时间	类型	流水	类型备注	余额	商品	商品sku	数量	业务凭证号
# 2025/1/6 1:03	收入	130	货款收入	19911.17	CD-蛙池《郊游》	默认	1	`1875951236990148608
# 2025/1/6 1:03	支出	-0.78	手续费支出	19910.39	CD-蛙池《郊游》	默认	1	`1875951236990148608
# 2025/1/6 8:20	收入	160	货款收入	20070.39	CTPK-002郊眠寺手工拨片 / 河北墨麒麟	默认	1	`1876061089142185984
# 2025/1/6 8:20	支出	-0.96	手续费支出	20069.43	CTPK-002郊眠寺手工拨片 / 河北墨麒麟	默认	1	`1876061089142185984
# 2025/1/6 9:44	收入	240	货款收入	20309.43	万能青年旅店《冀西南林路行》专辑双面海报	默认	2	`1876082428024500224
# 2025/1/6 9:44	支出	-1.44	手续费支出	20307.99	万能青年旅店《冀西南林路行》专辑双面海报	默认	2	`1876082428024500224
# 2025/1/6 9:44	收入	120	货款收入	20427.99	万能青年旅店《冀西南林路行》专辑双面海报	默认	1	`1876082484647604224
# 2025/1/6 9:44	支出	-0.72	手续费支出	20427.27	万能青年旅店《冀西南林路行》专辑双面海报	默认	1	`1876082484647604224




# 2. 微信流水csv
# 表头：
# 记账时间	微信支付业务单号	资金流水单号	业务名称	业务类型	收支类型	收支金额(元)	账户结余(元)	资金变更提交申请人	备注	业务凭证号
# `2025-01-06 01:03:49	`4200002539202501060554324627	`4200002539202501060554324627	`交易	`交易	`收入	`130.00	`19911.17	`system	`结算总金额 130.00 元;含手续费 0.78 元	`1875951236990148608
# `2025-01-06 01:03:49	`4200002539202501060554324627	`4200002539202501060554324627	`交易	`扣除交易手续费	`支出	`0.78	`19910.39	`system	`结算总金额 130.00 元;含手续费 0.78 元	`1875951236990148608
# `2025-01-06 08:20:11	`4200002540202501068002718079	`4200002540202501068002718079	`交易	`交易	`收入	`160.00	`20070.39	`system	`结算总金额 160.00 元;含手续费 0.96 元	`1876061089142185984
# `2025-01-06 15:55:35	`50300801922025010669258235508	`4200002563202501066099371528	`退款	`退款	`支出	`109.34	`415155.58	`1636528171API	`退款总金额110.00元;含手续费0.66元	`1876175767482675200
# 最后两行汇总：
# 资金流水总笔数	收入笔数	收入金额	支出笔数	支出金额
# `5066.0	`2458.0	`439120.0	`2608.0	`28387.3

import csv
from datetime import datetime
import math


def read_csv(file_path):
    with open(file_path, "r") as f:
        reader = csv.reader(f)
        return list(reader)


def diff_wechat_pay_and_my_fund_flow(platform_fund_flow_csv, wechat_fund_flow_csv):
    # 读取CSV文件
    platform_rows = read_csv(platform_fund_flow_csv)
    wechat_rows = read_csv(wechat_fund_flow_csv)

    print("\n=== 账户结余对账 ===")

    # 按分钟对平台数据进行分组
    platform_by_minute = {}
    for p_row in platform_rows[:]:
        try:
            # 解析时间到分钟级别 (格式: 2025-01-06 23:40:18)
            time_str = p_row[0]
            minute_key = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S").strftime(
                "%Y/%m/%d %H:%M"
            )

            if minute_key not in platform_by_minute:
                platform_by_minute[minute_key] = []
            platform_by_minute[minute_key].append(
                {
                    "balance": float(p_row[4]),
                    "amount": float(p_row[2]),
                    "biz_no": p_row[8].strip("`"),
                    "raw": p_row,
                }
            )
        except (ValueError, IndexError) as e:
            print(f"处理平台数据出错: {p_row}, 错误: {e}")
            continue

    # 按分钟对微信数据进行分组
    wechat_by_minute = {}
    for i, w_row in enumerate(wechat_rows[1:-2]):  # 跳过表头和汇总行
        try:
            # 解析时间到分钟级别 (格式: 2025-01-06 01:03:49)
            time_str = w_row[0].strip("`")
            minute_key = datetime.strptime(time_str, "%Y-%m-%d %H:%M:%S").strftime(
                "%Y/%m/%d %H:%M"
            )

            if minute_key not in wechat_by_minute:
                wechat_by_minute[minute_key] = []
            wechat_by_minute[minute_key].append(
                {
                    "balance": float(w_row[7].strip("`")),
                    "amount": float(w_row[6].strip("`")),
                    "biz_no": w_row[10].strip("`"),
                    "raw": w_row,
                    "line_number": i + 2,  # 添加行号信息
                }
            )
        except (ValueError, IndexError) as e:
            print(f"处理微信数据出错: {w_row}, 错误: {e}")
            continue

    # 对比每分钟的交易
    all_minutes = sorted(set(wechat_by_minute.keys()) | set(platform_by_minute.keys()))

    error_idx = 0

    for i, minute_key in enumerate(all_minutes):
        p_records = []

        # 获取当前分钟及前后相邻分钟的记录
        for offset in [-2, -1, 0, 1, 2, 3]:
            if i + offset >= 0 and i + offset < len(all_minutes):
                adjacent_key = all_minutes[i + offset]
                p_records.extend(platform_by_minute.get(adjacent_key, []))

        if not p_records:
            continue

        # print("\n")
        # print("*" * 100)
        # print(f"\n时间窗口: {minute_key} (包含前后1分钟)")

        # 通过余额匹配交易
        # 创建余额到记录的映射
        p_by_balance = {"{:.2f}".format(rec["balance"]): rec for rec in p_records}

        # 检查未匹配的交易
        w_records = wechat_by_minute.get(minute_key, [])
        if not w_records:
            continue
        w_record = w_records[-1]  # 按照微信最终余额查询即可
        formated_w_balance = "{:.2f}".format(w_record["balance"])
        if formated_w_balance in p_by_balance:
            # print(f"微信端余额 {formated_w_balance} 在平台端存在")
            pass
        else:
            print("\n")
            print("*" * 100)
            print(f"\n时间窗口: {minute_key} (包含前后1分钟), 第{error_idx}个错误")

            print(f"微信端余额 {formated_w_balance} 在平台端不存在")
            print(f"微信记录 (第{w_record['line_number']}行): {w_record['raw']}")
            print(f"当前平台记录余额列表: {list(p_by_balance.keys())}")
            error_idx += 1


if __name__ == "__main__":
    # 平台流水资金流csv
    # platform_fund_flow_csv = "/Users/<USER>/Downloads/2025-02-12 09_22_13-资金流水数据/流水数据2025-01-06.csv"
    platform_fund_flow_csv = "/Users/<USER>/Downloads/流水数据2025-01-06.csv"
    # 微信流水csv
    wechat_fund_flow_csv = (
        "/Users/<USER>/Downloads/1636528171基本账户2025-01-06_2025-01-06.csv"
    )

    diff_wechat_pay_and_my_fund_flow(
        platform_fund_flow_csv=platform_fund_flow_csv,
        wechat_fund_flow_csv=wechat_fund_flow_csv,
    )
