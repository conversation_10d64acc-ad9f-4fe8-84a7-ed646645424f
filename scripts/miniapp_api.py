import requests
import json

# 小程序资金接口
# https://developers.weixin.qq.com/miniprogram/dev/platform-capabilities/business-capabilities/ministore/wxafunds/API/funds/getorderflow.html


def get_order_flow(access_token, biz_type, mchid, funds_flow_id):
    """
    获取订单流水信息
    :param access_token: 微信API接口调用凭证
    :param biz_type: 业务类型，固定填1
    :param mchid: 商户号
    :param funds_flow_id: 流水单号
    :return: 返回获取到的订单流水信息或错误信息
    """
    url = (
        f"https://api.weixin.qq.com/shop/funds/getorderflow?access_token={access_token}"
    )
    headers = {"Content-Type": "application/json"}
    payload = {"biz_type": biz_type, "mchid": mchid, "funds_flow_id": funds_flow_id}
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    if response.status_code == 200:
        return response.json()  # 返回响应的JSON对象
    else:
        return {"error": f"请求失败，状态码：{response.status_code}"}


def scan_order_flow(access_token, biz_type, mchid, page_num, page_size, date):
    """
    扫描订单流水
    :param access_token: 微信API接口调用凭证
    :param biz_type: 业务类型，固定填1
    :param mchid: 商户号
    :param page_num: 页码
    :param page_size: 每页数据大小，必须小于等于100
    :param date: 日期, 格式：20210101
    :return: 返回扫描到的订单流水信息或错误信息
    """
    url = f"https://api.weixin.qq.com/shop/funds/scanorderflow?access_token={access_token}"
    headers = {"Content-Type": "application/json"}
    payload = {
        "biz_type": biz_type,
        "mchid": mchid,
        "page_num": page_num,
        "page_size": page_size,
        "date": date,
    }
    response = requests.post(url, headers=headers, data=json.dumps(payload))
    if response.status_code == 200:
        return response.json()  # 返回响应的JSON对象
    else:
        return {"error": f"请求失败，状态码：{response.status_code}"}


def get_flow_detail(access_token, funds_flow_id):
    # 调用方法示例
    # funds_flow_id = "1651031426690650"  # 替换成实际的流水单号
    access_token = get_access_token()
    biz_type = 1
    mchid = "1570685091"  # 替换成你的实际商户号

    result = get_order_flow(access_token, biz_type, mchid, funds_flow_id)
    print(result)


def get_flow_list(access_token, page_num=1, page_size=100, date="20240506"):
    # 调用方法示例
    biz_type = 1
    mchid = "1570685091"  # 替换成你的实际商户号

    result = scan_order_flow(access_token, biz_type, mchid, page_num, page_size, date)
    print(result)