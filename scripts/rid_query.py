import requests
import sys
import os
import json


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)

from le_util import get_stable_token

def get_request_info_by_rid(access_token, rid):
    """
    根据rid查询请求详情
    :param access_token: 微信API接口调用凭证
    :param rid: 调用接口报错返回的rid
    :return: 请求详情或错误信息
    """
    url = (
        f"https://api.weixin.qq.com/cgi-bin/openapi/rid/get?access_token={access_token}"
    )
    headers = {"Content-Type": "application/json"}
    payload = {"rid": rid}
    response = requests.post(url, headers=headers, data=json.dumps(payload))

    if response.status_code == 200:
        data = response.json()
        if data.get("errcode") == 0:
            return data  # 存在请求详情时返回请求详情
        else:
            return f"Error: {data.get('errmsg', 'Unknown error')}"  # 返回错误信息
    else:
        return f"HTTP Error: {response.status_code}"  # 网络错误等情况


if __name__ == "__main__":
    # 示例调用
    # access_token = "你的ACCESS_TOKEN"  # 将ACCESS_TOKEN替换为你的实际access_token
    access_token, expire = get_stable_token()
    rid = "61725984-6126f6f9-040f19c4"  # 示例rid，替换为实际的rid

    # 从args里获取rid
    rid = sys.argv[1]

    result = get_request_info_by_rid(access_token, rid)
    print(result)
