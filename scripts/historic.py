import csv
import datetime
import orjson as json
import logging
import os
from decimal import Decimal
from enum import IntEnum

CABLE_APP_APPLICANT = "1636528171API"

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额
# https://pay.weixin.qq.com/index.php/xphp/cfund_bill_nc/funds_bill_nc#/
# 微信支付-交易中心-资金账单

# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    CD2_PID,
    CD2_SKU_UNIQUE,
    SKU_DEFAULT,
    AfterSales,
    AfterSalesItem,
    Cart,
    Order,
    Product,
    StockRecord,
    TmpStock,
    TmpStockSummary,
    download_bill,
    filter_special,
    format_date,
    get_cart_key,
    get_cart_num,
    get_certain_cart_sku_num,
    get_certain_cart_sku_refund_num,
    get_session,
    read_csv,
    read_json,
    read_wechat_pay_log,
    write_csv,
)

# 创建一个 Session 实例
session = get_session()


def fix_daily_stock_data(date, start_stock):
    """修复每日库存数据，补充order_id"""
    print("date", date)
    pid = CD2_PID
    sku_unique = CD2_SKU_UNIQUE

    rows = read_csv(REPORT_ROOT + f"/流水数据{date}.csv")

    out_map = {}
    in_map = {}
    succ_order_count = 0
    refund_order_count = 0
    for r in rows:
        name = r[5]
        num = int(r[7])
        order_id = r[8].replace("`", "")
        kind = r[3]
        if name != "CD-《冀西南林路行》":
            # print("跳过", r)
            continue
        if kind == "货款收入":
            # print(r)
            out_map[order_id] = num
            succ_order_count += 1
        elif kind == "退款支出":
            # print(r)
            refund_order_count += 1
            in_map[order_id] = num

    stock_out = 0
    stock_in = 0
    skip_set = set()
    for r in rows:
        name = r[5]
        num = int(r[7])
        order_id = r[8].replace("`", "")
        kind = r[3]
        if name != "CD-《冀西南林路行》":
            continue
        if out_map.get(order_id) and in_map.get(order_id):
            if out_map[order_id] == in_map[order_id]:
                # print("当日本订单，退货数量等于出货数量，跳过出入库", order_id)
                skip_set.add(order_id)
                continue
            else:
                print("当日本订单，付款、退款库存出入数量不等", order_id)
                raise Exception("当日本订单，付款、退款库存出入数量不等")
        if kind == "货款收入":
            # print(r)
            stock_out += num
        elif kind == "退款支出":
            # print(r)
            stock_in += num

    print("下单后退款的订单数量:", len(skip_set))
    # 查询当日临时库存的出入库记录
    records = query_tmp_stock(date, pid, sku_unique, verbose=False)
    tmp_in = sum([r.total_stock_in for r in records])
    tmp_out = sum([r.total_stock_out for r in records])
    print("临时库存出库数量", tmp_out)
    print("临时库存入库数量", tmp_in)
    print("库存显示净出库量", tmp_out - tmp_in)

    print("*" * 80)

    print("出库数量", stock_out)
    print("入库数量", stock_in)

    print("净出库量", stock_out - stock_in)
    print("期初库存", start_stock)
    print("期末库存", start_stock - stock_out + stock_in)
    print("*" * 80)

    print("*" * 80)
    print("临时库存比对-- 取消、关闭的订单支付商品数量", tmp_out - stock_out)
    print("临时库存比对-- 取消、关闭的订单商品数量", tmp_in - stock_in)
    # !!!0506当日回滚时候多入库了9件商品
    # 查询当日取消、关闭的订单数量
    next_day = (
        datetime.datetime.strptime(date, "%Y-%m-%d") + datetime.timedelta(days=1)
    ).strftime("%Y-%m-%d")
    os = Order.query_range(session, date, next_day)
    cancel_close_os = [o for o in os if o.cancel_status not in (0,) and o.is_del == 0]

    print("*" * 80)
    print("付款订单数", succ_order_count)
    print("退款订单数", refund_order_count)
    print("订单比对-- 取消、关闭的订单数量", len(cancel_close_os))
    cancel_close_sku_count = 0
    for o in cancel_close_os:
        items = Cart.query(session, o.cart_id, o.order_id)
        num = sum([get_certain_cart_sku_num(c, pid, verbose=False) for c in items])
        cancel_close_sku_count += num
    print("订单比对-- 取消、关闭的商品数量", cancel_close_sku_count)

    delete_close_os = [o for o in os if o.is_del in (1,)]
    print("订单比对-- 删除的订单数量", len(delete_close_os))
    delete_close_sku_count = 0
    for o in delete_close_os:
        items = Cart.query(session, o.cart_id, o.order_id)
        num = sum([get_certain_cart_sku_num(c, pid, verbose=False) for c in items])
        delete_close_sku_count += num
    print("订单比对-- 删除的商品数量", delete_close_sku_count)
    print("*" * 80)

    print("=" * 80)
    print("=" * 80)
    print("=" * 80)


def stat(start, end, verbose=False):
    """统计已生成的流水
    start: 开始时间
    end: 结束时间
    示例： stat('2021-05-01', '2021-05-31')
    """
    # 每日10点更新前一天，11日9:00 获取9日，11日10:00 获取10日
    # 检查start end是否合法
    if not start or not end:
        print("stat 请输入开始结束时间")
        return False

    now = datetime.datetime.now()
    now_10 = now.replace(hour=10, minute=0, second=0, microsecond=0)
    # 前两天
    cur_valid = now - datetime.timedelta(days=1)
    if now >= now_10:
        cur_valid = now - datetime.timedelta(days=0)

    if start <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("start 非法")
        return False
    if end <= cur_valid.strftime("%Y-%m-%d"):
        pass
    else:
        print("end 非法:", end, "valid:", cur_valid.strftime("%Y-%m-%d"))
        return False

    start_date = datetime.datetime.strptime(start, "%Y-%m-%d")
    end_date = datetime.datetime.strptime(end, "%Y-%m-%d")
    cur_date = start_date
    # 商品id-sku组成的键，值为净销售数量
    total_sale_map = {}
    total_refund_map = {}
    while cur_date < end_date:
        s = cur_date.strftime("%Y-%m-%d")
        e = (cur_date + datetime.timedelta(days=1)).strftime("%Y-%m-%d")
        # 微信支付账单2024-05-06.csv
        cur_date_str = cur_date.strftime("%Y-%m-%d")
        bill_csv_path = WECHAT_RAW_DATA_ROOT + f"/微信支付账单{cur_date_str}.csv"
        sale_map, refund_map = cal(cur_date_str, bill_csv_path)
        for key in sale_map:
            tmp = total_sale_map.get(key, 0)
            tmp += sale_map[key]
            total_sale_map[key] = tmp
        for key in refund_map:
            tmp = total_refund_map.get(key, 0)
            tmp += refund_map[key]
            total_refund_map[key] = tmp
        cur_date = cur_date + datetime.timedelta(days=1)

    print("=" * 80)
    print("=" * 80)
    print("=" * 80)
    print("总计")
    key_set = set()
    for key in total_sale_map:
        key_set.add(key)
    for key in total_refund_map:
        key_set.add(key)
    for key in sorted(key_set):
        total_sale_num = total_sale_map.get(key, 0)
        total_refund_num = total_refund_map.get(key, 0)
        pid, sku = key.split("--")
        p = Product.get(session, pid)
        name = "%s - %s" % (p.name, sku)
        print(
            f"{name} 售出数量: ",
            total_sale_num,
            "商品退款数量: ",
            total_refund_num,
            "净售出:",
            total_sale_num - total_refund_num,
        )
    print("=" * 80)
    print("=" * 80)
    print("=" * 80)


def cal(day, f_path):
    rows = read_wechat_pay_log(f_path, verbose=False)
    # 净卖出商品数量
    total_sale_map = {}
    total_refund_map = {}
    if not rows:
        return total_sale_map, total_refund_map

    key_set = set()
    for idx, row in enumerate(rows):
        # 输出处理进度 500个一次
        # if idx % 500 == 0:
        #     print(f_path, "处理进度: ", idx, "/", len(rows))
        order_id = row[6]
        # 交易类型
        trade_status = row[9]
        order = Order.query_by_trade_no(session, order_id)
        if not order:
            print("订单不存在: ", order_id)
            continue

        cart_ids = order.cart_id
        items = Cart.query(session, cart_ids, order_id)
        order.items = items
        for cart in items:
            key = get_cart_key(cart)
            key_set.add(key)

        # 2024-05-06 15:19:13
        # trade 时间
        trade_time = row[0]
        # 转为 datetime
        trade_time = datetime.datetime.strptime(trade_time, "%Y-%m-%d %H:%M:%S")

        if trade_status == "SUCCESS":
            succ_map = count_row(order, verbose=True)
            for key in succ_map:
                tmp = total_sale_map.get(key, 0)
                tmp += succ_map[key]
                total_sale_map[key] = tmp
        elif trade_status == "REFUND":
            refund_map = count_row(order, True, verbose=True)
            for key in refund_map:
                tmp = total_refund_map.get(key, 0)
                tmp += refund_map[key]
                total_refund_map[key] = tmp
        else:
            print("未知交易状态", trade_status)

    for key in sorted(key_set):
        total_sale_num = total_sale_map.get(key, 0)
        total_refund_num = total_refund_map.get(key, 0)
        if total_sale_num - total_refund_num == 0:
            # 当日无变动
            continue
        pid, sku = key.split("--")
        p = Product.get(session, pid)
        name = "%s - %s" % (p.name, sku)
        print(
            f"{day} {name} 售出数量: ",
            total_sale_num,
            "商品退款数量: ",
            total_refund_num,
            "净售出:",
            total_sale_num - total_refund_num,
        )
    print("*" * 80)
    return total_sale_map, total_refund_map


def count_row(order, is_refund=False, verbose=False):
    items = order.items

    is_all_after_sales = False
    if is_refund:
        if len(items) == 1:
            is_all_after_sales = True
        else:
            is_after_sales_count = sum([cart.is_after_sales for cart in items])
            if is_after_sales_count == 0:
                is_all_after_sales = True

    count_map = {}
    for cart in items:
        key = get_cart_key(cart)
        product_id, sku = key.split("--")
        if is_refund:
            if is_all_after_sales:
                p_num = get_certain_cart_sku_num(
                    cart, product_id, sku=sku, verbose=verbose
                )
                cur_num = count_map.get(key, 0)
                cur_num += p_num
                count_map[key] = cur_num
            elif cart.is_after_sales:
                p_num = get_certain_cart_sku_num(
                    cart, product_id, sku=sku, verbose=verbose
                )
                cur_num = count_map.get(key, 0)
                cur_num += p_num
                count_map[key] = cur_num
        else:
            p_num = get_certain_cart_sku_num(cart, product_id, sku=sku, verbose=verbose)
            cur_num = count_map.get(key, 0)
            cur_num += p_num
            count_map[key] = cur_num
    return count_map


if __name__ == "__main__":
    # 命令行参数，操作类型download_bill,gen_report; 日期范围start,end;verbose
    from optparse import OptionParser

    parser = OptionParser(usage="""微信账单和流水生成""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    parser.add_option(
        "-s", "--start", dest="start", help="start date", default="2024-04-01"
    )
    # parser.add_option("-e", "--end", dest="end", help="end date", default="2024-05-01")
    parser.add_option("-e", "--end", dest="end", help="end date", default=None)
    parser.add_option("-o", "--operation", dest="operation", help="operation")
    parser.add_option("-t", "--trade_no", dest="trade_no", help="商户交易号")
    parser.add_option(
        "-a",
        "--accuracy_start_balance",
        dest="accuracy_start_balance",
        help="对账日期初余额",
    )
    parser.add_option("-p", "--pid", dest="pid", help="商品ID", default=CD2_PID)
    parser.add_option("-k", "--sku", dest="sku", help="SKU", default=SKU_DEFAULT)
    options, args = parser.parse_args()
    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    end = options.end
    if not end:
        end = options.start

    print(f"start: {options.start}, end: {end}")

    if options.operation == "gen_daily_stock_data":
        fix_daily_stock_data("2024-05-06", 13278)
        fix_daily_stock_data("2024-05-07", 8278)
        fix_daily_stock_data("2024-05-08", 8278)
        fix_daily_stock_data("2024-05-15", 7799 + 479)
        fix_daily_stock_data("2024-05-21", 5799 + 479)
        fix_daily_stock_data("2024-05-22", 5799)
        # gen_daily_stock_data(options.start, end, options.verbose)

    session.close()
