import os

# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)

from le_util import AfterSales, AfterSalesItem, get_session, read_csv


# stock_file_path = "/Users/<USER>/Downloads/stock.csv"
# funds_file_path = "/Users/<USER>/Downloads/funds.csv"
stock_file_path = "/data/workspace/backend/scripts/stock.csv"
funds_file_path = "/data/workspace/backend/scripts/funds.csv"

rows = read_csv(funds_file_path)
funds_in = [r for r in rows if r[3] in ("货款收入",)]
funds_out = [r for r in rows if r[3] in ("退款支出",)]

rows = read_csv(stock_file_path)
stock_out = [r for r in rows if r[3] == "出库"]
stock_in = [r for r in rows if r[3] == "入库"]


for f in funds_in:
    num = int(f[7])
    stock_list = [s for s in stock_out if s[5] == f[8]]
    if not stock_list:
        print("货款收入没有对应的出库记录", f[8], num, f[0])
        continue
    stock = stock_list[0]
    stock_num = abs(int(stock[4]))
    if num != stock_num:
        print("货款收入数量和出库数量不一致", f[8], num, stock_num)


for f in funds_out:
    num = int(f[7])
    stock_list = [s for s in stock_in if s[5] == f[8]]
    if not stock_list:
        oid = f[8].replace("`", "")
        session = get_session()
        after_sales = AfterSales.get_refunded(session, oid)
        items = AfterSalesItem.query(session, after_sales.id)
        print("退款支出没有对应的入库记录", f[8], num, f[0])
        for item in items:
            print("refund:", item.product_id, item.sku_unique, item.num)
        continue
    stock = stock_list[0]
    stock_num = abs(int(stock[4]))
    if num != stock_num:
        print("退款支出数量和入库数量不一致", f[8], num, stock_num)
