
# Re-try preparing the phone numbers and creating a CSV formatted string
phone_numbers = """
18822049022
"""

# Remove leading and trailing whitespaces and split by lines
phone_numbers = phone_numbers.strip().split("\n")

# Create a CSV formatted string
csv_content = "手机号,备注\n" + "\n".join([f"{num},90" for num in phone_numbers])

# Save the CSV content to a file
csv_file_path = 'blacklist.csv'
with open(csv_file_path, 'w') as f:
    f.write(csv_content)

csv_file_path
