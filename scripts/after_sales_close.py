import csv
import datetime
import json
import logging
import os

# 脚本用途
# 读取微信支付订单汇总表，输出截止时间的订单数量和金额


# 根目录在上一级
PROJ_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
# 注入path
cur_path = os.path.abspath(__file__)
if cur_path not in os.sys.path:
    os.sys.path.append(cur_path)


from le_util import (
    AfterSales,
    AfterSalesStatus,
    Order,
    get_session,
    User,
)


def check(dry_run=True):
    print("dry_run: ", dry_run)
    session = get_session(read_only=dry_run)

    state_user_cancel = 1
    state_normal = 0

    state = state_normal
    # state = state_user_cancel

    sales_list = AfterSales.get_wait_send_back_sales(session, state)
    for sales in sales_list:
        sts = AfterSalesStatus.get_by_sales_and_change_type(session, sales.id, 1)
        if sts:
            print(sales.id, "order:", sales.order_id, sts.change_type, sts.change_time)

            end_time = sts.change_time + datetime.timedelta(days=7)
            if end_time < datetime.datetime.now():
                print(f"超时关闭: after_sales_id: {sales.id} order: {sales.order_id} change_type: {sts.change_type} change_time: {sts.change_time}")
                if not dry_run:
                    sales.sales_state = 3
                    session.commit()
        else:
            print(f"没有售后状态: after_sales_id: {sales.id} order: {sales.order_id}")

    session.close()


if __name__ == "__main__":
    from optparse import OptionParser, OptionGroup

    parser = OptionParser(usage="""关闭售后 --no-dry-run""")
    parser.add_option("-v", "--verbose", action="store_true", help="verbose")
    parser.add_option("-q", "--quiet", action="store_true", help="quiet")

    dry_run_group = OptionGroup(parser, "Dry Run Options")
    dry_run_group.add_option(
        "-d",
        "--dry-run",
        action="store_true",
        dest="dry_run",
        default=True,
        help="dry run",
    )
    dry_run_group.add_option(
        "--no-dry-run",
        action="store_false",
        dest="dry_run",
        help="do not perform a dry run",
    )
    parser.add_option_group(dry_run_group)

    options, args = parser.parse_args()
    print(f"Dry run: {options.dry_run}")

    logging.basicConfig(
        level=options.quiet
        and logging.WARNING
        or options.verbose
        and logging.DEBUG
        or logging.INFO
    )

    check(dry_run=options.dry_run)
